2025-07-01 19:25:00,713 - root - INFO - 🚀 日志系统已启动，输出到: demo_output.txt
2025-07-01 19:25:00,713 - root - INFO - 处理内置问题...
2025-07-01 19:25:00,713 - root - INFO - 处理 3 个内置问题...
2025-07-01 19:25:00,713 - src.reasoning_engine.processors.mlr_processor.MLRProcessor - INFO - MLR处理器初始化完成
2025-07-01 19:25:00,713 - src.reasoning_engine.strategies.mlr_strategy.MLRMultiLayerReasoner - INFO - MLR多层推理器初始化完成
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - INFO - 最终覆盖：完整输入处理已强制设置为 True
2025-07-01 19:25:00,713 - root - INFO - 
================================================================================
2025-07-01 19:25:00,713 - root - INFO - 🎯 COT-DIR处理问题 1/3
2025-07-01 19:25:00,713 - root - INFO - ================================================================================
2025-07-01 19:25:00,713 - root - INFO - 📝 输入问题: Chenny is 10 years old. Alyana is 4 years younger than Chenny. How old is Anne if she is 2 years older than Alyana?
2025-07-01 19:25:00,713 - root - INFO - ⏰ 开始时间: 2025-07-01 19:25:00
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - INFO - 执行基础的实体提取...
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - DEBUG - 输入的问题文本: 'Chenny is 10 years old. Alyana is 4 years younger than Chenny. How old is Anne if she is 2 years older than Alyana?'
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - DEBUG - 使用的数字正则表达式: '\b\d+\.?\d*\b'
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - DEBUG - 找到 3 个数字匹配: ['10', '4', '2']
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - DEBUG - 使用的名称正则表达式: '\b[A-Z][a-z]*\b'
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - DEBUG - 找到 6 个名称匹配: ['Chenny', 'Alyana', 'Chenny', 'How', 'Anne', 'Alyana']
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - INFO - 提取到 8 个实体: ['10', '4', '2', 'Chenny', 'Alyana', 'Chenny', 'Anne', 'Alyana']
2025-07-01 19:25:00,713 - root - INFO - 更新关系模式库，发现0个关系
2025-07-01 19:25:00,713 - root - INFO - 
📍 步骤 2: 实体发现 (Entity Discovery)
2025-07-01 19:25:00,713 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,713 - root - INFO -    未发现实体。
2025-07-01 19:25:00,713 - root - INFO - 
📍 步骤 3: 隐式关系发现 (IRD)
2025-07-01 19:25:00,713 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,713 - root - INFO -    未发现关系。
2025-07-01 19:25:00,713 - root - INFO - 
📍 步骤 4: 多层推理 (MLR)
2025-07-01 19:25:00,713 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,713 - root - INFO -    无推理步骤。
2025-07-01 19:25:00,713 - root - INFO - 
📍 步骤 5: 置信度验证 (CV)
2025-07-01 19:25:00,713 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,713 - root - INFO - 
📊 综合置信度: 0.00
2025-07-01 19:25:00,713 - root - INFO - 
📍 步骤 6: 最终结果生成
2025-07-01 19:25:00,713 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,713 - root - INFO -    🎯 最终答案: 处理失败
2025-07-01 19:25:00,713 - root - INFO - 
================================================================================
2025-07-01 19:25:00,713 - root - INFO - 🎯 COT-DIR处理问题 2/3
2025-07-01 19:25:00,713 - root - INFO - ================================================================================
2025-07-01 19:25:00,713 - root - INFO - 📝 输入问题: Marty has 100 centimeters of ribbon that he must cut into 4 equal parts. Each of the cut parts must be divided into 5 equal parts. How long will each final cut be?
2025-07-01 19:25:00,713 - root - INFO - ⏰ 开始时间: 2025-07-01 19:25:00
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - INFO - 执行基础的实体提取...
2025-07-01 19:25:00,713 - src.reasoning_engine.cotdir_integration - DEBUG - 输入的问题文本: 'Marty has 100 centimeters of ribbon that he must cut into 4 equal parts. Each of the cut parts must be divided into 5 equal parts. How long will each final cut be?'
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 使用的数字正则表达式: '\b\d+\.?\d*\b'
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 找到 3 个数字匹配: ['100', '4', '5']
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 使用的名称正则表达式: '\b[A-Z][a-z]*\b'
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 找到 3 个名称匹配: ['Marty', 'Each', 'How']
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - INFO - 提取到 5 个实体: ['100', '4', '5', 'Marty', 'Each']
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 2: 实体发现 (Entity Discovery)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    未发现实体。
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 3: 隐式关系发现 (IRD)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    未发现关系。
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 4: 多层推理 (MLR)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    无推理步骤。
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 5: 置信度验证 (CV)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO - 
📊 综合置信度: 0.00
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 6: 最终结果生成
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    🎯 最终答案: 处理失败
2025-07-01 19:25:00,714 - root - INFO - 
================================================================================
2025-07-01 19:25:00,714 - root - INFO - 🎯 COT-DIR处理问题 3/3
2025-07-01 19:25:00,714 - root - INFO - ================================================================================
2025-07-01 19:25:00,714 - root - INFO - 📝 输入问题: Lillian's garden doesn't have any bird feeders in it so she wants to add some. She builds 3 and buys 3 others. Each bird feeder seems to attract 20 birds throughout the day until Lillian notices that the birds seem to prefer the feeders she made herself which attract 10 more birds each than the store-bought ones. How many birds can Lillian expect to see in her garden each day if the same amount keep coming to her bird feeders?
2025-07-01 19:25:00,714 - root - INFO - ⏰ 开始时间: 2025-07-01 19:25:00
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - INFO - 执行基础的实体提取...
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 输入的问题文本: 'Lillian's garden doesn't have any bird feeders in it so she wants to add some. She builds 3 and buys 3 others. Each bird feeder seems to attract 20 birds throughout the day until Lillian notices that the birds seem to prefer the feeders she made herself which attract 10 more birds each than the store-bought ones. How many birds can Lillian expect to see in her garden each day if the same amount keep coming to her bird feeders?'
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 使用的数字正则表达式: '\b\d+\.?\d*\b'
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 找到 4 个数字匹配: ['3', '3', '20', '10']
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 使用的名称正则表达式: '\b[A-Z][a-z]*\b'
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - DEBUG - 找到 6 个名称匹配: ['Lillian', 'She', 'Each', 'Lillian', 'How', 'Lillian']
2025-07-01 19:25:00,714 - src.reasoning_engine.cotdir_integration - INFO - 提取到 9 个实体: ['3', '3', '20', '10', 'Lillian', 'She', 'Each', 'Lillian', 'Lillian']
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 2: 实体发现 (Entity Discovery)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    未发现实体。
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 3: 隐式关系发现 (IRD)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    未发现关系。
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 4: 多层推理 (MLR)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    无推理步骤。
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 5: 置信度验证 (CV)
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO - 
📊 综合置信度: 0.00
2025-07-01 19:25:00,714 - root - INFO - 
📍 步骤 6: 最终结果生成
2025-07-01 19:25:00,714 - root - INFO - ────────────────────────────────────────────────────────────
2025-07-01 19:25:00,714 - root - INFO -    🎯 最终答案: 处理失败
2025-07-01 19:25:00,714 - root - INFO - 
🎉 演示完成！
