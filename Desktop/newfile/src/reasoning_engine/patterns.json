{"patterns": [{"name": "assignment", "type": "assignment", "pattern": "(\\w+) is (\\d+) years old", "template": "{arg1} = {arg2}"}, {"name": "direct_value_assignment", "type": "assignment", "pattern": "(\\w+) had (\\d+)", "template": "{arg1} = {arg2}"}, {"name": "direct_quantity_assignment", "type": "assignment", "pattern": "There (?:are|were) (\\d+) (\\w+)", "template": "{arg2} = {arg1}"}, {"name": "addition", "type": "binary_operation", "pattern": "(\\w+)'s age is (\\d+) more than (\\w+)", "template": "{arg1} = {arg3} + {arg2}"}, {"name": "subtraction", "type": "binary_operation", "pattern": "(\\w+) is (\\d+) years younger than (\\w+)", "template": "{arg1} = {arg3} - {arg2}"}, {"name": "more_than_relation", "type": "binary_operation", "pattern": "(\\w+) (?:did|had) (\\d+) more \\w+ than (\\w+)", "template": "{arg1} = {arg3} + {arg2}"}, {"name": "less_than_relation", "type": "binary_operation", "pattern": "(\\w+) (?:did|had) (\\d+) (?:less|fewer) \\w+ than (\\w+)", "template": "{arg1} = {arg3} - {arg2}"}, {"name": "multiplication_times", "type": "binary_operation", "pattern": "(\\w+) has (\\d+) times as many as (\\w+)", "template": "{arg1} = {arg3} * {arg2}"}, {"name": "simple_addition", "type": "calculation", "pattern": "How many \\w+ (?:did \\w+ \\w+|were there) in (?:all|total)", "template": "sum_all_entities"}, {"name": "difference_calculation", "type": "calculation", "pattern": "How many more \\w+ (?:than|did) (\\w+)", "template": "max_entity - min_entity"}, {"name": "remaining_after_loss", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) \\w+ left", "template": "{arg1}_remaining = {arg2}"}, {"name": "complex_initial_state", "type": "multi_step", "pattern": "(\\w+) had (\\d+) \\w+ initially", "template": "{arg1}_initial = {arg2}", "priority": "high"}, {"name": "final_state_given", "type": "multi_step", "pattern": "(\\w+) has (\\d+) \\w+ now", "template": "{arg1}_final = {arg2}", "priority": "high"}, {"name": "change_operation", "type": "multi_step", "pattern": "(\\w+) (?:gained|lost|added|removed) (\\d+) \\w+", "template": "{arg1}_change = {arg2}", "priority": "high"}, {"name": "spending_calculation", "type": "calculation", "pattern": "How much did \\w+ spend", "template": "initial_money - remaining_money", "priority": "medium"}, {"name": "earning_calculation", "type": "calculation", "pattern": "How much money did \\w+ make", "template": "final_money - initial_money", "priority": "medium"}, {"name": "multiplication_operation", "type": "binary_operation", "pattern": "(\\w+) (?:sold|made|has) (\\d+) times (?:as many|more) \\w+", "template": "{arg1} = {arg2} * base_value", "priority": "medium"}, {"name": "per_unit_calculation", "type": "calculation", "pattern": "How many \\w+ (?:per|each) \\w+", "template": "total_quantity / number_of_units", "priority": "medium"}, {"name": "binary_operation_had_more_+", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) more than (\\w+)", "template": "{arg1} = {arg3} + {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_had_more_-", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) more than (\\w+)", "template": "{arg1} = {arg3} - {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_had_less_+", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) less than (\\w+)", "template": "{arg1} = {arg3} + {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_had_less_-", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) less than (\\w+)", "template": "{arg1} = {arg3} - {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_had_fewer_+", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) fewer than (\\w+)", "template": "{arg1} = {arg3} + {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_had_fewer_-", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) fewer than (\\w+)", "template": "{arg1} = {arg3} - {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_did_more_+", "type": "binary_operation", "pattern": "(\\w+) did (\\d+) more than (\\w+)", "template": "{arg1} = {arg3} + {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_did_more_-", "type": "binary_operation", "pattern": "(\\w+) did (\\d+) more than (\\w+)", "template": "{arg1} = {arg3} - {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_did_less_+", "type": "binary_operation", "pattern": "(\\w+) did (\\d+) less than (\\w+)", "template": "{arg1} = {arg3} + {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_did_less_-", "type": "binary_operation", "pattern": "(\\w+) did (\\d+) less than (\\w+)", "template": "{arg1} = {arg3} - {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_did_fewer_+", "type": "binary_operation", "pattern": "(\\w+) did (\\d+) fewer than (\\w+)", "template": "{arg1} = {arg3} + {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "binary_operation_did_fewer_-", "type": "binary_operation", "pattern": "(\\w+) did (\\d+) fewer than (\\w+)", "template": "{arg1} = {arg3} - {arg2}", "description": "二元运算（加减乘除等）"}, {"name": "multiplication_operation", "type": "multiplication_operation", "pattern": "(\\w+) has (\\d+) times as many as (\\w+)", "template": "{arg1} = {arg3} * {arg2}", "description": "乘法/倍数关系"}, {"name": "state_transition_marbles", "type": "state_transition", "pattern": "(\\w+) had (\\d+) marbles (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_roses", "type": "state_transition", "pattern": "(\\w+) had (\\d+) roses (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_dollars", "type": "state_transition", "pattern": "(\\w+) had (\\d+) dollars (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_crackers", "type": "state_transition", "pattern": "(\\w+) had (\\d+) crackers (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_trees", "type": "state_transition", "pattern": "(\\w+) had (\\d+) trees (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_customers", "type": "state_transition", "pattern": "(\\w+) had (\\d+) customers (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_points", "type": "state_transition", "pattern": "(\\w+) had (\\d+) points (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "state_transition_games", "type": "state_transition", "pattern": "(\\w+) had (\\d+) games (initially|originally)", "template": "{arg1}_initial = {arg2}", "description": "状态转移（初始-变化-结果）"}, {"name": "distribution_operation_crackers_friend", "type": "distribution_operation", "pattern": "How many crackers did each friend", "template": "total / number_of_friend", "description": "平均分配/分组"}, {"name": "distribution_operation_crackers_child", "type": "distribution_operation", "pattern": "How many crackers did each child", "template": "total / number_of_child", "description": "平均分配/分组"}, {"name": "distribution_operation_crackers_group", "type": "distribution_operation", "pattern": "How many crackers did each group", "template": "total / number_of_group", "description": "平均分配/分组"}, {"name": "distribution_operation_crackers_person", "type": "distribution_operation", "pattern": "How many crackers did each person", "template": "total / number_of_person", "description": "平均分配/分组"}, {"name": "distribution_operation_roses_friend", "type": "distribution_operation", "pattern": "How many roses did each friend", "template": "total / number_of_friend", "description": "平均分配/分组"}, {"name": "distribution_operation_roses_child", "type": "distribution_operation", "pattern": "How many roses did each child", "template": "total / number_of_child", "description": "平均分配/分组"}, {"name": "distribution_operation_roses_group", "type": "distribution_operation", "pattern": "How many roses did each group", "template": "total / number_of_group", "description": "平均分配/分组"}, {"name": "distribution_operation_roses_person", "type": "distribution_operation", "pattern": "How many roses did each person", "template": "total / number_of_person", "description": "平均分配/分组"}, {"name": "distribution_operation_marbles_friend", "type": "distribution_operation", "pattern": "How many marbles did each friend", "template": "total / number_of_friend", "description": "平均分配/分组"}, {"name": "distribution_operation_marbles_child", "type": "distribution_operation", "pattern": "How many marbles did each child", "template": "total / number_of_child", "description": "平均分配/分组"}, {"name": "distribution_operation_marbles_group", "type": "distribution_operation", "pattern": "How many marbles did each group", "template": "total / number_of_group", "description": "平均分配/分组"}, {"name": "distribution_operation_marbles_person", "type": "distribution_operation", "pattern": "How many marbles did each person", "template": "total / number_of_person", "description": "平均分配/分组"}, {"name": "distribution_operation_points_friend", "type": "distribution_operation", "pattern": "How many points did each friend", "template": "total / number_of_friend", "description": "平均分配/分组"}, {"name": "distribution_operation_points_child", "type": "distribution_operation", "pattern": "How many points did each child", "template": "total / number_of_child", "description": "平均分配/分组"}, {"name": "distribution_operation_points_group", "type": "distribution_operation", "pattern": "How many points did each group", "template": "total / number_of_group", "description": "平均分配/分组"}, {"name": "distribution_operation_points_person", "type": "distribution_operation", "pattern": "How many points did each person", "template": "total / number_of_person", "description": "平均分配/分组"}, {"name": "distribution_operation_candies_friend", "type": "distribution_operation", "pattern": "How many candies did each friend", "template": "total / number_of_friend", "description": "平均分配/分组"}, {"name": "distribution_operation_candies_child", "type": "distribution_operation", "pattern": "How many candies did each child", "template": "total / number_of_child", "description": "平均分配/分组"}, {"name": "distribution_operation_candies_group", "type": "distribution_operation", "pattern": "How many candies did each group", "template": "total / number_of_group", "description": "平均分配/分组"}, {"name": "distribution_operation_candies_person", "type": "distribution_operation", "pattern": "How many candies did each person", "template": "total / number_of_person", "description": "平均分配/分组"}, {"name": "sum_operation_roses", "type": "sum_operation", "pattern": "How many roses (?:in all|in total|altogether)", "template": "sum_all_roses", "description": "总和/合计"}, {"name": "sum_operation_marbles", "type": "sum_operation", "pattern": "How many marbles (?:in all|in total|altogether)", "template": "sum_all_marbles", "description": "总和/合计"}, {"name": "sum_operation_dollars", "type": "sum_operation", "pattern": "How many dollars (?:in all|in total|altogether)", "template": "sum_all_dollars", "description": "总和/合计"}, {"name": "sum_operation_crackers", "type": "sum_operation", "pattern": "How many crackers (?:in all|in total|altogether)", "template": "sum_all_crackers", "description": "总和/合计"}, {"name": "sum_operation_trees", "type": "sum_operation", "pattern": "How many trees (?:in all|in total|altogether)", "template": "sum_all_trees", "description": "总和/合计"}, {"name": "sum_operation_customers", "type": "sum_operation", "pattern": "How many customers (?:in all|in total|altogether)", "template": "sum_all_customers", "description": "总和/合计"}, {"name": "sum_operation_points", "type": "sum_operation", "pattern": "How many points (?:in all|in total|altogether)", "template": "sum_all_points", "description": "总和/合计"}, {"name": "sum_operation_games", "type": "sum_operation", "pattern": "How many games (?:in all|in total|altogether)", "template": "sum_all_games", "description": "总和/合计"}]}