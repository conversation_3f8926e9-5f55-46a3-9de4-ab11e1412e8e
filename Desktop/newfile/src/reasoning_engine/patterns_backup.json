{"patterns": [{"name": "assignment", "type": "assignment", "pattern": "(\\w+) is (\\d+) years old", "template": "{arg1} = {arg2}"}, {"name": "direct_value_assignment", "type": "assignment", "pattern": "(\\w+) had (\\d+)", "template": "{arg1} = {arg2}"}, {"name": "direct_quantity_assignment", "type": "assignment", "pattern": "There (?:are|were) (\\d+) (\\w+)", "template": "{arg2} = {arg1}"}, {"name": "addition", "type": "binary_operation", "pattern": "(\\w+)'s age is (\\d+) more than (\\w+)", "template": "{arg1} = {arg3} + {arg2}"}, {"name": "subtraction", "type": "binary_operation", "pattern": "(\\w+) is (\\d+) years younger than (\\w+)", "template": "{arg1} = {arg3} - {arg2}"}, {"name": "more_than_relation", "type": "binary_operation", "pattern": "(\\w+) (?:did|had) (\\d+) more \\w+ than (\\w+)", "template": "{arg1} = {arg3} + {arg2}"}, {"name": "less_than_relation", "type": "binary_operation", "pattern": "(\\w+) (?:did|had) (\\d+) (?:less|fewer) \\w+ than (\\w+)", "template": "{arg1} = {arg3} - {arg2}"}, {"name": "multiplication_times", "type": "binary_operation", "pattern": "(\\w+) has (\\d+) times as many as (\\w+)", "template": "{arg1} = {arg3} * {arg2}"}, {"name": "simple_addition", "type": "calculation", "pattern": "How many \\w+ (?:did \\w+ \\w+|were there) in (?:all|total)", "template": "sum_all_entities"}, {"name": "difference_calculation", "type": "calculation", "pattern": "How many more \\w+ (?:than|did) (\\w+)", "template": "max_entity - min_entity"}, {"name": "remaining_after_loss", "type": "binary_operation", "pattern": "(\\w+) had (\\d+) \\w+ left", "template": "{arg1}_remaining = {arg2}"}]}