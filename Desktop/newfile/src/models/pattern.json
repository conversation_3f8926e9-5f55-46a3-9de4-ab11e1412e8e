{"pattern_groups": {"数量关系": {"description": "处理计量和容器等数量相关的模式", "基础计量": [{"id": "1", "pattern": ["n", "m", "q"], "relation_template": "a=b*c", "var_slot_val": "a,b,c", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "抽象乘法关系", "scene": "数量关系_基础计量", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "depends_on"}, {"source": "a", "target": "c", "relation": "depends_on"}], "reasoning_type": "direct", "composed_of": ["n", "m", "q"]}, {"id": "2", "pattern": ["v", "m", "q"], "relation_template": "a=b*c", "var_slot_val": "a,b,c", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "抽象乘法关系（动词主导）", "scene": "数量关系_基础计量", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "depends_on"}, {"source": "a", "target": "c", "relation": "depends_on"}], "reasoning_type": "direct", "composed_of": ["v", "m", "q"]}, {"id": "3", "pattern": ["n", "m", "q", "n"], "relation_template": "a=b*c/d", "var_slot_val": "a,b,c,d", "var_slot_index": {"a": "0", "b": "1", "c": "2", "d": "3"}, "description": "抽象除法关系", "scene": "数量关系_基础计量", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "depends_on"}, {"source": "a", "target": "c", "relation": "depends_on"}, {"source": "a", "target": "d", "relation": "inversely_depends_on"}], "reasoning_type": "direct", "composed_of": ["n", "m", "q", "n"]}, {"id": "4", "pattern": ["v", "m", "q"], "relation_template": "a=b*c", "var_slot_val": "v,m,q", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "提取目标水量，识别形如'rise to 9L'的表达", "scene": "数量关系_基础计量", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "depends_on"}, {"source": "a", "target": "c", "relation": "depends_on"}], "reasoning_type": "direct", "composed_of": ["v", "m", "q"]}, {"id": "work_coop_1", "pattern": ["n1", "v1", "m1", "n2", "v2", "m2"], "relation_template": "1/t = 1/t1 + 1/t2", "var_slot_val": "n1,v1,m1,n2,v2,m2", "var_slot_index": {"t": "0", "t1": "2", "t2": "5"}, "description": "两人合作完成工作，合成效率关系", "scene": "数量关系_合作效率", "dependencies": [], "semantic_dependencies": [{"source": "t", "target": "t1", "relation": "harmonically_depends_on"}, {"source": "t", "target": "t2", "relation": "harmonically_depends_on"}], "reasoning_type": "composite", "composed_of": ["n1", "v1", "m1", "n2", "v2", "m2"]}, {"id": "chase_1", "pattern": ["n1", "v1", "m1", "n2", "v2", "m2", "delta_t"], "relation_template": "d = v2 * t - v1 * (t - delta_t)", "var_slot_val": "n1,v1,m1,n2,v2,m2,delta_t", "var_slot_index": {"d": "0", "v1": "2", "v2": "5", "t": "6", "delta_t": "7"}, "description": "追及问题，后发先至", "scene": "数量关系_行程追及", "dependencies": [], "semantic_dependencies": [{"source": "d", "target": "v2", "relation": "directly_depends_on"}, {"source": "d", "target": "t", "relation": "directly_depends_on"}, {"source": "d", "target": "v1", "relation": "inversely_depends_on"}, {"source": "d", "target": "delta_t", "relation": "indirectly_depends_on"}], "reasoning_type": "multistep", "composed_of": ["n1", "v1", "m1", "n2", "v2", "m2", "delta_t"]}, {"id": "mixture_1", "pattern": ["n1", "m1", "q1", "n2", "m2", "q2"], "relation_template": "c = (m1*q1 + m2*q2)/(m1+m2)", "var_slot_val": "n1,m1,q1,n2,m2,q2", "var_slot_index": {"c": "0", "m1": "1", "q1": "2", "m2": "4", "q2": "5"}, "description": "混合液体浓度计算", "scene": "数量关系_流水混合", "dependencies": [], "semantic_dependencies": [{"source": "c", "target": "m1", "relation": "complex_depends_on"}, {"source": "c", "target": "q1", "relation": "complex_depends_on"}, {"source": "c", "target": "m2", "relation": "complex_depends_on"}, {"source": "c", "target": "q2", "relation": "complex_depends_on"}], "reasoning_type": "composite", "composed_of": ["n1", "m1", "q1", "n2", "m2", "q2"]}, {"id": "seq_rec_1", "pattern": ["n", "m", "q"], "relation_template": "a_n = a_{n-1} + d", "var_slot_val": "n,m,q", "var_slot_index": {"a_n": "0", "a_{n-1}": "1", "d": "2"}, "description": "等差数列递推关系", "scene": "数量关系_数列递推", "dependencies": ["seq_rec_1 (n-1)"], "semantic_dependencies": [{"source": "a_n", "target": "a_{n-1}", "relation": "recursively_depends_on"}, {"source": "a_n", "target": "d", "relation": "directly_depends_on"}], "reasoning_type": "recursive", "composed_of": ["n", "m", "q"]}, {"id": "composite_1", "pattern": ["work_coop_1", "mixture_1"], "relation_template": "final = work_result + mixture_result", "var_slot_val": "work_result,mixture_result", "var_slot_index": {"final": "0", "work_result": "1", "mixture_result": "2"}, "description": "复合题型，组合多个子模式结果", "scene": "数量关系_复合题型", "dependencies": ["work_coop_1", "mixture_1"], "composed_of": ["work_coop_1", "mixture_1"], "composition_type": "sequential", "semantic_dependencies": [{"source": "final", "target": "work_result", "relation": "compound_depends_on"}, {"source": "final", "target": "mixture_result", "relation": "compound_depends_on"}, {"source": "final", "target": "work_coop_1", "relation": "pattern_depends_on"}, {"source": "final", "target": "mixture_1", "relation": "pattern_depends_on"}], "reasoning_type": "compound"}, {"id": "compound_interest", "pattern": ["n", "m", "q", "rate", "years"], "relation_template": "A = P * (1 + r) ^ n", "var_slot_val": "A,P,r,n", "var_slot_index": {"A": "0", "P": "1", "r": "2", "n": "3"}, "description": "复利公式", "scene": "数量关系_复利", "dependencies": [], "semantic_dependencies": [{"source": "A", "target": "P", "relation": "depends_on"}, {"source": "A", "target": "r", "relation": "depends_on"}, {"source": "A", "target": "n", "relation": "depends_on"}], "reasoning_type": "compound_interest", "composed_of": ["n", "m", "q", "rate", "years"]}], "容器关系": [{"id": "15", "pattern": ["n", "里", "有", "n"], "relation_template": "a contains b", "var_slot_val": "n,n", "var_slot_index": {"a": "0", "b": "3"}, "description": "容器包含物体，例：书包里有书", "scene": "数量关系_容器关系", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "contains"}], "reasoning_type": "hierarchical", "composed_of": ["n", "里", "有", "n"]}, {"id": "16", "pattern": ["n", "中", "共有", "m", "q", "n"], "relation_template": "a has b c of d", "var_slot_val": "n,m,q,n", "var_slot_index": {"a": "0", "b": "3", "c": "4", "d": "5"}, "description": "容器中有若干物体，例：教室中共有40名学生", "scene": "数量关系_容器关系", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "contains_quantity"}, {"source": "b", "target": "c", "relation": "has_unit"}, {"source": "b", "target": "d", "relation": "type_of"}], "reasoning_type": "hierarchical", "composed_of": ["n", "中", "共有", "m", "q", "n"]}], "速率关系": [{"id": "5", "pattern": ["n->ATT->m", "n->ATT->n"], "relation_template": "a=b/c", "var_slot_val": "a,b,c", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "抽象速率关系", "scene": "数量关系_速率关系", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "directly_depends_on"}, {"source": "a", "target": "c", "relation": "inversely_depends_on"}], "reasoning_type": "direct", "composed_of": ["n->ATT->m", "n->ATT->n"]}, {"id": "6", "pattern": ["n->ATT->n", "v->SBV->q"], "relation_template": "a=b-c", "var_slot_val": "n,q,q", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "提取水量变化关系，识别形如'water level...rise to 9L'的复杂表达", "scene": "数量关系_速率关系", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "directly_depends_on"}, {"source": "a", "target": "c", "relation": "inversely_depends_on"}], "reasoning_type": "direct", "composed_of": ["n->ATT->n", "v->SBV->q"]}], "单位换算": [{"id": "14", "pattern": ["m", "q1", "prep", "q2"], "relation_template": "a=b*c", "var_slot_val": "m,q1,q2", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "识别形如'1000米等于多少公里'的问句", "scene": "数量关系_单位换算", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "unit_conversion"}, {"source": "a", "target": "c", "relation": "conversion_factor"}], "reasoning_type": "direct", "composed_of": ["m", "q1", "prep", "q2"]}]}, "动作关系": {"description": "描述动作和状态变化的模式", "变化类": [{"id": "17", "pattern": ["n", "到", "n", "的", "v", "n"], "relation_template": "distance(a,b)=c", "var_slot_val": "n,n,v,n", "var_slot_index": {"a": "0", "b": "2", "c": "4", "d": "5"}, "description": "对象a到对象b的动作距离，例：小明到教室的移动距离", "scene": "动作关系_变化类", "dependencies": [], "semantic_dependencies": [{"source": "distance", "target": "a", "relation": "origin_depends_on"}, {"source": "distance", "target": "b", "relation": "destination_depends_on"}, {"source": "distance", "target": "c", "relation": "action_depends_on"}], "reasoning_type": "spatial", "composed_of": ["n", "到", "n", "的", "v", "n"]}, {"id": "18", "pattern": ["n", "level", "to", "rise", "to", "m", "q"], "relation_template": "a rises to b*c", "var_slot_val": "n,m,q", "var_slot_index": {"a": "0", "b": "5", "c": "6"}, "description": "水平面变化，例：water level to rise to 9L", "scene": "动作关系_变化类", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "target_state"}, {"source": "a", "target": "c", "relation": "target_unit"}], "reasoning_type": "state_change", "composed_of": ["n", "level", "to", "rise", "to", "m", "q"]}, {"id": "19", "pattern": ["n", "are", "dropped", "into", "n"], "relation_template": "a is added to b", "var_slot_val": "n,n", "var_slot_index": {"a": "0", "b": "4"}, "description": "物体被加入容器，例：ice cubes are dropped into water", "scene": "动作关系_变化类", "dependencies": [], "semantic_dependencies": [{"source": "b", "target": "a", "relation": "receives"}], "reasoning_type": "state_change", "composed_of": ["n", "are", "dropped", "into", "n"]}], "流动类": [{"id": "20", "pattern": ["n", "is", "leaking", "from", "n", "through", "n"], "relation_template": "leak(a,b,c)", "var_slot_val": "n,n,n", "var_slot_index": {"a": "0", "b": "4", "c": "6"}, "description": "液体泄漏，例：water is leaking from tank through tube", "scene": "动作关系_流动类", "dependencies": [], "semantic_dependencies": [{"source": "leak", "target": "a", "relation": "substance"}, {"source": "leak", "target": "b", "relation": "source"}, {"source": "leak", "target": "c", "relation": "path"}], "reasoning_type": "flow_based", "composed_of": ["n", "is", "leaking", "from", "n", "through", "n"]}]}, "属性关系": {"description": "描述对象属性的模式", "基础属性": [{"id": "21", "pattern": ["n", "长", "num", "米"], "relation_template": "length(a)=b", "var_slot_val": "n,num", "var_slot_index": {"a": "0", "b": "2"}, "description": "对象长度，例：绳子长5米", "scene": "属性关系_基础属性", "dependencies": [], "semantic_dependencies": [{"source": "length", "target": "a", "relation": "property_of"}, {"source": "length", "target": "b", "relation": "value_is"}], "reasoning_type": "attribute", "composed_of": ["n", "长", "num", "米"]}], "时间属性": [{"id": "22", "pattern": ["n", "用时", "num", "分钟"], "relation_template": "time(a)=b", "var_slot_val": "n,num", "var_slot_index": {"a": "0", "b": "2"}, "description": "对象用时，例：小明用时30分钟", "scene": "属性关系_时间属性", "dependencies": [], "semantic_dependencies": [{"source": "time", "target": "a", "relation": "duration_of"}, {"source": "time", "target": "b", "relation": "measures"}], "reasoning_type": "temporal", "composed_of": ["n", "用时", "num", "分钟"]}, {"id": "23", "pattern": ["how", "long", "will", "it", "take"], "relation_template": "time=(b-c)/(d*e-f*60)", "var_slot_val": "target_water,initial_water,cube_volume,cube_rate,leak_rate", "var_slot_index": {"b": "target_water", "c": "initial_water", "d": "cube_volume", "e": "cube_rate", "f": "leak_rate"}, "description": "时间耗费，例：How long will it take", "scene": "属性关系_时间属性", "dependencies": [], "semantic_dependencies": [{"source": "time", "target": "b", "relation": "target_state"}, {"source": "time", "target": "c", "relation": "initial_state"}, {"source": "time", "target": "d", "relation": "rate_factor_1"}, {"source": "time", "target": "e", "relation": "rate_factor_2"}, {"source": "time", "target": "f", "relation": "rate_factor_3"}], "reasoning_type": "temporal_complex", "composed_of": ["how", "long", "will", "it", "take"]}, {"id": "13", "pattern": ["adv", "v", "n", "v", "prep", "m", "q"], "relation_template": "a=(b-c)/d", "var_slot_val": "adv,n,m,q", "var_slot_index": {"a": "0", "b": "2", "c": "1", "d": "3"}, "description": "识别形如'多长时间后水池的水量会达到200升'的问句", "scene": "属性关系_时间属性", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "goal_depends_on"}, {"source": "a", "target": "c", "relation": "start_depends_on"}, {"source": "a", "target": "d", "relation": "rate_depends_on"}], "reasoning_type": "temporal", "composed_of": ["adv", "v", "n", "v", "prep", "m", "q"]}]}, "隐性关系": {"description": "抽象语义依赖的隐性关系模式", "变化类": [{"id": "delta_abstract", "pattern": [], "relation_template": "a = b - c", "var_slot_val": "a,b,c", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "抽象变化关系", "scene": "隐性关系_变化", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "final_state"}, {"source": "a", "target": "c", "relation": "initial_state"}], "reasoning_type": "implicit_delta", "composed_of": []}], "总和类": [{"id": "sum_abstract", "pattern": [], "relation_template": "a = b + c", "var_slot_val": "a,b,c", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "抽象总和关系", "scene": "隐性关系_总和", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "composed_of"}, {"source": "a", "target": "c", "relation": "composed_of"}], "reasoning_type": "implicit_sum", "composed_of": []}], "递推类": [{"id": "recurrence_abstract", "pattern": [], "relation_template": "a = b + c", "var_slot_val": "a,b,c", "var_slot_index": {"a": "0", "b": "1", "c": "2"}, "description": "抽象递推关系", "scene": "隐性关系_递推", "dependencies": [], "semantic_dependencies": [{"source": "a", "target": "b", "relation": "previous_state"}, {"source": "a", "target": "c", "relation": "incremental_change"}], "reasoning_type": "implicit_recurrence", "composed_of": []}]}}, "scene_information": {"description": "场景驱动的模式分类体系", "scenes": [{"name": "数量关系_基础计量", "description": "处理基本的数量关系计算", "applicable_patterns": ["1", "2", "3", "4"], "features": ["变量数量<=3", "主要运算:乘除", "问题目标:计算数量"], "reasoning_chains": ["直接计算", "单步推理"]}, {"name": "数量关系_合作效率", "description": "处理多个主体共同完成工作的效率问题", "applicable_patterns": ["work_coop_1"], "features": ["变量数量>=3", "主要运算:分数运算", "问题目标:计算时间"], "reasoning_chains": ["效率合成", "多步推理"]}, {"name": "数量关系_行程追及", "description": "处理物体运动相遇或追赶的问题", "applicable_patterns": ["chase_1"], "features": ["变量数量>=4", "主要运算:速度时间距离", "问题目标:计算时间或距离"], "reasoning_chains": ["多变量方程", "条件推理"]}, {"name": "数量关系_流水混合", "description": "处理液体混合后的浓度或含量问题", "applicable_patterns": ["mixture_1"], "features": ["变量数量>=4", "主要运算:加权平均", "问题目标:计算浓度"], "reasoning_chains": ["加权平均", "比例推理"]}, {"name": "数量关系_数列递推", "description": "处理数列和递推关系的问题", "applicable_patterns": ["seq_rec_1"], "features": ["变量数量>=2", "主要运算:递推公式", "问题目标:计算项数或项值"], "reasoning_chains": ["递推公式", "序列推理", "递归计算"]}, {"name": "数量关系_复合题型", "description": "处理多个子问题组合的复杂问题", "applicable_patterns": ["composite_1"], "features": ["变量数量>=5", "主要运算:多种复合", "问题目标:多步计算"], "reasoning_chains": ["组合推理", "树状结构", "多路径计算"]}, {"name": "属性关系_时间属性", "description": "处理与时间相关的属性和变化", "applicable_patterns": ["13", "22", "23"], "features": ["变量类型:时间", "主要运算:速率计算", "问题目标:计算时间"], "reasoning_chains": ["时间推理", "速率计算"]}]}, "reasoning_relationships": {"description": "模式间推理关系类型", "types": [{"name": "链式推理", "description": "一个模式的输出作为另一个模式的输入", "examples": ["seq_rec_1 -> seq_rec_1", "1 -> 3"], "visualization": "A→B→C"}, {"name": "树状推理", "description": "多个独立模式的结果汇总到一个模式", "examples": ["1,2,3 -> composite_1"], "visualization": "A↘\nB→D\nC↗"}, {"name": "环状推理", "description": "多个模式循环依赖形成闭环", "examples": ["复杂方程组求解"], "visualization": "A→B→C→A"}, {"name": "递归推理", "description": "模式多次应用于自身的结果", "examples": ["seq_rec_1"], "visualization": "A→A'→A''→..."}, {"name": "并行推理", "description": "多个模式同时应用后合并结果", "examples": ["mixture_1 + work_coop_1"], "visualization": "A\nB→结果\nC"}]}}