#!/usr/bin/env python3
"""
Proposed COT-DIR Model Implementation

This module implements the proposed Chain-of-Thought with Directional Implicit Reasoning (COT-DIR) model.
The model includes three main components:
1. Implicit Relation Discovery (IRD)
2. Multi-Level Reasoning (MLR)
3. Chain Verification (CV)
"""

import json
import logging
import re
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np

from .base_model import ModelInput, ModelOutput, ProposedModel


class ComplexityLevel(Enum):
    """Complexity levels for mathematical problems."""
    L0_EXPLICIT = "L0"
    L1_SHALLOW = "L1"
    L2_MEDIUM = "L2"
    L3_DEEP = "L3"


class RelationType(Enum):
    """Types of implicit relations."""
    ARITHMETIC = "arithmetic"
    PROPORTION = "proportion"
    COMPARISON = "comparison"
    TEMPORAL = "temporal"
    CAUSAL = "causal"
    CONSTRAINT = "constraint"


@dataclass
class ImplicitRelation:
    """Represents an implicit relation discovered in the problem."""
    relation_type: RelationType
    entities: List[str]
    confidence: float
    description: str
    mathematical_expression: Optional[str] = None


@dataclass
class ReasoningStep:
    """Represents a step in the reasoning chain."""
    step_id: int
    description: str
    operation: str
    input_values: List[float]
    output_value: float
    confidence: float
    depends_on: List[int]  # IDs of previous steps this depends on


@dataclass
class VerificationResult:
    """Results from chain verification."""
    is_consistent: bool
    consistency_score: float
    logical_errors: List[str]
    mathematical_errors: List[str]
    suggestions: List[str]


class COTDIRModel(ProposedModel):
    """Chain-of-Thought with Directional Implicit Reasoning model."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("COT-DIR", config)
        
        # Component configuration
        self.enable_ird = config.get("enable_ird", True) if config else True
        self.enable_mlr = config.get("enable_mlr", True) if config else True
        self.enable_cv = config.get("enable_cv", True) if config else True
        
        # Model parameters
        self.confidence_threshold = config.get("confidence_threshold", 0.7) if config else 0.7
        self.max_reasoning_depth = config.get("max_reasoning_depth", 5) if config else 5
        self.relation_discovery_threshold = config.get("relation_threshold", 0.6) if config else 0.6
        
        # Component weights for final scoring
        self.ird_weight = config.get("ird_weight", 0.3) if config else 0.3
        self.mlr_weight = config.get("mlr_weight", 0.5) if config else 0.5
        self.cv_weight = config.get("cv_weight", 0.2) if config else 0.2
        
        # Initialize components
        self.relation_patterns = self._initialize_relation_patterns()
        self.reasoning_templates = self._initialize_reasoning_templates()
        self.verification_rules = self._initialize_verification_rules()
        
    def initialize(self) -> bool:
        """Initialize the COT-DIR model."""
        try:
            self.logger.info("Initializing COT-DIR model")
            
            # Set component status
            self.components["ird"] = self.enable_ird
            self.components["mlr"] = self.enable_mlr
            self.components["cv"] = self.enable_cv
            
            # Validate configuration
            if not any([self.enable_ird, self.enable_mlr, self.enable_cv]):
                self.logger.error("At least one component must be enabled")
                return False
            
            self.is_initialized = True
            self.logger.info(f"COT-DIR initialized with components: IRD={self.enable_ird}, MLR={self.enable_mlr}, CV={self.enable_cv}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize COT-DIR model: {e}")
            return False
    
    def solve_problem(self, problem_input: ModelInput) -> ModelOutput:
        """Solve problem using COT-DIR approach."""
        start_time = time.time()
        
        if not self.validate_input(problem_input):
            return ModelOutput(
                answer="",
                reasoning_chain=["Invalid input"],
                confidence_score=0.0,
                processing_time=time.time() - start_time,
                error_message="Invalid input format"
            )
        
        try:
            reasoning_chain = []
            intermediate_steps = []
            final_answer = ""
            
            # Step 1: Problem Analysis
            complexity = self._analyze_complexity(problem_input.problem_text)
            reasoning_chain.append(f"Problem complexity analyzed: {complexity.value}")
            
            # Step 2: Implicit Relation Discovery (IRD)
            relations = []
            if self.enable_ird:
                relations = self.implicit_relation_discovery(problem_input)
                reasoning_chain.append(f"Discovered {len(relations)} implicit relations")
                for rel in relations:
                    reasoning_chain.append(f"  - {rel.relation_type.value}: {rel.description} (confidence: {rel.confidence:.2f})")
            
            # Step 3: Multi-Level Reasoning (MLR)
            reasoning_steps = []
            if self.enable_mlr:
                reasoning_steps = self.multi_level_reasoning({"relations": relations, "complexity": complexity, "problem": problem_input})
                reasoning_chain.append(f"Generated {len(reasoning_steps)} reasoning steps")
                for step in reasoning_steps:
                    reasoning_chain.append(f"  Step {step.step_id}: {step.description} -> {step.output_value}")
                    intermediate_steps.append({
                        "step_id": step.step_id,
                        "description": step.description,
                        "operation": step.operation,
                        "result": step.output_value,
                        "confidence": step.confidence
                    })
            
            # Step 4: Chain Verification (CV)
            verification_result = None
            if self.enable_cv and reasoning_steps:
                verification_result = self.chain_verification(reasoning_steps)
                reasoning_chain.append(f"Chain verification: consistency={verification_result.is_consistent}, score={verification_result.consistency_score:.2f}")
                if verification_result.logical_errors:
                    reasoning_chain.extend([f"  Logic error: {error}" for error in verification_result.logical_errors])
                if verification_result.mathematical_errors:
                    reasoning_chain.extend([f"  Math error: {error}" for error in verification_result.mathematical_errors])
            
            # Step 5: Generate Final Answer
            if reasoning_steps:
                final_step = reasoning_steps[-1]
                final_answer = str(final_step.output_value)
            else:
                # Fallback to simple extraction
                numbers = self._extract_numbers(problem_input.problem_text)
                if len(numbers) >= 2:
                    final_answer = str(sum(numbers))
                    reasoning_chain.append(f"Fallback: sum of extracted numbers = {final_answer}")
            
            # Calculate confidence score
            confidence = self._calculate_confidence(relations, reasoning_steps, verification_result)
            
            return ModelOutput(
                answer=final_answer,
                reasoning_chain=reasoning_chain,
                confidence_score=confidence,
                processing_time=time.time() - start_time,
                memory_usage=self._estimate_memory_usage(relations, reasoning_steps),
                intermediate_steps=intermediate_steps,
                metadata={
                    "complexity": complexity.value,
                    "relations_count": len(relations),
                    "reasoning_steps": len(reasoning_steps),
                    "verification_score": verification_result.consistency_score if verification_result else None,
                    "components_used": {
                        "ird": self.enable_ird,
                        "mlr": self.enable_mlr,
                        "cv": self.enable_cv
                    }
                }
            )
            
        except Exception as e:
            return ModelOutput(
                answer="",
                reasoning_chain=[f"Error in COT-DIR processing: {str(e)}"],
                confidence_score=0.0,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def batch_solve(self, problems: List[ModelInput]) -> List[ModelOutput]:
        """Solve multiple problems using COT-DIR."""
        return [self.solve_problem(problem) for problem in problems]
    
    def implicit_relation_discovery(self, problem_input: ModelInput) -> List[ImplicitRelation]:
        """Discover implicit relations in the problem using IRD component."""
        relations = []
        text = problem_input.problem_text.lower()
        
        # Extract entities (numbers, quantities, objects)
        entities = self._extract_entities(problem_input.problem_text)
        
        # Apply relation discovery patterns
        for pattern_type, patterns in self.relation_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern["regex"], text)
                for match in matches:
                    relation = ImplicitRelation(
                        relation_type=RelationType(pattern_type),
                        entities=pattern["entities"](match, entities),
                        confidence=pattern["confidence"],
                        description=pattern["description"](match),
                        mathematical_expression=pattern.get("expression", lambda m: None)(match)
                    )
                    
                    if relation.confidence >= self.relation_discovery_threshold:
                        relations.append(relation)
        
        # Remove duplicate relations
        relations = self._deduplicate_relations(relations)
        
        # Sort by confidence
        relations.sort(key=lambda r: r.confidence, reverse=True)
        
        return relations
    
    def multi_level_reasoning(self, context: Dict[str, Any]) -> List[ReasoningStep]:
        """Perform multi-level reasoning using MLR component."""
        relations = context.get("relations", [])
        complexity = context.get("complexity", ComplexityLevel.L1_SHALLOW)
        problem_input = context.get("problem")
        
        reasoning_steps = []
        step_id = 1
        
        # Extract numerical values
        numbers = self._extract_numbers(problem_input.problem_text)
        
        # Level 1: Direct computation from explicit information
        if numbers:
            step = ReasoningStep(
                step_id=step_id,
                description="Extract numerical values from problem",
                operation="extraction",
                input_values=[],
                output_value=len(numbers),
                confidence=0.9,
                depends_on=[]
            )
            reasoning_steps.append(step)
            step_id += 1
        
        # Level 2: Apply discovered relations
        for relation in relations:
            if relation.mathematical_expression:
                try:
                    # Evaluate mathematical expression
                    result = self._evaluate_expression(relation.mathematical_expression, numbers)
                    step = ReasoningStep(
                        step_id=step_id,
                        description=f"Apply {relation.relation_type.value} relation: {relation.description}",
                        operation=relation.relation_type.value,
                        input_values=numbers[:2] if len(numbers) >= 2 else numbers,
                        output_value=result,
                        confidence=relation.confidence,
                        depends_on=[1] if reasoning_steps else []
                    )
                    reasoning_steps.append(step)
                    step_id += 1
                except Exception as e:
                    self.logger.warning(f"Failed to evaluate relation expression: {e}")
        
        # Level 3: Higher-order reasoning for complex problems
        if complexity in [ComplexityLevel.L2_MEDIUM, ComplexityLevel.L3_DEEP]:
            # Multi-step calculations
            if len(numbers) >= 3:
                # Combine operations
                intermediate_result = numbers[0] + numbers[1]
                step = ReasoningStep(
                    step_id=step_id,
                    description=f"Intermediate calculation: {numbers[0]} + {numbers[1]}",
                    operation="addition",
                    input_values=[numbers[0], numbers[1]],
                    output_value=intermediate_result,
                    confidence=0.8,
                    depends_on=[1]
                )
                reasoning_steps.append(step)
                step_id += 1
                
                final_result = intermediate_result * numbers[2]
                step = ReasoningStep(
                    step_id=step_id,
                    description=f"Final calculation: {intermediate_result} * {numbers[2]}",
                    operation="multiplication",
                    input_values=[intermediate_result, numbers[2]],
                    output_value=final_result,
                    confidence=0.85,
                    depends_on=[step_id - 1]
                )
                reasoning_steps.append(step)
        
        # Level 4: Complex reasoning for L3 problems
        if complexity == ComplexityLevel.L3_DEEP:
            # Add verification and constraint checking
            if reasoning_steps:
                last_step = reasoning_steps[-1]
                # Simple constraint: result should be positive for most real-world problems
                if last_step.output_value > 0:
                    confidence_boost = 0.1
                    last_step.confidence = min(1.0, last_step.confidence + confidence_boost)
        
        return reasoning_steps
    
    def chain_verification(self, reasoning_chain: List[ReasoningStep]) -> VerificationResult:
        """Verify the consistency and correctness of reasoning chain using CV component."""
        logical_errors = []
        mathematical_errors = []
        suggestions = []
        
        # Check logical consistency
        for i, step in enumerate(reasoning_chain):
            # Check dependencies
            for dep_id in step.depends_on:
                if dep_id > i:
                    logical_errors.append(f"Step {step.step_id} depends on future step {dep_id}")
                
                # Check if dependency exists
                dep_exists = any(s.step_id == dep_id for s in reasoning_chain[:i])
                if not dep_exists:
                    logical_errors.append(f"Step {step.step_id} depends on non-existent step {dep_id}")
        
        # Check mathematical consistency
        for step in reasoning_chain:
            if step.operation == "addition" and len(step.input_values) >= 2:
                expected = sum(step.input_values)
                if abs(step.output_value - expected) > 1e-6:
                    mathematical_errors.append(f"Step {step.step_id}: addition error {step.input_values} != {step.output_value}")
            
            elif step.operation == "multiplication" and len(step.input_values) >= 2:
                expected = step.input_values[0] * step.input_values[1]
                if abs(step.output_value - expected) > 1e-6:
                    mathematical_errors.append(f"Step {step.step_id}: multiplication error {step.input_values} != {step.output_value}")
        
        # Generate suggestions
        if not logical_errors and not mathematical_errors:
            suggestions.append("Reasoning chain is logically and mathematically consistent")
        else:
            if logical_errors:
                suggestions.append("Review step dependencies and order")
            if mathematical_errors:
                suggestions.append("Verify mathematical calculations")
        
        # Calculate consistency score
        total_errors = len(logical_errors) + len(mathematical_errors)
        max_possible_errors = len(reasoning_chain) * 2  # Assume max 2 errors per step
        consistency_score = max(0.0, 1.0 - (total_errors / max_possible_errors)) if max_possible_errors > 0 else 1.0
        
        return VerificationResult(
            is_consistent=(total_errors == 0),
            consistency_score=consistency_score,
            logical_errors=logical_errors,
            mathematical_errors=mathematical_errors,
            suggestions=suggestions
        )
    
    def _analyze_complexity(self, problem_text: str) -> ComplexityLevel:
        """Analyze problem complexity."""
        text = problem_text.lower()
        
        # Count complexity indicators
        complexity_indicators = {
            "multiple_operations": len(re.findall(r'\b(and|then|after|before|next)\b', text)),
            "conditional_logic": len(re.findall(r'\b(if|when|unless|provided)\b', text)),
            "temporal_relations": len(re.findall(r'\b(before|after|during|while)\b', text)),
            "comparison_terms": len(re.findall(r'\b(more|less|greater|smaller|compared)\b', text)),
            "word_count": len(text.split()),
            "number_count": len(re.findall(r'\d+(?:\.\d+)?', text))
        }
        
        # Calculate complexity score
        score = 0
        score += min(complexity_indicators["multiple_operations"] * 10, 30)
        score += min(complexity_indicators["conditional_logic"] * 15, 30)
        score += min(complexity_indicators["temporal_relations"] * 12, 24)
        score += min(complexity_indicators["comparison_terms"] * 8, 24)
        score += min(complexity_indicators["word_count"] // 10, 20)
        score += min(complexity_indicators["number_count"] * 5, 20)
        
        # Determine complexity level
        if score >= 80:
            return ComplexityLevel.L3_DEEP
        elif score >= 50:
            return ComplexityLevel.L2_MEDIUM
        elif score >= 20:
            return ComplexityLevel.L1_SHALLOW
        else:
            return ComplexityLevel.L0_EXPLICIT
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract entities from text."""
        entities = {
            "numbers": re.findall(r'\d+(?:\.\d+)?', text),
            "objects": re.findall(r'\b([a-zA-Z]+(?:\s+[a-zA-Z]+)*)\b(?=\s+(?:costs?|weighs?|measures?))', text),
            "units": re.findall(r'\b(?:dollars?|cents?|pounds?|kilograms?|meters?|feet|hours?|minutes?|seconds?)\b', text.lower()),
            "quantities": re.findall(r'\b(?:total|sum|difference|product|quotient|average)\b', text.lower())
        }
        return entities
    
    def _extract_numbers(self, text: str) -> List[float]:
        """Extract numerical values from text."""
        pattern = r'\d+(?:\.\d+)?'
        matches = re.findall(pattern, text)
        return [float(match) for match in matches]
    
    def _evaluate_expression(self, expression: str, numbers: List[float]) -> float:
        """Safely evaluate mathematical expression."""
        try:
            # Replace placeholders with actual numbers
            expr = expression
            for i, num in enumerate(numbers):
                expr = expr.replace(f"${i}", str(num))
            
            # Only allow safe mathematical operations
            allowed_names = {
                "__builtins__": {},
                "abs": abs,
                "min": min,
                "max": max,
                "sum": sum,
                "round": round
            }
            
            return eval(expr, allowed_names)
        except Exception:
            return 0.0
    
    def _calculate_confidence(self, relations: List[ImplicitRelation], 
                            reasoning_steps: List[ReasoningStep],
                            verification_result: Optional[VerificationResult]) -> float:
        """Calculate overall confidence score."""
        confidence_components = []
        
        # IRD contribution
        if self.enable_ird and relations:
            ird_confidence = sum(r.confidence for r in relations) / len(relations)
            confidence_components.append(self.ird_weight * ird_confidence)
        
        # MLR contribution
        if self.enable_mlr and reasoning_steps:
            mlr_confidence = sum(s.confidence for s in reasoning_steps) / len(reasoning_steps)
            confidence_components.append(self.mlr_weight * mlr_confidence)
        
        # CV contribution
        if self.enable_cv and verification_result:
            confidence_components.append(self.cv_weight * verification_result.consistency_score)
        
        # Calculate weighted average
        if confidence_components:
            return sum(confidence_components) / sum([
                self.ird_weight if self.enable_ird else 0,
                self.mlr_weight if self.enable_mlr else 0,
                self.cv_weight if self.enable_cv else 0
            ])
        else:
            return 0.5  # Default confidence
    
    def _estimate_memory_usage(self, relations: List[ImplicitRelation], 
                             reasoning_steps: List[ReasoningStep]) -> float:
        """Estimate memory usage in MB."""
        base_memory = 0.5  # Base overhead
        relation_memory = len(relations) * 0.01  # 10KB per relation
        step_memory = len(reasoning_steps) * 0.005  # 5KB per step
        return base_memory + relation_memory + step_memory
    
    def _deduplicate_relations(self, relations: List[ImplicitRelation]) -> List[ImplicitRelation]:
        """Remove duplicate relations."""
        seen = set()
        unique_relations = []
        
        for relation in relations:
            key = (relation.relation_type, frozenset(relation.entities), relation.description)
            if key not in seen:
                seen.add(key)
                unique_relations.append(relation)
        
        return unique_relations
    
    def _initialize_relation_patterns(self) -> Dict[str, List[Dict[str, Any]]]:
        """Initialize patterns for relation discovery."""
        return {
            "arithmetic": [
                {
                    "regex": r"(\d+(?:\.\d+)?)\s+(?:plus|and|\+)\s+(\d+(?:\.\d+)?)",
                    "confidence": 0.9,
                    "entities": lambda m, e: [m.group(1), m.group(2)],
                    "description": lambda m: f"Addition: {m.group(1)} + {m.group(2)}",
                    "expression": lambda m: f"{m.group(1)} + {m.group(2)}"
                },
                {
                    "regex": r"(\d+(?:\.\d+)?)\s+(?:times|multiplied by|\*)\s+(\d+(?:\.\d+)?)",
                    "confidence": 0.9,
                    "entities": lambda m, e: [m.group(1), m.group(2)],
                    "description": lambda m: f"Multiplication: {m.group(1)} × {m.group(2)}",
                    "expression": lambda m: f"{m.group(1)} * {m.group(2)}"
                }
            ],
            "comparison": [
                {
                    "regex": r"(\d+(?:\.\d+)?)\s+(?:more than|greater than)\s+(\d+(?:\.\d+)?)",
                    "confidence": 0.8,
                    "entities": lambda m, e: [m.group(1), m.group(2)],
                    "description": lambda m: f"Comparison: {m.group(1)} > {m.group(2)}",
                    "expression": lambda m: f"{m.group(1)} - {m.group(2)}"
                }
            ],
            "proportion": [
                {
                    "regex": r"(\d+(?:\.\d+)?)\s+(?:percent|%)\s+of\s+(\d+(?:\.\d+)?)",
                    "confidence": 0.85,
                    "entities": lambda m, e: [m.group(1), m.group(2)],
                    "description": lambda m: f"Percentage: {m.group(1)}% of {m.group(2)}",
                    "expression": lambda m: f"({m.group(1)} / 100) * {m.group(2)}"
                }
            ]
        }
    
    def _initialize_reasoning_templates(self) -> Dict[str, Any]:
        """Initialize templates for reasoning."""
        return {
            "basic_arithmetic": {
                "operations": ["addition", "subtraction", "multiplication", "division"],
                "confidence": 0.8
            },
            "word_problems": {
                "patterns": ["total", "difference", "product", "quotient"],
                "confidence": 0.7
            }
        }
    
    def _initialize_verification_rules(self) -> List[Dict[str, Any]]:
        """Initialize rules for chain verification."""
        return [
            {
                "name": "mathematical_consistency",
                "description": "Check if mathematical operations are correct",
                "weight": 0.5
            },
            {
                "name": "logical_flow",
                "description": "Check if reasoning steps follow logical order",
                "weight": 0.3
            },
            {
                "name": "dependency_validity",
                "description": "Check if step dependencies are valid",
                "weight": 0.2
            }
        ]


# Register proposed model with factory
from .base_model import ModelFactory

ModelFactory.register_model(COTDIRModel, "cotdir") 
