{"version": "1.0.0", "description": "默认模式定义文件，包含各种问题类型的模式定义", "patterns": {"motion": {"meet_head_on": {"description": "相向而行问题", "regex_patterns": {"speed": ["(\\d+(?:\\.\\d+)?)\\s*(?:km/h|公里/小时|千米/小时|公里每小时|千米每小时|公里/h|千米/h|km/小时|km每小时)"], "distance": ["(\\d+(?:\\.\\d+)?)\\s*(?:km|公里|千米)"]}, "keywords": ["相向", "相遇", "迎面", "对向", "相对", "两地", "A地", "B地"], "equation_template": "time = distance / (speed1 + speed2)"}, "same_direction": {"description": "同向而行问题", "regex_patterns": {"speed": ["(\\d+(?:\\.\\d+)?)\\s*(?:km/h|公里/小时|千米/小时|公里每小时|千米每小时|公里/h|千米/h|km/小时|km每小时)"], "distance": ["(\\d+(?:\\.\\d+)?)\\s*(?:km|公里|千米)"]}, "keywords": ["同向", "追及", "追上", "超过", "赶上"], "equation_template": "time = distance / (speed1 - speed2)"}}, "work_efficiency": {"together": {"description": "共同工作问题", "regex_patterns": {"time": ["(\\d+(?:\\.\\d+)?)\\s*(?:小时|天|分钟|h|d|min)"]}, "keywords": ["共同", "一起", "合作", "协作"], "equation_template": "1/together_time = 1/time1 + 1/time2"}, "efficiency_ratio": {"description": "效率比例问题", "regex_patterns": {"ratio": ["(\\d+(?:\\.\\d+)?)\\s*倍"], "time": ["(\\d+(?:\\.\\d+)?)\\s*(?:小时|天|分钟|h|d|min)"]}, "keywords": ["效率", "比", "倍"], "equation_template": "time2 = time1 / ratio"}}, "rate_and_volume": {"ice_cube_problem": {"description": "冰块问题", "regex_patterns": {"volume": ["(\\d+(?:\\.\\d+)?)\\s*(?:cm³|立方厘米|立方cm|cm3)"], "water_volume": ["(\\d+(?:\\.\\d+)?)\\s*(?:L|升|立升|l)"], "leak_rate": ["(\\d+(?:\\.\\d+)?)\\s*(?:mL|毫升|ml)/(?:s|秒|second)"]}, "keywords": ["冰块", "水箱", "容器", "水池", "水缸"], "equation_template": "time = (target_volume - initial_volume) / (cube_volume * cube_rate - leak_rate)"}, "water_tank": {"description": "水箱问题", "regex_patterns": {"volume": ["(\\d+(?:\\.\\d+)?)\\s*(?:L|升|立升|l|m³|立方米)"], "rate": ["(\\d+(?:\\.\\d+)?)\\s*(?:L|升|立升|l|m³|立方米)/(?:min|分钟|h|小时)"]}, "keywords": ["水箱", "容器", "水池", "水缸", "注水", "放水"], "equation_template": "time = volume / rate"}}, "investment_growth": {"compound_interest": {"description": "复利问题", "regex_patterns": {"initial_amount": ["(\\d+(?:\\.\\d+)?)\\s*(?:元|块|万元|万|k|千|千元)"], "rate": ["(\\d+(?:\\.\\d+)?)\\s*%"], "time": ["(\\d+(?:\\.\\d+)?)\\s*(?:年|月|日|周|week|month|year)"]}, "keywords": ["复利", "利息", "本金", "投资", "增长"], "equation_template": "final_amount = initial_amount * (1 + rate)^time"}, "simple_interest": {"description": "单利问题", "regex_patterns": {"initial_amount": ["(\\d+(?:\\.\\d+)?)\\s*(?:元|块|万元|万|k|千|千元)"], "rate": ["(\\d+(?:\\.\\d+)?)\\s*%"], "time": ["(\\d+(?:\\.\\d+)?)\\s*(?:年|月|日|周|week|month|year)"]}, "keywords": ["单利", "利息", "本金", "投资"], "equation_template": "final_amount = initial_amount * (1 + rate * time)"}}, "mixture": {"concentration": {"description": "浓度问题", "regex_patterns": {"concentration": ["(\\d+(?:\\.\\d+)?)\\s*%"], "volume": ["(\\d+(?:\\.\\d+)?)\\s*(?:L|升|立升|l|mL|毫升|ml)"]}, "keywords": ["浓度", "溶液", "混合", "稀释"], "equation_template": "c1 * v1 + c2 * v2 = c3 * (v1 + v2)"}}, "sequence": {"arithmetic": {"description": "等差数列问题", "regex_patterns": {"first_term": ["(\\d+(?:\\.\\d+)?)"], "difference": ["(\\d+(?:\\.\\d+)?)"], "n": ["(\\d+(?:\\.\\d+)?)"]}, "keywords": ["等差", "数列", "首项", "公差"], "equation_template": "an = a1 + (n-1)*d"}, "geometric": {"description": "等比数列问题", "regex_patterns": {"first_term": ["(\\d+(?:\\.\\d+)?)"], "ratio": ["(\\d+(?:\\.\\d+)?)"], "n": ["(\\d+(?:\\.\\d+)?)"]}, "keywords": ["等比", "数列", "首项", "公比"], "equation_template": "an = a1 * r^(n-1)"}}}}