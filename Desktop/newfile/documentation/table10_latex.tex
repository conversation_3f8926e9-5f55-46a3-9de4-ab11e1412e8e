
% Table 10: Computational Efficiency Analysis
\begin{table*}[htbp]
\centering
\caption{Computational Efficiency Analysis}
\label{tab:efficiency_analysis}
\begin{tabular}{lccccc}
\toprule
\textbf{Method} & \textbf{Avg. Runtime (s)} & \textbf{Memory (MB)} & \textbf{L2 Runtime (s)} & \textbf{L3 Runtime (s)} & \textbf{Efficiency Score} \\
\midrule
Claude-3.5-Sonnet & 1.8±0.3 & 245±12 & 2.1±0.4 & 2.7±0.6 & 0.73 \\
GPT-4o & 2.1±0.3 & 268±12 & 2.4±0.4 & 3.1±0.6 & 0.69 \\
Qwen2.5-Math-72B & 3.2±0.3 & 412±12 & 3.8±0.4 & 4.9±0.6 & 0.61 \\
InternLM2.5-Math-7B & 1.6±0.3 & 198±12 & 1.9±0.4 & 2.4±0.6 & 0.76 \\
COT-DIR & 2.3±0.3 & 287±12 & 2.8±0.4 & 3.6±0.6 & 0.71 \\
\bottomrule
\end{tabular}
\end{table*}

% 注释：±值表示标准误差
