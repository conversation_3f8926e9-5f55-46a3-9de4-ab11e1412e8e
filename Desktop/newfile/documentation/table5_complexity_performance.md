# Table 5: Performance Analysis by Problem Complexity Level

| Method | L0 (%) | L1 (%) | L2 (%) | L3 (%) | Robustness | Complexity Drop |
|--------|--------|--------|--------|--------|------------|------------------|
| Claude-3.5-<PERSON><PERSON> | 94.2 | 87.6 | 78.4 | 65.7 | 0.74 | 28.5 |
| GPT-4o | 92.8 | 85.3 | 76.1 | 63.2 | 0.71 | 29.6 |
| Qwen2.5-Math-72B | 95.1 | 89.3 | 80.7 | 68.4 | 0.77 | 26.7 |
| InternLM2.5-Math-7B | 88.9 | 80.4 | 70.1 | 57.2 | 0.66 | 31.7 |
| DeepSeek-Math-7B | 89.6 | 81.8 | 71.9 | 59.1 | 0.68 | 30.5 |
| Graph2Tree | 88.6 | 79.2 | 68.5 | 54.3 | 0.62 | 34.3 |
| COT-DIR | 95.1 | 90.7 | 83.4 | 73.2 | 0.82 | 21.9 |
