# COT-DIR + MLR 演示系统使用说明

## 系统概述

本演示系统展示了**COT-DIR（Chain of Thought - Directional Implicit Reasoning）+ MLR（Multi-Layer Reasoning）**的完整推理过程，从文字输入到最终解题的每一个步骤都会详细显示。

## 🚀 如何运行演示

### 方式一：交互式输入
```bash
python interactive_demo.py
```
运行后会提示您输入问题，您可以输入任何数学问题。

### 方式二：直接指定问题
```bash
python interactive_demo.py "您的数学问题"
```

### 方式三：详细逐步演示
```bash
python detailed_step_by_step_demo.py
```
这会运行预设的详细演示。

## 📝 支持的问题类型和输入示例

### 1. 加法计算问题
**特征关键词：** 一共、总共、加起来

**示例输入：**
- `小明有3个苹果，小红有5个苹果，他们一共有多少个苹果？`
- `教室里有15个学生，又来了8个学生，总共有多少个学生？`
- `小华买了12本书，小李买了7本书，他们加起来有多少本书？`

**系统会展示：**
1. 识别人物实体（小明、小红）
2. 识别数量实体（3、5）
3. 识别物品实体（苹果）
4. 发现拥有关系（小明拥有3个苹果）
5. 发现数学关系（总数=部分之和）
6. L1层直接计算：提取数值[3,5]
7. L2层关系应用：3+5=8
8. L3层目标验证：确认答案8

### 2. 减法计算问题
**特征关键词：** 剩下、还有、用了、吃了

**示例输入：**
- `小明有10个苹果，吃了3个，还剩多少个？`
- `书架上有25本书，借走了9本，剩下多少本？`
- `妈妈给了我50元，买文具花了15元，还有多少钱？`

### 3. 乘法计算问题
**特征关键词：** 倍、每、共

**示例输入：**
- `每盒有6支笔，买了4盒，一共有多少支笔？`
- `小明的苹果数是小红的3倍，小红有5个，小明有多少个？`
- `一个班有30个学生，5个班一共有多少个学生？`

### 4. 除法计算问题
**特征关键词：** 平均、每人、分给

**示例输入：**
- `20个苹果平均分给4个人，每人分到多少个？`
- `60元钱买了12支笔，每支笔多少钱？`
- `班里有24个学生，分成6组，每组有多少人？`

### 5. 混合运算问题
**示例输入：**
- `小明有8个苹果，小红比小明多3个，小华比小红少2个，他们三人一共有多少个苹果？`
- `商店原有45本书，上午卖了12本，下午又进了20本，现在有多少本书？`

## 🔍 系统输出详解

### 第1步：文字输入分析
- **基础统计**：字符数、分词数
- **问题类型识别**：加法、减法、乘法、除法等
- **语言特征提取**：关键词、数字、人名、单位

### 第2步：实体发现与标注
- **人物实体**：识别问题中的人物（小明、小红等）
- **数量实体**：提取数值及其上下文（3个、5元等）
- **物品实体**：识别可计数对象（苹果、书、学生等）
- **动作实体**：识别操作动词（有、买、卖等）

### 第3步：关系发现与构建
- **拥有关系**：谁拥有什么物品
- **数学关系**：加法、减法等数学运算关系
- **关系图构建**：节点数、边数、连通分量

### 第4步：多层推理 (MLR)
- **L1层（直接计算）**：提取和识别基础数值
- **L2层（关系应用）**：应用发现的关系进行计算
- **L3层（目标导向）**：验证结果是否符合问题要求

### 第5步：置信度验证
七维验证体系：
1. **逻辑一致性**：推理过程是否逻辑清晰
2. **数学正确性**：计算是否准确
3. **语义对齐**：理解是否符合语义
4. **约束满足**：是否满足问题约束
5. **常识检查**：结果是否符合常识
6. **推理完整性**：推理链是否完整
7. **解决方案最优性**：解法是否最优

### 第6步：最终答案生成
- **最终答案**：问题的数值解
- **置信度**：系统对答案的信心程度
- **质量等级**：优秀/良好/及格/需改进
- **推理摘要**：整个推理过程的总结

## 📊 输出报告说明

系统会生成详细的JSON报告，包含：
- 完整的中间步骤记录
- 每个阶段的详细结果
- 置信度分析
- 处理时间统计

报告文件命名格式：`interactive_demo_report_[时间戳].json`

## 💡 使用技巧

### 1. 问题表述要清晰
- 使用具体的数值
- 包含明确的问句（多少、几个等）
- 避免歧义表达

### 2. 支持的中文表达
- 人名：小明、小红、小华、小李、小张等
- 物品：苹果、书、学生、笔、球等
- 动作：有、买、卖、给、分等
- 单位：个、只、元、本、支等

### 3. 观察输出重点
- 注意实体识别的准确性
- 关注关系发现的合理性
- 观察多层推理的逻辑链
- 查看置信度验证的各项得分

## 🎯 演示效果

系统将完整展示：
1. **文字→结构化**：如何将自然语言转换为结构化信息
2. **关系发现**：如何自动发现实体间的隐式关系
3. **推理链条**：如何通过多层推理解决问题
4. **验证机制**：如何确保答案的可靠性

每一步都有详细的中间结果输出，让您清楚看到AI是如何"思考"和"推理"的。

## 📞 常见问题

**Q: 如果识别错误怎么办？**
A: 系统会显示置信度，低置信度的结果会有相应标记。

**Q: 支持多复杂的问题？**
A: 目前主要支持小学数学水平的应用题，复杂问题可能需要简化处理。

**Q: 如何看懂输出结果？**
A: 按步骤阅读，重点关注每步的"描述"和"置信度"信息。

---

**开始您的演示之旅吧！输入一个数学问题，看看AI是如何一步步解决它的。** 