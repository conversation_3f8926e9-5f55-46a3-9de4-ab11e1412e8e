#!/usr/bin/env python3
"""
测试增强后的模式库效果
使用失败样本中的问题测试新模式的效果
"""

import json
import os
import sys

sys.path.append('src')

from reasoning_engine.pattern_based_solver import PatternBasedSolver


def load_test_problems():
    """加载测试问题"""
    with open("failure_analysis.json", 'r', encoding='utf-8') as f:
        failure_samples = json.load(f)
    
    # 选择一些典型的失败样本作为测试
    test_problems = []
    for sample in failure_samples[:20]:  # 测试前20个样本
        test_problems.append({
            "id": sample.get("id", "unknown"),
            "problem": sample.get("problem", ""),
            "expected_answer": sample.get("expected_answer", ""),
            "original_predicted": sample.get("predicted_answer", "")
        })
    
    return test_problems

def test_enhanced_patterns():
    """测试增强后的模式库"""
    print("开始测试增强后的模式库...")
    
    # 加载测试问题
    test_problems = load_test_problems()
    print(f"加载了 {len(test_problems)} 个测试问题")
    
    # 初始化模式求解器
    solver = PatternBasedSolver()
    
    results = []
    improved_count = 0
    total_count = len(test_problems)
    
    for i, problem_data in enumerate(test_problems):
        problem_id = problem_data["id"]
        problem = problem_data["problem"]
        expected = problem_data["expected_answer"]
        original_predicted = problem_data["original_predicted"]
        
        print(f"\n测试问题 {i+1}/{total_count}: {problem_id}")
        print(f"问题: {problem}")
        print(f"期望答案: {expected}")
        print(f"原始预测: {original_predicted}")
        
        try:
            # 使用增强模式库求解
            new_predicted, reasoning_steps = solver.solve(problem)
            confidence = 1.0 if new_predicted is not None else 0.0
            
            print(f"新预测答案: {new_predicted}")
            print(f"置信度: {confidence}")
            
            # 检查是否改进
            improved = False
            if (original_predicted == "" or original_predicted is None) and new_predicted not in (None, ""):
                improved = True
                print("✓ 从无答案改进为有答案")
            elif original_predicted not in (None, "") and new_predicted not in (None, ""):
                try:
                    original_num = float(original_predicted)
                    new_num = float(new_predicted)
                    expected_num = float(expected)
                    
                    original_error = abs(original_num - expected_num)
                    new_error = abs(new_num - expected_num)
                    
                    if new_error < original_error:
                        improved = True
                        print(f"✓ 误差从 {original_error} 改进为 {new_error}")
                except:
                    pass
            
            if improved:
                improved_count += 1
            
            results.append({
                "id": problem_id,
                "problem": problem,
                "expected": expected,
                "original_predicted": original_predicted,
                "new_predicted": new_predicted,
                "confidence": confidence,
                "improved": improved,
                "reasoning_steps": reasoning_steps
            })
            
        except Exception as e:
            print(f"求解出错: {e}")
            results.append({
                "id": problem_id,
                "problem": problem,
                "expected": expected,
                "original_predicted": original_predicted,
                "new_predicted": "",
                "confidence": 0.0,
                "improved": False,
                "error": str(e)
            })
    
    # 生成测试报告
    generate_test_report(results, improved_count, total_count)
    
    return results

def generate_test_report(results, improved_count, total_count):
    """生成测试报告"""
    report = f"""
# 增强模式库测试报告

## 测试统计
- 总测试问题数: {total_count}
- 改进问题数: {improved_count}
- 改进率: {(improved_count/total_count*100):.1f}%

## 详细结果
"""
    
    for result in results:
        report += f"""
### 问题 {result['id']}
- **问题**: {result['problem']}
- **期望答案**: {result['expected']}
- **原始预测**: {result['original_predicted']}
- **新预测**: {result['new_predicted']}
- **置信度**: {result['confidence']}
- **是否改进**: {'✓' if result['improved'] else '✗'}
"""
        if 'reasoning_steps' in result:
            report += "- **推理步骤**:\n"
            for step in result['reasoning_steps']:
                report += f"  - {step}\n"
    
    # 保存报告
    with open("enhanced_patterns_test_report.md", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n=== 测试完成 ===")
    print(f"改进率: {(improved_count/total_count*100):.1f}%")
    print(f"详细报告已保存到: enhanced_patterns_test_report.md")

def main():
    """主函数"""
    try:
        results = test_enhanced_patterns()
        print(f"\n测试完成，共测试 {len(results)} 个问题")
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 