[{"id": "chal-120", "problem": "<PERSON> had 5 more marbles than <PERSON>. <PERSON> lost 3 of his marbles at the playground. If <PERSON> had 27 marbles How many marbles did <PERSON> have initially?", "expected_answer": "22.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [5.0, 3.0, 27.0]", "Extracted entities: {'ed': 27.0, 'doug': 3.0, 'of': 3.0, 'marbles': 27.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('Ed', '5')", "Calculation template: Ed = 5.0", "Matched pattern 'more_than_relation': (\\w+) (?:did|had) (\\d+) more \\w+ than (\\w+)", "Captured groups: ('<PERSON>', '5', '<PERSON>')", "Calculation template: <PERSON> = <PERSON> + 5.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-334", "problem": "There were 2 roses in the vase. <PERSON> threw away 4 roses from the vase and cut some more new roses from her flower garden to put in the vase. There are now 23 roses in the vase. How many roses did she cut?", "expected_answer": "25.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [2.0, 4.0, 23.0]", "Extracted entities: {'jessica': 4.0, 'roses': 2.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('2', 'roses')", "Calculation template: roses = 2.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-188", "problem": "Last week <PERSON> had 114 dollars and <PERSON> had 22 dollars. They washed cars over the weekend and now <PERSON> has 21 dollars and <PERSON> has 78 dollars. How much money did <PERSON> make over the weekend?", "expected_answer": "56.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [114.0, 22.0, 21.0, 78.0]", "Extracted entities: {'fred': 21.0, 'jason': 78.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('<PERSON>', '114')", "Calculation template: <PERSON> = 114.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-313", "problem": "A waiter had 11 customers. After some left he still had 3 customers. How many more customers left than those that stayed behind?", "expected_answer": "5.0", "predicted_answer": "8.0", "reasoning_steps": ["Extracted numbers: [11.0, 3.0]", "Extracted entities: {'waiter': 11.0, 'still': 3.0, 'customers': 3.0}", "'How many more' pattern: 11.0 - 3.0 = 8.0", "Direct arithmetic result: 8.0"]}, {"id": "chal-679", "problem": "<PERSON> had 23 crackers. He has 11 crackers left after he gave equal numbers of crackers to his 2 friends. How many crackers did each friend eat?", "expected_answer": "6.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [23.0, 11.0, 2.0]", "Extracted entities: {'matthew': 23.0, 'he': 11.0, 'crackers': 11.0, 'friends': 2.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('<PERSON>', '23')", "Calculation template: Matthew = 23.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-402", "problem": "For <PERSON>'s birthday she received 8 dollars from her mom. Her dad gave her 5 more dollars. If she spent 4 dollars. How much more money did she receive from her mom than she did from her dad?", "expected_answer": "3.0", "predicted_answer": "9.0", "reasoning_steps": ["Extracted numbers: [8.0, 5.0, 4.0]", "Extracted entities: {'she': 8.0}", "Received+Gave-Spent: 8.0 + 5.0 - 4.0 = 9.0", "Direct arithmetic result: 9.0"]}, {"id": "chal-490", "problem": "After <PERSON> visited a supermarket there were 29 dollars left. If there were 54 dollars in her wallet initially How much did she spend?", "expected_answer": "25.0", "predicted_answer": "-25.0", "reasoning_steps": ["Extracted numbers: [29.0, 54.0]", "Extracted entities: {'dollars': 54.0}", "Spending pattern: 29.0 - 54.0 = -25.0", "Direct arithmetic result: -25.0"]}, {"id": "chal-603", "problem": "The Razorback shop makes $ 115 dollars off each jersey and $ 25 off each t-shirt. During the Arkansas and Texas tech game they sold 113 t-shirts and 78 jerseys. How much more does a jersey cost than a t-shirt?", "expected_answer": "90.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [115.0, 25.0, 113.0, 78.0]", "Extracted entities: {'they': 113.0, 'off': 25.0, 't': 113.0, 'jerseys': 78.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-512", "problem": "<PERSON> got a box of 521 crayons and 66 erasers for his birthday. At the end of the school year he only had 154 left while not having lost a single erasers. How many crayons had been lost or given away?", "expected_answer": "367.0", "predicted_answer": "587.0", "reasoning_steps": ["Extracted numbers: [521.0, 66.0, 154.0]", "Extracted entities: {'only': 154.0, 'crayons': 521.0, 'erasers': 66.0, 'left': 154.0}", "Given+Lost pattern: 521.0 + 66.0 = 587.0", "Direct arithmetic result: 587.0"]}, {"id": "chal-472", "problem": "There are 896 skittles in <PERSON>'s skittles collection. <PERSON> also has 517 erasers and 90 scales. If the skittles are organized into 8 groups How big is each group?", "expected_answer": "112.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [896.0, 517.0, 90.0, 8.0]", "Extracted entities: {'also': 517.0, 'skittles': 896.0, 'erasers': 517.0, 'scales': 90.0, 'groups': 8.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('896', 'skittles')", "Calculation template: skittles = 896.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-140", "problem": "<PERSON> cut some roses from her flower garden to put in her vase. There are now 19 roses in the vase. If there were 3 roses in the vase initially How many roses did she cut?", "expected_answer": "16.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [19.0, 3.0]", "Extracted entities: {'roses': 3.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('3', 'roses')", "Calculation template: roses = 3.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-436", "problem": "<PERSON> scored 12 points in each game. If she scored a total of 36 points How many games did she play?", "expected_answer": "3.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [12.0, 36.0]", "Extracted entities: {'melissa': 12.0, 'points': 36.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-178", "problem": "The Razorback t-shirt shop makes $ 98 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 163 t-shirts. If they sold 89 t-shirts during the Arkansas game How much money did they make from selling the t-shirts during the arkansas game?", "expected_answer": "8722.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [98.0, 163.0, 89.0]", "Extracted entities: {'they': 89.0, 't': 89.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-586", "problem": "<PERSON> played tag with 2 kids on monday, 14 kids on tuesday and 16 kids on wednesday. How many kids did she play with on tuesday and wednesday?", "expected_answer": "30.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [2.0, 14.0, 16.0]", "Extracted entities: {'kids': 16.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-902", "problem": "<PERSON> grew 9 trees in her backyard. After a typhoon 4 died. Then she grew 5 more trees. How many trees does she have left?", "expected_answer": "10.0", "predicted_answer": "0.0", "reasoning_steps": ["Extracted numbers: [9.0, 4.0, 5.0]", "Extracted entities: {'trees': 9.0, 'died': 4.0}", "Remaining pattern: 9.0 - 9.0 = 0.0", "Direct arithmetic result: 0.0"]}, {"id": "chal-82", "problem": "<PERSON>'s mother made 14 cookies for 2 guests. If each of them had the same number of cookies How many did each of them have?", "expected_answer": "7.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [14.0, 2.0]", "Extracted entities: {'cookies': 14.0, 'guests': 2.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-275", "problem": "<PERSON>'s mother made cookies for 10 guests but 9 guests did not come. If she prepared 18 cookies and each guest had the same number of cookies How many did each of them have?", "expected_answer": "18.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 9.0, 18.0]", "Extracted entities: {'guests': 9.0, 'cookies': 18.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-546", "problem": "Dan has $ 4. For a total of $ 3 he bought 10 candy bar each one costing the same amount of money. How much money is left?", "expected_answer": "1.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 3.0, 10.0]", "Extracted entities: {'he': 3.0, 'candy': 10.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-55", "problem": "A waiter had 12 customers. After some left he still had 14 customers. Then he got 10 new customers How many customers does he have now?", "expected_answer": "24.0", "predicted_answer": "-12.0", "reasoning_steps": ["Extracted numbers: [12.0, 14.0, 10.0]", "Extracted entities: {'waiter': 12.0, 'still': 14.0, 'he': 10.0, 'customers': 14.0, 'new': 10.0}", "Remaining pattern: 12.0 - 24.0 = -12.0", "Direct arithmetic result: -12.0"]}, {"id": "chal-425", "problem": "The Razorback t-shirt shop sells each t-shirt for $ 51 dollars. During the Arkansas and Texas tech game they offered a discount of $ 8 per t-shirt and sold 130 t-shirts. How much money did they make from selling the t-shirts?", "expected_answer": "5590.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [51.0, 8.0, 130.0]", "Extracted entities: {'and': 130.0, 'per': 8.0, 't': 130.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-126", "problem": "<PERSON> has 22 nintendo games. How many does she need to buy so that she will have 140 games left?", "expected_answer": "118.0", "predicted_answer": "-118.0", "reasoning_steps": ["Extracted numbers: [22.0, 140.0]", "Extracted entities: {'kelly': 22.0, 'nintendo': 22.0, 'games': 140.0}", "Remaining pattern: 22.0 - 140.0 = -118.0", "Direct arithmetic result: -118.0"]}, {"id": "chal-917", "problem": "<PERSON> made 134 pastries and 11 cakes. If he sold 140 cakes and 92 pastries How many more pastries than cakes did baker make?", "expected_answer": "123.0", "predicted_answer": "129.0", "reasoning_steps": ["Extracted numbers: [134.0, 11.0, 140.0, 92.0]", "Extracted entities: {'he': 140.0, 'pastries': 92.0, 'cakes': 140.0}", "'How many more' pattern: 140.0 - 11.0 = 129.0", "Direct arithmetic result: 129.0"]}, {"id": "chal-494", "problem": "<PERSON> had 51 books and 106 pens. After selling some books and pens in a garage sale he had 82 books and 14 pens left. How many pens did he sell in the garage sale?", "expected_answer": "92.0", "predicted_answer": "-151.0", "reasoning_steps": ["Extracted numbers: [51.0, 106.0, 82.0, 14.0]", "Extracted entities: {'paul': 51.0, 'he': 82.0, 'books': 82.0, 'pens': 14.0}", "Remaining pattern: 51.0 - 202.0 = -151.0", "Direct arithmetic result: -151.0"]}, {"id": "chal-529", "problem": "<PERSON> received 3 emails in the afternoon, 6 emails in the morning and some more in the evening. If he received a total of 10 emails in the day How many emails did jack receive in the evening?", "expected_answer": "1.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [3.0, 6.0, 10.0]", "Extracted entities: {'jack': 3.0, 'emails': 10.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-302", "problem": "<PERSON> is baking a cake. The recipe calls for 14 cups of flour and 6 cups of sugar. She already put in 5 cups of flour. How many more cups of flour than cups of sugar does she need to add now?", "expected_answer": "3.0", "predicted_answer": "9.0", "reasoning_steps": ["Extracted numbers: [14.0, 6.0, 5.0]", "Extracted entities: {'cups': 5.0}", "'How many more' pattern: 14.0 - 5.0 = 9.0", "Direct arithmetic result: 9.0"]}, {"id": "chal-102", "problem": "<PERSON> lost 11 marbles. If he had 19 marbles in his collection earlier How many marbles does he have now?", "expected_answer": "8.0", "predicted_answer": "-8.0", "reasoning_steps": ["Extracted numbers: [11.0, 19.0]", "Extracted entities: {'josh': 11.0, 'he': 19.0, 'marbles': 19.0}", "Loss pattern: 11.0 - 19.0 = -8.0", "Direct arithmetic result: -8.0"]}, {"id": "chal-98", "problem": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 19 inches. The frog jumped 10 inches farther than the grasshopper and the mouse jumped 20 inches farther than the frog. How much farther did the mouse jump than the grasshopper?", "expected_answer": "30.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [19.0, 10.0, 20.0]", "Extracted entities: {'inches': 20.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-284", "problem": "<PERSON> had 9 action figures on a shelf in his room. Later he added 7 more action figures to the shelf. If he also has 10 books on the shelf How many more action figures than books were on his shelf?", "expected_answer": "6.0", "predicted_answer": "3.0", "reasoning_steps": ["Extracted numbers: [9.0, 7.0, 10.0]", "Extracted entities: {'jerry': 9.0, 'also': 10.0, 'action': 9.0, 'books': 10.0}", "'How many more' pattern: 10.0 - 7.0 = 3.0", "Direct arithmetic result: 3.0"]}, {"id": "chal-469", "problem": "<PERSON> was sending out birthday invitations to her friends. Each package of invitations she bought had 3 invitations in it and she bought 2 packs. If she wants to invite 9 friends How many extra invitations will she need to buy?", "expected_answer": "3.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [3.0, 2.0, 9.0]", "Extracted entities: {'bought': 3.0, 'she': 2.0, 'invitations': 3.0, 'packs': 2.0, 'friends': 9.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('bought', '3')", "Calculation template: bought = 3.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-741", "problem": "He had a total of 40 saltwater animals in different aquariums. Each aquarium has 2 animals in it. How many aquariums did he have?", "expected_answer": "20.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [40.0, 2.0]", "Extracted entities: {'aquarium': 2.0, 'saltwater': 40.0, 'animals': 2.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-206", "problem": "<PERSON> took a look at his books and magazines. If he has 9 books and 46 magazines in each of his 10 bookshelves How many magazines does he have in total?", "expected_answer": "460.0", "predicted_answer": "65.0", "reasoning_steps": ["Extracted numbers: [9.0, 46.0, 10.0]", "Extracted entities: {'he': 9.0, 'books': 9.0, 'magazines': 46.0, 'bookshelves': 10.0}", "Total/sum pattern: 9.0 + 46.0 + 10.0 = 65.0", "Direct arithmetic result: 65.0"]}, {"id": "chal-323", "problem": "3 birds and 4 storks were sitting on the fence. 6 more storks came to join them. How many birds and storks are sitting on the fence?", "expected_answer": "13.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [3.0, 4.0, 6.0]", "Extracted entities: {'birds': 3.0, 'storks': 4.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-341", "problem": "<PERSON> received 3 emails in the afternoon, 5 emails in the morning and 16 emails in the evening. How many more emails did <PERSON> receive in the morning than in the afternoon?", "expected_answer": "2.0", "predicted_answer": "13.0", "reasoning_steps": ["Extracted numbers: [3.0, 5.0, 16.0]", "Extracted entities: {'jack': 3.0, 'emails': 16.0}", "'How many more' pattern: 16.0 - 3.0 = 13.0", "Direct arithmetic result: 13.0"]}, {"id": "chal-791", "problem": "<PERSON> did 46 push-ups and 58 crunches in gym class today. <PERSON> did 38 more push-ups but 62 less crunches than zach<PERSON>. How many more crunches than push-ups did <PERSON> do?", "expected_answer": "12.0", "predicted_answer": "24.0", "reasoning_steps": ["Extracted numbers: [46.0, 58.0, 38.0, 62.0]", "Extracted entities: {'push': 46.0, 'crunches': 58.0}", "'How many more' pattern: 62.0 - 38.0 = 24.0", "Direct arithmetic result: 24.0"]}, {"id": "chal-374", "problem": "6 red peaches, 90 yellow peaches and 16 green peaches are in the basket. How many red and green peaches are in the basket?", "expected_answer": "22.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [6.0, 90.0, 16.0]", "Extracted entities: {'red': 6.0, 'yellow': 90.0, 'green': 16.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-173", "problem": "<PERSON> had some pieces of candy. If he put them into 26 bags with 33 pieces in each bag How many pieces of candy did he have?", "expected_answer": "858.0", "predicted_answer": "1.2692307692307692", "reasoning_steps": ["Extracted numbers: [26.0, 33.0]", "Extracted entities: {'bags': 26.0, 'pieces': 33.0}", "Division pattern (each): 33.0 / 26.0 = 1.2692307692307692", "Direct arithmetic result: 1.2692307692307692"]}, {"id": "chal-705", "problem": "If they are already at 659 feet and the cave is 762 feet deep How much farther until they reach the end of the cave?", "expected_answer": "103.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [659.0, 762.0]", "Extracted entities: {'feet': 762.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-539", "problem": "<PERSON> had some marbles in his collection. He lost 21 marbles. If he has 12 marbles now How many marbles did he have in his collection?", "expected_answer": "33.0", "predicted_answer": "9.0", "reasoning_steps": ["Extracted numbers: [21.0, 12.0]", "Extracted entities: {'he': 12.0, 'marbles': 12.0}", "Loss pattern: 21.0 - 12.0 = 9.0", "Direct arithmetic result: 9.0"]}, {"id": "chal-8", "problem": "<PERSON><PERSON> had 41 cookies. He gave 9 cookies to his friend and ate 18 cookies. How many more cookies did he eat than those he gave to his friend?", "expected_answer": "9.0", "predicted_answer": "32.0", "reasoning_steps": ["Extracted numbers: [41.0, 9.0, 18.0]", "Extracted entities: {'paco': 41.0, 'he': 9.0, 'cookies': 18.0}", "'How many more' pattern: 41.0 - 9.0 = 32.0", "Direct arithmetic result: 32.0"]}, {"id": "chal-725", "problem": "He then went to see the oranges being harvested. He found out that they harvest 66 sacks per day and that each sack containes 25 oranges. How many oranges will they have after 87 days of harvest?", "expected_answer": "143550.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [66.0, 25.0, 87.0]", "Extracted entities: {'sacks': 66.0, 'oranges': 25.0, 'days': 87.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-489", "problem": "<PERSON> has 21 packages of gum and 45 packages of candy. There are 9 pieces in each package. How many pieces of candies does <PERSON> have?", "expected_answer": "405.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [21.0, 45.0, 9.0]", "Extracted entities: {'robin': 21.0, 'packages': 45.0, 'pieces': 9.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('9', 'pieces')", "Calculation template: pieces = 9.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-163", "problem": "<PERSON><PERSON> had 40 sweet cookies and 25 salty cookies. He ate 28 salty cookies and 15 sweet cookies. How many more salty cookies than sweet cookies did he eat?", "expected_answer": "13.0", "predicted_answer": "25.0", "reasoning_steps": ["Extracted numbers: [40.0, 25.0, 28.0, 15.0]", "Extracted entities: {'paco': 40.0, 'sweet': 15.0, 'salty': 28.0}", "'How many more' pattern: 40.0 - 15.0 = 25.0", "Direct arithmetic result: 25.0"]}, {"id": "chal-841", "problem": "<PERSON> has $ 4. He bought a chocolate for $ 7 and a candy bar for $ 2. How much money did he spend to buy chocolate than he did to buy candy bar?", "expected_answer": "5.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 7.0, 2.0]", "Extracted entities: {'and': 7.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-37", "problem": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 703 visitors came to the Buckingham palace on the previous day. If there were 246 visitors on that day How many visited the Buckingham palace within 25 days?", "expected_answer": "949.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [703.0, 246.0, 25.0]", "Extracted entities: {'visitors': 246.0, 'days': 25.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('246', 'visitors')", "Calculation template: visitors = 246.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-999", "problem": "<PERSON> is baking a cake. The recipe calls for 7 cups of sugar and 10 cups of flour. She already put in 4 cups of sugar. How many more cups of sugar does she need to add?", "expected_answer": "3.0", "predicted_answer": "6.0", "reasoning_steps": ["Extracted numbers: [7.0, 10.0, 4.0]", "Extracted entities: {'cups': 4.0}", "'How many more' pattern: 10.0 - 4.0 = 6.0", "Direct arithmetic result: 6.0"]}, {"id": "chal-763", "problem": "<PERSON> has 11 fewer peaches than <PERSON>. If <PERSON> has 17 peaches. How many peaches does <PERSON> have?", "expected_answer": "28.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [11.0, 17.0]", "Extracted entities: {'jake': 17.0, 'fewer': 11.0, 'peaches': 17.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-58", "problem": "Winter is almost here and most animals are migrating to warmer countries. There were 89 bird families living near the mountain. If 60 bird families flew away for winter How many more bird families flew away for the winter than those that stayed behind?", "expected_answer": "31.0", "predicted_answer": "29.0", "reasoning_steps": ["Extracted numbers: [89.0, 60.0]", "Extracted entities: {'bird': 89.0}", "'How many more' pattern: 89.0 - 60.0 = 29.0", "Direct arithmetic result: 29.0"]}, {"id": "chal-569", "problem": "<PERSON> had to complete 9 pages of math homework, 11 pages of reading homework and 29 more pages of biology homework. How many pages of math and reading homework did she have to complete?", "expected_answer": "20.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [9.0, 11.0, 29.0]", "Extracted entities: {'pages': 11.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-397", "problem": "For the walls of the house <PERSON> would use large planks of wood. Each plank needs 2 pieces of nails to be secured and he would use 16 planks. How many nails does <PERSON> need for the house wall?", "expected_answer": "32.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [2.0, 16.0]", "Extracted entities: {'pieces': 2.0, 'planks': 16.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-892", "problem": "7 red peaches, 71 yellow peaches and 8 green peaches are in the basket. How many more green peaches than red peaches are in the basket?", "expected_answer": "1.0", "predicted_answer": "64.0", "reasoning_steps": ["Extracted numbers: [7.0, 71.0, 8.0]", "Extracted entities: {'red': 7.0, 'yellow': 71.0, 'green': 8.0}", "'How many more' pattern: 71.0 - 7.0 = 64.0", "Direct arithmetic result: 64.0"]}, {"id": "chal-897", "problem": "<PERSON> has 28 packages of gum and 14 packages of candy. There are 6 pieces in each package. How many pieces does <PERSON> have?", "expected_answer": "7.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [28.0, 14.0, 6.0]", "Extracted entities: {'robin': 28.0, 'packages': 14.0, 'pieces': 6.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('6', 'pieces')", "Calculation template: pieces = 6.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-214", "problem": "There were 12 roses and 2 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 10 roses and 21 orchids in the vase. How many orchids did she cut?", "expected_answer": "19.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [12.0, 2.0, 10.0, 21.0]", "Extracted entities: {'roses': 12.0, 'orchids': 21.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('12', 'roses')", "Calculation template: roses = 12.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-659", "problem": "4 birds and 6 storks were sitting on the fence. 2 more storks came to join them. How many storks are sitting on the fence?", "expected_answer": "8.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 6.0, 2.0]", "Extracted entities: {'birds': 4.0, 'storks': 6.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-410", "problem": "<PERSON> collects baseball cards. She gave 301 of her cards to <PERSON> and now has 154 cards left.. How many cards did <PERSON> have initially?", "expected_answer": "455.0", "predicted_answer": "147.0", "reasoning_steps": ["Extracted numbers: [301.0, 154.0]", "Extracted entities: {'she': 301.0, 'now': 154.0, 'of': 301.0, 'cards': 154.0}", "Remaining pattern: 301.0 - 154.0 = 147.0", "Direct arithmetic result: 147.0"]}, {"id": "chal-638", "problem": "<PERSON> wants to split a collection of eggs into 4 groups. <PERSON> has 8 eggs and 6 marbles. How many eggs will each group have?", "expected_answer": "2.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 8.0, 6.0]", "Extracted entities: {'rebecca': 8.0, 'groups': 4.0, 'eggs': 8.0, 'marbles': 6.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-682", "problem": "<PERSON> collects bottle caps and wrappers. He found 15 bottle caps and 18 wrappers at the park. Now he has 67 wrappers and 35 bottle caps in his collection. How many more wrappers than bottle caps does danny have now?", "expected_answer": "32.0", "predicted_answer": "52.0", "reasoning_steps": ["Extracted numbers: [15.0, 18.0, 67.0, 35.0]", "Extracted entities: {'he': 67.0, 'bottle': 35.0, 'wrappers': 67.0}", "'How many more' pattern: 67.0 - 15.0 = 52.0", "Direct arithmetic result: 52.0"]}, {"id": "chal-11", "problem": "3 birds were sitting on the fence. 6 more storks and 2 more birds came to join them. How many more storks than birds are sitting on the fence?", "expected_answer": "1.0", "predicted_answer": "4.0", "reasoning_steps": ["Extracted numbers: [3.0, 6.0, 2.0]", "Extracted entities: {'birds': 3.0}", "'How many more' pattern: 6.0 - 2.0 = 4.0", "Direct arithmetic result: 4.0"]}, {"id": "chal-356", "problem": "<PERSON> could fit 10 action figures on each shelf in his room. His room has could hold 8 action figures. How many total shelves did his room have?", "expected_answer": "80.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 8.0]", "Extracted entities: {'action': 8.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-798", "problem": "A mailman gives 2 junk mails to each house in a block. If the mailman has to give 14 pieces of junk mail to each block. How many houses are there in a block?", "expected_answer": "7.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [2.0, 14.0]", "Extracted entities: {'junk': 2.0, 'pieces': 14.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-405", "problem": "<PERSON><PERSON> had 39 sweet cookies and 6 salty cookies. He ate 23 salty cookies and 32 sweet cookies. How many more sweet cookies than salty cookies did he eat?", "expected_answer": "9.0", "predicted_answer": "33.0", "reasoning_steps": ["Extracted numbers: [39.0, 6.0, 23.0, 32.0]", "Extracted entities: {'paco': 39.0, 'sweet': 32.0, 'salty': 23.0}", "'How many more' pattern: 39.0 - 6.0 = 33.0", "Direct arithmetic result: 33.0"]}, {"id": "chal-585", "problem": "<PERSON> weighed 92 kilograms. After she started to go jogging everyday she lost 56 kilograms in the first week and 99 kilograms in the second week. How much did she weigh after the first week of jogging?", "expected_answer": "36.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [92.0, 56.0, 99.0]", "Extracted entities: {'she': 56.0, 'kilograms': 99.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-599", "problem": "<PERSON> had 10 more marbles than <PERSON>. <PERSON> lost 11 of his marbles at the playground. If <PERSON> had 45 marbles How many more marbles did <PERSON> have than <PERSON> then?", "expected_answer": "21.0", "predicted_answer": "35.0", "reasoning_steps": ["Extracted numbers: [10.0, 11.0, 45.0]", "Extracted entities: {'ed': 45.0, 'doug': 11.0, 'of': 11.0, 'marbles': 45.0}", "'How many more' pattern: 45.0 - 10.0 = 35.0", "Direct arithmetic result: 35.0"]}, {"id": "chal-250", "problem": "There are 7 baskets of peaches. Each basket has 10 red peaches and 2 green peaches. How many green peaches are in the baskets altogether?", "expected_answer": "14.0", "predicted_answer": "19.0", "reasoning_steps": ["Extracted numbers: [7.0, 10.0, 2.0]", "Extracted entities: {'basket': 10.0, 'baskets': 7.0, 'red': 10.0, 'green': 2.0}", "Total/sum pattern: 7.0 + 10.0 + 2.0 = 19.0", "Direct arithmetic result: 19.0"]}, {"id": "chal-148", "problem": "Next on his checklist is wax to stick the feathers together. He needs 159 g of wax more. If the feathers require a total of 628 g of wax How many grams of wax does he already have?", "expected_answer": "469.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [159.0, 628.0]", "Extracted entities: {'g': 628.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-115", "problem": "<PERSON> was placing her pencils into rows with 22 pencils in each row. She had 6 packs of pencils each one having 14 pencils. How many pencils does she have?", "expected_answer": "84.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [22.0, 6.0, 14.0]", "Extracted entities: {'she': 6.0, 'pencils': 14.0, 'packs': 6.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('She', '6')", "Calculation template: She = 6.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-810", "problem": "<PERSON> has 4 more apples than <PERSON>. Together <PERSON> and <PERSON> have 14 apples. <PERSON> has 6 apples more than <PERSON> and <PERSON> together do. How many apples does <PERSON> have?", "expected_answer": "20.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 14.0, 6.0]", "Extracted entities: {'adam': 4.0, 'bob': 6.0, 'apples': 6.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-656", "problem": "<PERSON> has 6 fewer peaches than <PERSON>. <PERSON> has 18 more peaches than <PERSON>. If jill has 5 peaches How many peaches does <PERSON> have?", "expected_answer": "17.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [6.0, 18.0, 5.0]", "Extracted entities: {'jake': 6.0, 'steven': 18.0, 'jill': 5.0, 'fewer': 6.0, 'peaches': 5.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-225", "problem": "<PERSON> wants to split a collection of eggs into groups of 3. <PERSON> has 99 bananas 9 eggs and 27 marbles. How many groups will be created?", "expected_answer": "3.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [3.0, 99.0, 9.0, 27.0]", "Extracted entities: {'rebecca': 99.0, 'bananas': 99.0, 'eggs': 9.0, 'marbles': 27.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-177", "problem": "<PERSON> has 12 peaches. <PERSON> has 4 fewer peaches than <PERSON> who has 67 more peaches than <PERSON>. How many peaches does <PERSON> have?", "expected_answer": "8.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [12.0, 4.0, 67.0]", "Extracted entities: {'steven': 12.0, 'jake': 4.0, 'who': 67.0, 'peaches': 12.0, 'fewer': 4.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-731", "problem": "<PERSON> had 21 books. After selling some in a garage sale he bought 42 new ones. If he has 15 books now How many more books did he sell than he bought?", "expected_answer": "6.0", "predicted_answer": "27.0", "reasoning_steps": ["Extracted numbers: [21.0, 42.0, 15.0]", "Extracted entities: {'paul': 21.0, 'he': 15.0, 'books': 15.0, 'new': 42.0}", "'How many more' pattern: 42.0 - 15.0 = 27.0", "Direct arithmetic result: 27.0"]}, {"id": "chal-390", "problem": "<PERSON><PERSON> bought 200 water bottles and 256 soda bottles when they were on sale. If she drank 312 water bottles and 4 soda bottles a day How many days would the soda bottles last?", "expected_answer": "64.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [200.0, 256.0, 312.0, 4.0]", "Extracted entities: {'debby': 200.0, 'water': 312.0, 'soda': 4.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-886", "problem": "<PERSON> played tag with 5 kids on monday. She played tag with some more kids on tuesday. If she played with a total of 15 kids How many kids did she play with on tuesday?", "expected_answer": "10.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [5.0, 15.0]", "Extracted entities: {'kids': 15.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-615", "problem": "Next on his checklist is wax to stick the feathers together. If he has 557 g of wax and right now he just needs 17 g Total how many grams of wax do the feathers require?", "expected_answer": "574.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [557.0, 17.0]", "Extracted entities: {'he': 557.0, 'g': 17.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-643", "problem": "The school has 304 grades and each grade has 75 students How many students were there in total?", "expected_answer": "22800.0", "predicted_answer": "379.0", "reasoning_steps": ["Extracted numbers: [304.0, 75.0]", "Extracted entities: {'school': 304.0, 'grade': 75.0, 'grades': 304.0, 'students': 75.0}", "Total/sum pattern: 304.0 + 75.0 = 379.0", "Direct arithmetic result: 379.0"]}, {"id": "chal-476", "problem": "There are 10 peaches distributed equally in some baskets. If each basket has 4 red peaches and 6 green peaches How many baskets of peaches are there?", "expected_answer": "1.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 4.0, 6.0]", "Extracted entities: {'basket': 4.0, 'peaches': 10.0, 'red': 4.0, 'green': 6.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('10', 'peaches')", "Calculation template: peaches = 10.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-814", "problem": "A book has 2 chapters. The first chapter is 60 pages long. If there are a total of 93 pages in the book How many pages are in the second chapter?", "expected_answer": "33.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [2.0, 60.0, 93.0]", "Extracted entities: {'book': 2.0, 'chapters': 2.0, 'pages': 93.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-727", "problem": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 90 chocolate chip cookies yesterday and 51 raisin cookies and 484 chocolate chip cookies this morning. How many chocolate chip cookies did <PERSON> bake?", "expected_answer": "574.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [90.0, 51.0, 484.0]", "Extracted entities: {'chocolate': 484.0, 'raisin': 51.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-926", "problem": "<PERSON> collects cards. She had 438 baseball cards and 18 Ace cards. She gave some of her cards to <PERSON> and now has 55 Ace cards and 178 baseball cards left. How many more baseball cards than Ace cards does <PERSON> have?", "expected_answer": "123.0", "predicted_answer": "420.0", "reasoning_steps": ["Extracted numbers: [438.0, 18.0, 55.0, 178.0]", "Extracted entities: {'she': 438.0, 'now': 55.0, 'baseball': 178.0, 'ace': 55.0}", "'How many more' pattern: 438.0 - 18.0 = 420.0", "Direct arithmetic result: 420.0"]}, {"id": "chal-7", "problem": "<PERSON> had some action figures on a shelf in his room. Later he added 7 more action figures to the shelf. If there are a total of 10 action figures on his shelf now How many action figures did he have initially on the shelf?", "expected_answer": "3.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [7.0, 10.0]", "Extracted entities: {'action': 10.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-260", "problem": "<PERSON> gives away 91 nintendo games. How many did she have initially if she still has 92 games left?", "expected_answer": "183.0", "predicted_answer": "-1.0", "reasoning_steps": ["Extracted numbers: [91.0, 92.0]", "Extracted entities: {'still': 92.0, 'nintendo': 91.0, 'games': 92.0}", "Remaining pattern: 91.0 - 92.0 = -1.0", "Direct arithmetic result: -1.0"]}, {"id": "chal-575", "problem": "<PERSON> was placing her pencils and crayons into 30 rows with 71 crayons and 24 pencils in each row. How many pencils does she have?", "expected_answer": "720.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [30.0, 71.0, 24.0]", "Extracted entities: {'rows': 30.0, 'crayons': 71.0, 'pencils': 24.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-292", "problem": "<PERSON> has 18 fewer peaches than <PERSON> who has 13 more peaches than <PERSON>. <PERSON> has 19 peaches. How many peaches does <PERSON> have?", "expected_answer": "6.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [18.0, 13.0, 19.0]", "Extracted entities: {'jake': 18.0, 'who': 13.0, 'steven': 19.0, 'fewer': 18.0, 'peaches': 19.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-319", "problem": "<PERSON> had 3 books and 4 action figures on a shelf in his room. Later he added 2 more action figures to the shelf. How many more action figures than books were on his shelf?", "expected_answer": "3.0", "predicted_answer": "2.0", "reasoning_steps": ["Extracted numbers: [3.0, 4.0, 2.0]", "Extracted entities: {'jerry': 3.0, 'books': 3.0, 'action': 4.0}", "'How many more' pattern: 4.0 - 2.0 = 2.0", "Direct arithmetic result: 2.0"]}, {"id": "chal-938", "problem": "<PERSON> had 10 apps on his phone. He added 11 new apps. After deleting some he had 4 left. How many apps did he delete?", "expected_answer": "17.0", "predicted_answer": "-5.0", "reasoning_steps": ["Extracted numbers: [10.0, 11.0, 4.0]", "Extracted entities: {'dave': 10.0, 'he': 4.0, 'apps': 10.0, 'new': 11.0, 'left': 4.0}", "Remaining pattern: 10.0 - 15.0 = -5.0", "Direct arithmetic result: -5.0"]}, {"id": "chal-474", "problem": "<PERSON> had 95 pens and 153 books. After selling some books and pens in a garage sale he had 13 books and 23 pens left. How many books did he sell in the garage sale?", "expected_answer": "140.0", "predicted_answer": "-94.0", "reasoning_steps": ["Extracted numbers: [95.0, 153.0, 13.0, 23.0]", "Extracted entities: {'paul': 95.0, 'he': 13.0, 'pens': 23.0, 'books': 13.0}", "Remaining pattern: 95.0 - 189.0 = -94.0", "Direct arithmetic result: -94.0"]}, {"id": "chal-831", "problem": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 324 students suggested adding mashed potatoes 374 suggested adding bacon to the menu and 128 suggested adding tomatoes How many students participated in the suggestion of new food items?", "expected_answer": "826.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [324.0, 374.0, 128.0]", "Extracted entities: {'students': 324.0, 'suggested': 128.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-404", "problem": "<PERSON> had 16 apps and 77 files on his phone. After deleting some apps and files he had 5 apps and 23 files left. How many apps did he delete?", "expected_answer": "11.0", "predicted_answer": "-89.0", "reasoning_steps": ["Extracted numbers: [16.0, 77.0, 5.0, 23.0]", "Extracted entities: {'dave': 16.0, 'he': 5.0, 'apps': 5.0, 'files': 23.0}", "Remaining pattern: 16.0 - 105.0 = -89.0", "Direct arithmetic result: -89.0"]}, {"id": "chal-300", "problem": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 25 inches. The frog jumped 18 inches farther than the grasshopper and the mouse jumped 2 inches farther than the frog. How far did the mouse jump?", "expected_answer": "45.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [25.0, 18.0, 2.0]", "Extracted entities: {'inches': 2.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-630", "problem": "After a typhoon, 2 trees in <PERSON>'s backyard died. If she had grown 12 trees initially How many trees does she have left?", "expected_answer": "10.0", "predicted_answer": "-10.0", "reasoning_steps": ["Extracted numbers: [2.0, 12.0]", "Extracted entities: {'trees': 12.0}", "Remaining pattern: 2.0 - 12.0 = -10.0", "Direct arithmetic result: -10.0"]}, {"id": "chal-418", "problem": "<PERSON> brought 7 balloons and 5 balls while <PERSON> brought 6 balloons and 4 balls to the park. How many balls did <PERSON> and <PERSON> have in the park?", "expected_answer": "9.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [7.0, 5.0, 6.0, 4.0]", "Extracted entities: {'balloons': 6.0, 'balls': 4.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-662", "problem": "The Razorback t-shirt shop sells each t-shirt for $ 201 dollars. During the Arkansas and Texas tech game they increased the prices by $ 217 per t-shirt and sold 14 t-shirts. How much money did they make from selling the t-shirts?", "expected_answer": "5852.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [201.0, 217.0, 14.0]", "Extracted entities: {'and': 14.0, 'per': 217.0, 't': 14.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-180", "problem": "<PERSON> has 5 fewer peaches than <PERSON>. <PERSON> has 18 more peaches than <PERSON>. If <PERSON> has 87 peaches How many more peaches does <PERSON> have than <PERSON>?", "expected_answer": "13.0", "predicted_answer": "82.0", "reasoning_steps": ["Extracted numbers: [5.0, 18.0, 87.0]", "Extracted entities: {'jake': 5.0, 'steven': 18.0, 'jill': 87.0, 'fewer': 5.0, 'peaches': 87.0}", "'How many more' pattern: 87.0 - 5.0 = 82.0", "Direct arithmetic result: 82.0"]}, {"id": "chal-672", "problem": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 36 of the pickers that they fill 8 drums of grapes per day. How many days will it take to fill 240 drums of grapes?", "expected_answer": "30.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [36.0, 8.0, 240.0]", "Extracted entities: {'of': 36.0, 'drums': 240.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-551", "problem": "<PERSON> has some packages of gum. There are 7 pieces in each package. <PERSON> has 6 extra pieces of gum. In all the number of pieces of gums robin has is 41. How many packages does <PERSON> have?", "expected_answer": "5.0", "predicted_answer": "54.0", "reasoning_steps": ["Extracted numbers: [7.0, 6.0, 41.0]", "Extracted entities: {'robin': 6.0, 'pieces': 7.0, 'extra': 6.0}", "Total/sum pattern: 7.0 + 6.0 + 41.0 = 54.0", "Direct arithmetic result: 54.0"]}, {"id": "chal-67", "problem": "An industrial machine made 9 shirts yesterday and 8 shirts today. It can make 2 shirts a minute. How many minutes did the machine work today?", "expected_answer": "4.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [9.0, 8.0, 2.0]", "Extracted entities: {'shirts': 2.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-777", "problem": "There are 87 oranges and 290 bananas in <PERSON>'s collection. If the bananas are organized into 2 groups and oranges are organized into 93 groups How big is each group of bananas?", "expected_answer": "145.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [87.0, 290.0, 2.0, 93.0]", "Extracted entities: {'oranges': 87.0, 'bananas': 290.0, 'groups': 93.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('87', 'oranges')", "Calculation template: oranges = 87.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-210", "problem": "<PERSON><PERSON> had 6 cookies. He gave 30 cookies to his friend and ate 23 cookies. How many more cookies did he give to his friend than those he ate?", "expected_answer": "7.0", "predicted_answer": "24.0", "reasoning_steps": ["Extracted numbers: [6.0, 30.0, 23.0]", "Extracted entities: {'paco': 6.0, 'he': 30.0, 'cookies': 23.0}", "'How many more' pattern: 30.0 - 6.0 = 24.0", "Direct arithmetic result: 24.0"]}, {"id": "chal-951", "problem": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 19 inches. The frog jumped 39 inches farther than the grasshopper and the mouse jumped 94 inches lesser than the frog. How far did the frog jump?", "expected_answer": "58.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [19.0, 39.0, 94.0]", "Extracted entities: {'inches': 94.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-121", "problem": "<PERSON> had to complete 7 pages of math homework. If she had to complete 4 more pages of math homework than reading homework How many pages of reading homework did she have to complete?", "expected_answer": "3.0", "predicted_answer": "11.0", "reasoning_steps": ["Extracted numbers: [7.0, 4.0]", "Extracted entities: {'pages': 7.0}", "'More than' addition: 7.0 + 4.0 = 11.0", "Direct arithmetic result: 11.0"]}, {"id": "chal-525", "problem": "A waiter had 3 customers. After some more arrived he had 8 customers. How many new customers arrived?", "expected_answer": "5.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [3.0, 8.0]", "Extracted entities: {'waiter': 3.0, 'he': 8.0, 'customers': 8.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('waiter', '3')", "Calculation template: waiter = 3.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-677", "problem": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 19 inches. The grasshopper jumped 4 inches farther than the frog and the mouse jumped 44 inches lesser than the frog. How far did the frog jump?", "expected_answer": "15.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [19.0, 4.0, 44.0]", "Extracted entities: {'inches': 44.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-821", "problem": "<PERSON> had some books. After selling 137 in a garage sale he had 105 left. How many books did he have at the start?", "expected_answer": "242.0", "predicted_answer": "32.0", "reasoning_steps": ["Extracted numbers: [137.0, 105.0]", "Extracted entities: {'he': 105.0, 'in': 137.0, 'left': 105.0}", "Remaining pattern: 137.0 - 105.0 = 32.0", "Direct arithmetic result: 32.0"]}, {"id": "chal-543", "problem": "<PERSON> did 53 push-ups and 14 crunches in gym class today. <PERSON> did 17 more push-ups but 10 less crunches than zach<PERSON>. How many push-ups and crunches did <PERSON> do?", "expected_answer": "67.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [53.0, 14.0, 17.0, 10.0]", "Extracted entities: {'push': 53.0, 'crunches': 14.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-742", "problem": "Every day <PERSON> spends 6 hours on learning english and 7 hours on learning chinese. If he learns for 5 days How many hours does he spend on learning english and chinese in all?", "expected_answer": "65.0", "predicted_answer": "18.0", "reasoning_steps": ["Extracted numbers: [6.0, 7.0, 5.0]", "Extracted entities: {'hours': 7.0, 'days': 5.0}", "Total/sum pattern: 6.0 + 7.0 + 5.0 = 18.0", "Direct arithmetic result: 18.0"]}, {"id": "chal-976", "problem": "Winter is almost here and most animals are migrating to warmer countries. There are some bird families living near the mountain. 20 bird families flew away for winter while 14 bird families stayed behind. How many bird families were living near the mountain at the start?", "expected_answer": "34.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [20.0, 14.0]", "Extracted entities: {'bird': 14.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-773", "problem": "<PERSON> received 10 emails and 12 letters in the morning. He then received 3 emails and 44 letters in the afternoon. How many more emails did <PERSON> receive in the morning than in the afternoon?", "expected_answer": "7.0", "predicted_answer": "41.0", "reasoning_steps": ["Extracted numbers: [10.0, 12.0, 3.0, 44.0]", "Extracted entities: {'jack': 10.0, 'then': 3.0, 'emails': 3.0, 'letters': 44.0}", "'How many more' pattern: 44.0 - 3.0 = 41.0", "Direct arithmetic result: 41.0"]}, {"id": "chal-149", "problem": "<PERSON> was reading through his favorite book. He read 8 pages per day. If the book had 576 pages How many days did he take to finish the book?", "expected_answer": "72.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [8.0, 576.0]", "Extracted entities: {'book': 576.0, 'pages': 576.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('book', '576')", "Calculation template: book = 576.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-500", "problem": "<PERSON><PERSON> bought 95 soda bottles and 180 water bottles when they were on sale. If she drank 15 water bottles and 54 soda bottles a day How many days would the water bottles last?", "expected_answer": "12.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [95.0, 180.0, 15.0, 54.0]", "Extracted entities: {'debby': 95.0, 'soda': 54.0, 'water': 15.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-200", "problem": "<PERSON><PERSON> bought 360 soda bottles and 162 water bottles when they were on sale. If she drank 122 water bottles and 9 soda bottles a day How many days would the soda bottles last?", "expected_answer": "40.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [360.0, 162.0, 122.0, 9.0]", "Extracted entities: {'debby': 360.0, 'soda': 9.0, 'water': 122.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-446", "problem": "A book has 31 chapters. Each chapter is 61 pages long. How many pages does the book have altogether?", "expected_answer": "1891.0", "predicted_answer": "92.0", "reasoning_steps": ["Extracted numbers: [31.0, 61.0]", "Extracted entities: {'book': 31.0, 'chapters': 31.0, 'pages': 61.0}", "Total/sum pattern: 31.0 + 61.0 = 92.0", "Direct arithmetic result: 92.0"]}, {"id": "chal-144", "problem": "<PERSON> was helping her mom plant flowers and they put 10 seeds in each flower bed. If they planted 60 seeds altogther How many flower beds did they have?", "expected_answer": "6.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 60.0]", "Extracted entities: {'seeds': 60.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-929", "problem": "They decided to hold the party in their backyard. They have 10 sets of tables and each set has 6 chairs. If there are 11 people sitting on chairs How many chairs are left unoccupied?", "expected_answer": "49.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 6.0, 11.0]", "Extracted entities: {'set': 6.0, 'sets': 10.0, 'chairs': 6.0, 'people': 11.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('11', 'people')", "Calculation template: people = 11.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-668", "problem": "<PERSON> is baking a cake. The recipe calls for 11 cups of sugar and some more cups of flour. She already put in 3 cups of flour. If she still needs to add 6 more cups of flour How many cups of flour did the recipe require?", "expected_answer": "9.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [11.0, 3.0, 6.0]", "Extracted entities: {'cups': 3.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-181", "problem": "For the walls of the house he would use 13 large planks of wood. If each plank of wood needs 17 pieces of nails to be secured and in addition 8 nails are needed for some smaller planks. How many nails does <PERSON> need for the house wall?", "expected_answer": "229.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [13.0, 17.0, 8.0]", "Extracted entities: {'large': 13.0, 'pieces': 17.0, 'nails': 8.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-950", "problem": "There were some roses in the vase. <PERSON> cut 16 more roses from her flower garden and put them in the vase. There are now 23 roses in the vase. How many roses were there in the vase at the beginning?", "expected_answer": "7.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [16.0, 23.0]", "Extracted entities: {'roses': 23.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-883", "problem": "<PERSON> brought 3 balloons and 20 balls while <PERSON> brought 5 balloons and 59 balls to the park. How many balloons did <PERSON> and <PERSON> have in the park?", "expected_answer": "8.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [3.0, 20.0, 5.0, 59.0]", "Extracted entities: {'balloons': 5.0, 'balls': 59.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-891", "problem": "A farmer had 177 tomatoes and 12 potatoes in his garden. If he picked 53 tomatoes How many tomatoes and potatoes does he have left?", "expected_answer": "136.0", "predicted_answer": "112.0", "reasoning_steps": ["Extracted numbers: [177.0, 12.0, 53.0]", "Extracted entities: {'farmer': 177.0, 'tomatoes': 53.0, 'potatoes': 12.0}", "Remaining pattern: 177.0 - 65.0 = 112.0", "Direct arithmetic result: 112.0"]}, {"id": "chal-986", "problem": "<PERSON> had to complete 11 pages of math homework, 2 pages of reading homework and 3 more pages of biology homework. How many more pages of math homework than biology homework did she have?", "expected_answer": "8.0", "predicted_answer": "9.0", "reasoning_steps": ["Extracted numbers: [11.0, 2.0, 3.0]", "Extracted entities: {'pages': 2.0}", "'How many more' pattern: 11.0 - 2.0 = 9.0", "Direct arithmetic result: 9.0"]}, {"id": "chal-526", "problem": "<PERSON>'s mother made cookies for guests. If she prepared 38 cookies and each of them had 19 cookies How many guests did she prepare cookies for?", "expected_answer": "2.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [38.0, 19.0]", "Extracted entities: {'them': 19.0, 'cookies': 19.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('them', '19')", "Calculation template: them = 19.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-278", "problem": "<PERSON> needs a carpet of size 10 square feet to cover her room. If her room is 2 feet wide What is the length of her room?", "expected_answer": "5.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 2.0]", "Extracted entities: {'square': 10.0, 'feet': 2.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-125", "problem": "<PERSON> is baking a cake. The recipe calls for 12 cups of sugar and 14 cups of flour. She already put in 10 cups of sugar. How many more cups of flour than cups of sugar does she need to add now?", "expected_answer": "12.0", "predicted_answer": "4.0", "reasoning_steps": ["Extracted numbers: [12.0, 14.0, 10.0]", "Extracted entities: {'cups': 10.0}", "'How many more' pattern: 14.0 - 10.0 = 4.0", "Direct arithmetic result: 4.0"]}, {"id": "chal-981", "problem": "10 red peaches and some more green peaches are in the basket. If there are a total of 15 peaches in the basket How many green peaches are in the basket?", "expected_answer": "5.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [10.0, 15.0]", "Extracted entities: {'red': 10.0, 'peaches': 15.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-751", "problem": "After <PERSON> started to go jogging everyday she lost 126 kilograms. She currently weighs 66 kilograms. How much did she weigh before starting to jog?", "expected_answer": "192.0", "predicted_answer": "60.0", "reasoning_steps": ["Extracted numbers: [126.0, 66.0]", "Extracted entities: {'she': 126.0, 'kilograms': 66.0}", "Loss pattern: 126.0 - 66.0 = 60.0", "Direct arithmetic result: 60.0"]}, {"id": "chal-801", "problem": "A waiter had 3 customers. After some left he still had 4 customers. How many more customers stayed behind than those that left?", "expected_answer": "5.0", "predicted_answer": "1.0", "reasoning_steps": ["Extracted numbers: [3.0, 4.0]", "Extracted entities: {'waiter': 3.0, 'still': 4.0, 'customers': 4.0}", "'How many more' pattern: 4.0 - 3.0 = 1.0", "Direct arithmetic result: 1.0"]}, {"id": "chal-348", "problem": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 527 chocolate chip cookies and 86 raisin cookies yesterday. And she baked 86 raisin cookies and 554 chocolate chip cookies this morning. How many chocolate chip cookies did <PERSON> bake?", "expected_answer": "1081.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [527.0, 86.0, 86.0, 554.0]", "Extracted entities: {'chocolate': 554.0, 'raisin': 86.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-973", "problem": "A book has 3 chapters. The first chapter is 53 pages long the second chapter is 75 pages long and the third chapter is 21 pages long. How many more pages does the first chapter have than the third chapter?", "expected_answer": "32.0", "predicted_answer": "72.0", "reasoning_steps": ["Extracted numbers: [3.0, 53.0, 75.0, 21.0]", "Extracted entities: {'book': 3.0, 'chapters': 3.0, 'pages': 21.0}", "'How many more' pattern: 75.0 - 3.0 = 72.0", "Direct arithmetic result: 72.0"]}, {"id": "chal-222", "problem": "The Razorback t-shirt shop makes $ 78 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 186 t-shirts. If they sold 172 t-shirts during the Arkansas game How much money did they make from selling the t-shirts during the Texas tech game?", "expected_answer": "1092.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [78.0, 186.0, 172.0]", "Extracted entities: {'they': 172.0, 't': 172.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-783", "problem": "<PERSON> picked 6 ripe apples from her tree. Now the tree has 2 ripe apples and 4 unripe apples. How many apples did the tree have to begin with?", "expected_answer": "12.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [6.0, 2.0, 4.0]", "Extracted entities: {'tree': 2.0, 'ripe': 2.0, 'unripe': 4.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-583", "problem": "Rachel had to complete 8 pages of math homework. If she had to complete 3 more pages of math homework than reading homework How many pages did she have to complete in all?", "expected_answer": "13.0", "predicted_answer": "11.0", "reasoning_steps": ["Extracted numbers: [8.0, 3.0]", "Extracted entities: {'pages': 8.0}", "'More than' addition: 8.0 + 3.0 = 11.0", "Direct arithmetic result: 11.0"]}, {"id": "chal-217", "problem": "Mom buys 51 white t - shirts. If white t - shirts can be purchased in packages of 3 How many packages will she have?", "expected_answer": "17.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [51.0, 3.0]", "Extracted entities: {'white': 51.0, 'how': 3.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-321", "problem": "<PERSON> had 19 apps and 18 files on his phone. After deleting some apps and files he had 6 apps and 15 files left. How many more files than apps does he have left on the phone?", "expected_answer": "9.0", "predicted_answer": "13.0", "reasoning_steps": ["Extracted numbers: [19.0, 18.0, 6.0, 15.0]", "Extracted entities: {'dave': 19.0, 'he': 6.0, 'apps': 6.0, 'files': 15.0}", "'How many more' pattern: 19.0 - 6.0 = 13.0", "Direct arithmetic result: 13.0"]}, {"id": "chal-699", "problem": "<PERSON> could fit 11 action figures on each shelf in his room. His room has 4 shelves and 40 cabinets. How many total action figures could his shelves hold?", "expected_answer": "44.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [11.0, 4.0, 40.0]", "Extracted entities: {'room': 4.0, 'action': 11.0, 'shelves': 4.0, 'cabinets': 40.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-541", "problem": "<PERSON> currently weighs 27 kilograms. After she started to go jogging everyday she lost 101 kilograms. How much did she weigh before starting to jog?", "expected_answer": "128.0", "predicted_answer": "-74.0", "reasoning_steps": ["Extracted numbers: [27.0, 101.0]", "Extracted entities: {'she': 101.0, 'kilograms': 101.0}", "Loss pattern: 27.0 - 101.0 = -74.0", "Direct arithmetic result: -74.0"]}, {"id": "chal-113", "problem": "There were 16 roses and 3 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 7 orchids and 13 roses in the vase. How many orchids did she cut?", "expected_answer": "4.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [16.0, 3.0, 7.0, 13.0]", "Extracted entities: {'roses': 16.0, 'orchids': 7.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('16', 'roses')", "Calculation template: roses = 16.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-270", "problem": "<PERSON> collects cards. She had 246 baseball cards and 214 Ace cards. She gave some of her cards to <PERSON> and now has 404 baseball cards and 495 Ace cards left. How many more Ace cards than baseball cards does <PERSON> have?", "expected_answer": "91.0", "predicted_answer": "281.0", "reasoning_steps": ["Extracted numbers: [246.0, 214.0, 404.0, 495.0]", "Extracted entities: {'she': 246.0, 'now': 404.0, 'baseball': 404.0, 'ace': 495.0}", "'How many more' pattern: 495.0 - 214.0 = 281.0", "Direct arithmetic result: 281.0"]}, {"id": "chal-40", "problem": "There are 8 different books and 5 different movies in the ' crazy silly school ' series. If you read 19 of the movies and watched 16 of the books How many more movies than books have you read?", "expected_answer": "3.0", "predicted_answer": "14.0", "reasoning_steps": ["Extracted numbers: [8.0, 5.0, 19.0, 16.0]", "Extracted entities: {'different': 8.0, 'of': 16.0}", "'How many more' pattern: 19.0 - 5.0 = 14.0", "Direct arithmetic result: 14.0"]}, {"id": "chal-184", "problem": "<PERSON>'s mother made cookies for 14. If each of them had 30 cookies How many cookies did she prepare?", "expected_answer": "420.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [14.0, 30.0]", "Extracted entities: {'them': 30.0, 'cookies': 30.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('them', '30')", "Calculation template: them = 30.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-208", "problem": "Every day <PERSON> spends 2 hours on learning english, 5 hours on learning chinese and 4 hours on learning spanish. How many more hours does he spend on learning chinese than he does on learning spanish?", "expected_answer": "1.0", "predicted_answer": "3.0", "reasoning_steps": ["Extracted numbers: [2.0, 5.0, 4.0]", "Extracted entities: {'hours': 4.0}", "'How many more' pattern: 5.0 - 2.0 = 3.0", "Direct arithmetic result: 3.0"]}, {"id": "chal-502", "problem": "<PERSON> had 35 packs of pencils each one having 4 pencils. She was placing her pencils into rows with 2 pencils in each row. How many rows could she make?", "expected_answer": "70.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [35.0, 4.0, 2.0]", "Extracted entities: {'faye': 35.0, 'packs': 35.0, 'pencils': 2.0}", "Matched pattern 'direct_value_assignment': (\\w+) had (\\d+)", "Captured groups: ('<PERSON>', '35')", "Calculation template: Faye = 35.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-922", "problem": "44 campers went rowing in the morning 39 campers went rowing in the afternoon and 31 campers went rowing in the evening. How many more campers went rowing in the morning than in the afternoon?", "expected_answer": "5.0", "predicted_answer": "13.0", "reasoning_steps": ["Extracted numbers: [44.0, 39.0, 31.0]", "Extracted entities: {'campers': 31.0}", "'How many more' pattern: 44.0 - 31.0 = 13.0", "Direct arithmetic result: 13.0"]}, {"id": "chal-488", "problem": "<PERSON><PERSON> bought 153 water bottles when they were on sale. She drank the same number of bottles each day. If the bottles lasted for 17 days How many bottles did she drink each day?", "expected_answer": "9.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [153.0, 17.0]", "Extracted entities: {'debby': 153.0, 'water': 153.0, 'days': 17.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-939", "problem": "The ring toss game at the carnival made the same amount of money each day. In total in 5 days they earned 165 dollars. How much did they make per day?", "expected_answer": "33.0", "predicted_answer": "170.0", "reasoning_steps": ["Extracted numbers: [5.0, 165.0]", "Extracted entities: {'days': 5.0}", "Total/sum pattern: 5.0 + 165.0 = 170.0", "Direct arithmetic result: 170.0"]}, {"id": "chal-346", "problem": "<PERSON> grew some trees in her backyard. After a typhoon 5 died. If 12 trees were left How many trees did she grow?", "expected_answer": "17.0", "predicted_answer": "-7.0", "reasoning_steps": ["Extracted numbers: [5.0, 12.0]", "Extracted entities: {'died': 5.0, 'trees': 12.0}", "Remaining pattern: 5.0 - 12.0 = -7.0", "Direct arithmetic result: -7.0"]}, {"id": "chal-767", "problem": "<PERSON> wants to split a collection of eggs into groups of 5. <PERSON> has 20 eggs and 6 marbles. How many more eggs does <PERSON> have than marbles?", "expected_answer": "14.0", "predicted_answer": "15.0", "reasoning_steps": ["Extracted numbers: [5.0, 20.0, 6.0]", "Extracted entities: {'rebecca': 20.0, 'eggs': 20.0, 'marbles': 6.0}", "'How many more' pattern: 20.0 - 5.0 = 15.0", "Direct arithmetic result: 15.0"]}, {"id": "chal-133", "problem": "22 children were riding on the bus. At the bus stop 40 children got on the bus while some got off the bus. Then there were 2 children altogether on the bus. How many children got off the bus at the bus stop?", "expected_answer": "60.0", "predicted_answer": "64.0", "reasoning_steps": ["Extracted numbers: [22.0, 40.0, 2.0]", "Extracted entities: {'children': 2.0}", "Total/sum pattern: 22.0 + 40.0 + 2.0 = 64.0", "Direct arithmetic result: 64.0"]}, {"id": "chal-652", "problem": "<PERSON> is baking a cake. The recipe calls for 9 cups of flour and 11 cups of sugar. She already put in 4 cups of flour. How many more cups of sugar than cups of flour does she need to add now?", "expected_answer": "6.0", "predicted_answer": "7.0", "reasoning_steps": ["Extracted numbers: [9.0, 11.0, 4.0]", "Extracted entities: {'cups': 4.0}", "'How many more' pattern: 11.0 - 4.0 = 7.0", "Direct arithmetic result: 7.0"]}, {"id": "chal-607", "problem": "Every day <PERSON> spends 4 hours on learning english and 6 hours on learning chinese. If he learns for 86 days How many hours does he spend on learning english and chinese each day?", "expected_answer": "10.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 6.0, 86.0]", "Extracted entities: {'hours': 6.0, 'days': 86.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-235", "problem": "<PERSON> has $ 4. He had $ 3 left with him after he bought a candy bar. How much did the candy bar cost?", "expected_answer": "1.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [4.0, 3.0]", "Extracted entities: {'left': 3.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-291", "problem": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 197 chocolate chip cookies and 46 raisin cookies yesterday. And she baked 75 raisin cookies and 66 chocolate chip cookies this morning. How many more chocolate chip cookies did <PERSON> bake yesterday compared to today?", "expected_answer": "131.0", "predicted_answer": "151.0", "reasoning_steps": ["Extracted numbers: [197.0, 46.0, 75.0, 66.0]", "Extracted entities: {'chocolate': 66.0, 'raisin': 75.0}", "'How many more' pattern: 197.0 - 46.0 = 151.0", "Direct arithmetic result: 151.0"]}, {"id": "chal-757", "problem": "<PERSON> played tag with 16 kids on monday. If she played tag with 12 more kids on monday than on tuesday How many kids did she play with on tuesday?", "expected_answer": "4.0", "predicted_answer": "28.0", "reasoning_steps": ["Extracted numbers: [16.0, 12.0]", "Extracted entities: {'kids': 16.0}", "'More than' addition: 16.0 + 12.0 = 28.0", "Direct arithmetic result: 28.0"]}, {"id": "chal-744", "problem": "There were 8 people on the bus. At the next stop 12 more people got on the bus and 3 people got off. How many people are there on the bus now?", "expected_answer": "17.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [8.0, 12.0, 3.0]", "Extracted entities: {'people': 8.0}", "Matched pattern 'direct_quantity_assignment': There (?:are|were) (\\d+) (\\w+)", "Captured groups: ('8', 'people')", "Calculation template: people = 8.0", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-640", "problem": "<PERSON> had to complete 7 pages of math homework, 11 pages of reading homework and 8 more pages of biology homework. How many more pages of reading homework than biology homework did she have?", "expected_answer": "3.0", "predicted_answer": "4.0", "reasoning_steps": ["Extracted numbers: [7.0, 11.0, 8.0]", "Extracted entities: {'pages': 11.0}", "'How many more' pattern: 11.0 - 7.0 = 4.0", "Direct arithmetic result: 4.0"]}, {"id": "chal-351", "problem": "After a typhoon, 13 trees in <PERSON>'s backyard died. If she had grown 3 trees initially How many more trees died in the typhoon than those that survived?", "expected_answer": "23.0", "predicted_answer": "10.0", "reasoning_steps": ["Extracted numbers: [13.0, 3.0]", "Extracted entities: {'trees': 3.0}", "'How many more' pattern: 13.0 - 3.0 = 10.0", "Direct arithmetic result: 10.0"]}, {"id": "chal-994", "problem": "<PERSON> scored 84 points after playing 2 rounds of a trivia game. If he gained the same number of points each round How many points did he score per round?", "expected_answer": "42.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [84.0, 2.0]", "Extracted entities: {'luke': 84.0, 'points': 84.0, 'rounds': 2.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-391", "problem": "<PERSON> had 5 action figures and 2 books on a shelf in his room. Later he added 9 more books to the shelf. How many more books than action figures were on his shelf?", "expected_answer": "6.0", "predicted_answer": "7.0", "reasoning_steps": ["Extracted numbers: [5.0, 2.0, 9.0]", "Extracted entities: {'jerry': 5.0, 'action': 5.0, 'books': 2.0}", "'How many more' pattern: 9.0 - 2.0 = 7.0", "Direct arithmetic result: 7.0"]}, {"id": "chal-545", "problem": "<PERSON> collects bottle caps. He found 36 bottle caps at the park while he threw away 35 old ones. Now he has 22 bottle caps in his collection. How many more bottle caps did danny find at the park than those he threw away?", "expected_answer": "1.0", "predicted_answer": "14.0", "reasoning_steps": ["Extracted numbers: [36.0, 35.0, 22.0]", "Extracted entities: {'he': 22.0, 'bottle': 22.0, 'old': 35.0}", "'How many more' pattern: 36.0 - 22.0 = 14.0", "Direct arithmetic result: 14.0"]}, {"id": "chal-838", "problem": "<PERSON> spent $ 9. Then he spent $ 8 more. Now he has $ 17. How much did <PERSON> have before he spent his money?", "expected_answer": "34.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [9.0, 8.0, 17.0]", "Extracted entities: {}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-431", "problem": "<PERSON> is baking a cake. The recipe calls for 9 cups of flour and 5 cups of sugar. She already put in 3 cups of flour and 62 cups of sugar. How many more cups of flour does she need to add?", "expected_answer": "6.0", "predicted_answer": "59.0", "reasoning_steps": ["Extracted numbers: [9.0, 5.0, 3.0, 62.0]", "Extracted entities: {'cups': 62.0}", "'How many more' pattern: 62.0 - 3.0 = 59.0", "Direct arithmetic result: 59.0"]}, {"id": "chal-960", "problem": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 94 of the pickers that they fill 90 drums of grapes in 6 days. How many drums of grapes would be filled per day?", "expected_answer": "15.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [94.0, 90.0, 6.0]", "Extracted entities: {'of': 94.0, 'drums': 90.0, 'days': 6.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-819", "problem": "<PERSON> had 5 action figures and 9 books on a shelf in his room. Later he added 7 more action figures to the shelf. How many more action figures than books were on his shelf?", "expected_answer": "3.0", "predicted_answer": "4.0", "reasoning_steps": ["Extracted numbers: [5.0, 9.0, 7.0]", "Extracted entities: {'jerry': 5.0, 'action': 5.0, 'books': 9.0}", "'How many more' pattern: 9.0 - 5.0 = 4.0", "Direct arithmetic result: 4.0"]}, {"id": "chal-991", "problem": "After resting they decided to go for a swim. The depth of the water is 9 times <PERSON>'s height. If <PERSON> is 15 feet tall and <PERSON> is 6 feet shorter than <PERSON> How deep was the water?", "expected_answer": "81.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [9.0, 15.0, 6.0]", "Extracted entities: {'feet': 6.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}, {"id": "chal-860", "problem": "<PERSON> was placing her pencils and crayons into 7 rows with 36 pencils and 30 crayons in each row. How many crayons does she have?", "expected_answer": "210.0", "predicted_answer": "", "reasoning_steps": ["Extracted numbers: [7.0, 36.0, 30.0]", "Extracted entities: {'rows': 7.0, 'pencils': 36.0, 'crayons': 30.0}", "No matching pattern found", "Unable to solve this problem with current patterns"]}]