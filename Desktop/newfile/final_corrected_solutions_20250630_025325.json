{
  "metadata": {
    "generation_script": "generate_final_solutions.py",
    "generator_used": "FinalCOTDIRGenerator (V4)",
    "source_file": "full_relation_solutions_20250630_024146.json",
    "total_problems": 14097,
    "timestamp": "2025-06-30T02:53:25.657934",
    "total_processing_time_seconds": 0.91
  },
  "solutions": [
    {
      "problem_id": "4964",
      "question": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?",
      "dataset_source": "MATH",
      "problem_type": "一般算术",
      "relations": [
        