{"patterns": [{"name": "complex_initial_state", "type": "multi_step", "pattern": "(\\w+) had (\\d+) \\w+ initially", "template": "{arg1}_initial = {arg2}", "priority": "high"}, {"name": "final_state_given", "type": "multi_step", "pattern": "(\\w+) has (\\d+) \\w+ now", "template": "{arg1}_final = {arg2}", "priority": "high"}, {"name": "change_operation", "type": "multi_step", "pattern": "(\\w+) (?:gained|lost|added|removed) (\\d+) \\w+", "template": "{arg1}_change = {arg2}", "priority": "high"}, {"name": "spending_calculation", "type": "calculation", "pattern": "How much did \\w+ spend", "template": "initial_money - remaining_money", "priority": "medium"}, {"name": "earning_calculation", "type": "calculation", "pattern": "How much money did \\w+ make", "template": "final_money - initial_money", "priority": "medium"}, {"name": "multiplication_operation", "type": "binary_operation", "pattern": "(\\w+) (?:sold|made|has) (\\d+) times (?:as many|more) \\w+", "template": "{arg1} = {arg2} * base_value", "priority": "medium"}, {"name": "per_unit_calculation", "type": "calculation", "pattern": "How many \\w+ (?:per|each) \\w+", "template": "total_quantity / number_of_units", "priority": "medium"}]}