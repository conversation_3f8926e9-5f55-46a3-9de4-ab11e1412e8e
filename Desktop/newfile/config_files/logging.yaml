version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  detailed:
    format: '%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: standard
    stream: ext://sys.stdout

  file_handler:
    class: logging.FileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/app.log
    encoding: utf8

  error_file_handler:
    class: logging.FileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    encoding: utf8

loggers:
  '':  # root logger
    level: INFO
    handlers: [console, file_handler, error_file_handler]
    propagate: false
  
  models:
    level: DEBUG
    handlers: [console, file_handler]
    propagate: false

  processors:
    level: DEBUG
    handlers: [console, file_handler]
    propagate: false

root:  # root logger
  level: INFO
  handlers: [console, file_handler, error_file_handler]
2025-03-20 15:26:02,197 - processors.nlp_processor - INFO - NLP处理器初始化成功，使用设备: cpu
2025-03-20 15:26:02,197 - __main__ - ERROR - 程序执行出错: Can't instantiate abstract class RateAndVolumeSolver with abstract method _solve_equations
2025-03-20 15:26:54,285 - processors.nlp_processor - INFO - NLP处理器初始化成功，使用设备: cpu
2025-03-20 15:26:54,285 - __main__ - ERROR - 程序执行出错: Can't instantiate abstract class RateAndVolumeSolver with abstract method _solve_equations
2025-03-20 15:28:10,082 - processors.nlp_processor - INFO - NLP处理器初始化成功，使用设备: cpu
2025-03-20 15:28:10,082 - __main__ - ERROR - 程序执行出错: Can't instantiate abstract class RateAndVolumeSolver with abstract method _solve_equations
2025-03-20 15:28:39,954 - processors.nlp_processor - INFO - NLP处理器初始化成功，使用设备: cpu
2025-03-20 15:28:39,954 - __main__ - ERROR - 程序执行出错: Can't instantiate abstract class RateAndVolumeSolver with abstract method _solve_equations
