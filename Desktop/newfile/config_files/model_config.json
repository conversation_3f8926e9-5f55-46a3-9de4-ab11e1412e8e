{"models": {"gpt4o": {"enabled": false, "api_key": null, "temperature": 0.7, "max_tokens": 2048, "base_url": "https://api.openai.com/v1", "description": "OpenAI GPT-4o model for mathematical reasoning"}, "claude": {"enabled": false, "api_key": null, "temperature": 0.7, "max_tokens": 2048, "base_url": "https://api.anthropic.com/v1", "description": "Anthropic Claude 3.5 Sonnet model"}, "qwen": {"enabled": true, "is_local": true, "base_url": "http://localhost:8000/v1", "temperature": 0.7, "max_tokens": 2048, "description": "Qwen2.5-Math-72B model (local deployment)"}, "internlm": {"enabled": true, "is_local": true, "base_url": "http://localhost:8001/v1", "temperature": 0.7, "max_tokens": 2048, "description": "InternLM2.5-Math-7B model (local deployment)"}, "deepseek": {"enabled": true, "is_local": true, "base_url": "http://localhost:8002/v1", "temperature": 0.7, "max_tokens": 2048, "description": "DeepSeek-Math-7B model (local deployment)"}, "cotdir": {"enabled": true, "enable_ird": true, "enable_mlr": true, "enable_cv": true, "confidence_threshold": 0.7, "max_reasoning_depth": 5, "relation_threshold": 0.6, "ird_weight": 0.3, "mlr_weight": 0.5, "cv_weight": 0.2, "description": "Proposed Chain-of-Thought with Directional Implicit Reasoning model"}, "template_baseline": {"enabled": true, "description": "Template-based baseline model using predefined patterns"}, "equation_baseline": {"enabled": true, "description": "Equation-based baseline model using symbolic math"}, "rule_baseline": {"enabled": true, "description": "Rule-based baseline model using heuristic rules"}}, "evaluation": {"timeout": 30, "max_workers": 4, "retry_attempts": 3, "batch_size": 100, "enable_parallel": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/model_interface.log", "max_file_size": "10MB", "backup_count": 5}, "data": {"test_problems_path": "Data/test_problems.json", "results_output_path": "results/model_comparison_results.json", "benchmark_datasets": ["Data/GSM8K/test.jsonl", "Data/SVAMP/SVAMP.json", "Data/MultiArith/MultiArith.json"]}, "ablation_study": {"cotdir_variants": [{"name": "COT-DIR Full", "config": {"enable_ird": true, "enable_mlr": true, "enable_cv": true}}, {"name": "w/o IRD", "config": {"enable_ird": false, "enable_mlr": true, "enable_cv": true}}, {"name": "w/o MLR", "config": {"enable_ird": true, "enable_mlr": false, "enable_cv": true}}, {"name": "w/o CV", "config": {"enable_ird": true, "enable_mlr": true, "enable_cv": false}}, {"name": "IRD only", "config": {"enable_ird": true, "enable_mlr": false, "enable_cv": false}}, {"name": "MLR only", "config": {"enable_ird": false, "enable_mlr": true, "enable_cv": false}}, {"name": "CV only", "config": {"enable_ird": false, "enable_mlr": false, "enable_cv": true}}]}, "api_keys": {"note": "Set environment variables or update this section with your API keys", "openai": "OPENAI_API_KEY", "anthropic": "ANTHROPIC_API_KEY", "qwen": "QWEN_API_KEY"}, "performance": {"enable_caching": true, "cache_size": 1000, "enable_profiling": false, "memory_limit_mb": 4096}}