\begin{table}[htbp]
\caption{Ablation Study: Component-wise Performance Analysis}
\label{tab:ablation_study}
\centering
\small
\begin{tabular}{lccccc}
\toprule
\textbf{Configuration} & \textbf{Overall} & \textbf{L2/L3} & \textbf{Relation F1} & \textbf{Efficiency} & \textbf{$\Delta$} \\
\midrule
Baseline CoT & 0.715 & 0.525 & 0.678 & 2.4s & - \\
\midrule
+ Implicit Relation Detection & 0.731 & 0.541 & 0.695 & 2.2s & +1.6\% \\
+ Deep Relation Modeling & 0.739 & 0.556 & 0.703 & 2.1s & +2.4\% \\
+ Adaptive Reasoning Path & 0.744 & 0.564 & 0.708 & 2.0s & +2.9\% \\
+ Relation-aware Attention & 0.747 & 0.572 & 0.712 & 1.9s & +3.2\% \\
\midrule
\textbf{COT-DIR (Full)} & \textbf{0.747} & \textbf{0.572} & \textbf{0.712} & \textbf{1.9s} & \textbf{+3.2\%} \\
\bottomrule
\end{tabular}
\end{table}

\textbf{Ablation Analysis}: 
\begin{itemize}
    \item \textbf{Implicit Relation Detection} contributes 1.6\% accuracy improvement, demonstrating the value of identifying hidden mathematical relationships
    \item \textbf{Deep Relation Modeling} adds another 0.8\% gain, showing benefits of hierarchical relation representation
    \item \textbf{Adaptive Reasoning Path} provides 0.5\% improvement through dynamic reasoning strategy selection
    \item \textbf{Relation-aware Attention} contributes 0.3\% final gain via focused attention on discovered relations
\end{itemize}

Each component contributes meaningfully to both overall performance and complex problem solving (L2/L3), with cumulative effects validating our architectural design choices. 