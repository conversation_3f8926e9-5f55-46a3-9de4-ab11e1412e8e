
# 增强模式库测试报告

## 测试统计
- 总测试问题数: 20
- 改进问题数: 0
- 改进率: 0.0%

## 详细结果

### 问题 chal-120
- **问题**: <PERSON> had 5 more marbles than <PERSON>. <PERSON> lost 3 of his marbles at the playground. If <PERSON> had 27 marbles How many marbles did <PERSON> have initially?
- **期望答案**: 22.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [5.0, 3.0, 27.0]
  - Extracted entities: {'ed': 27.0, 'doug': 3.0, 'of': 3.0, 'marbles': 27.0}
  - Matched pattern 'direct_value_assignment': (\w+) had (\d+)
  - Captured groups: ('Ed', '5')
  - Calculation template: Ed = 5.0
  - Matched pattern 'more_than_relation': (\w+) (?:did|had) (\d+) more \w+ than (\w+)
  - Captured groups: ('Ed', '5', '<PERSON>')
  - Calculation template: Ed = Doug + 5.0
  - Matched pattern 'change_operation': (\w+) (?:gained|lost|added|removed) (\d+) \w+
  - Captured groups: ('<PERSON>', '3')
  - Calculation template: <PERSON>_change = 3.0
  - No matching pattern found

### 问题 chal-334
- **问题**: There were 2 roses in the vase. Jessica threw away 4 roses from the vase and cut some more new roses from her flower garden to put in the vase. There are now 23 roses in the vase. How many roses did she cut?
- **期望答案**: 25.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [2.0, 4.0, 23.0]
  - Extracted entities: {'jessica': 4.0, 'roses': 2.0}
  - Matched pattern 'direct_quantity_assignment': There (?:are|were) (\d+) (\w+)
  - Captured groups: ('2', 'roses')
  - Calculation template: roses = 2.0
  - No matching pattern found

### 问题 chal-188
- **问题**: Last week Fred had 114 dollars and Jason had 22 dollars. They washed cars over the weekend and now Fred has 21 dollars and Jason has 78 dollars. How much money did Jason make over the weekend?
- **期望答案**: 56.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [114.0, 22.0, 21.0, 78.0]
  - Extracted entities: {'fred': 21.0, 'jason': 78.0}
  - Matched pattern 'direct_value_assignment': (\w+) had (\d+)
  - Captured groups: ('Fred', '114')
  - Calculation template: Fred = 114.0
  - Matched pattern 'earning_calculation': How much money did \w+ make
  - No matching pattern found

### 问题 chal-313
- **问题**: A waiter had 11 customers. After some left he still had 3 customers. How many more customers left than those that stayed behind?
- **期望答案**: 5.0
- **原始预测**: 8.0
- **新预测**: 8.0
- **置信度**: 1.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [11.0, 3.0]
  - Extracted entities: {'waiter': 11.0, 'still': 3.0, 'customers': 3.0}
  - 'How many more' pattern: 11.0 - 3.0 = 8.0
  - Direct arithmetic result: 8.0

### 问题 chal-679
- **问题**: Matthew had 23 crackers. He has 11 crackers left after he gave equal numbers of crackers to his 2 friends. How many crackers did each friend eat?
- **期望答案**: 6.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [23.0, 11.0, 2.0]
  - Extracted entities: {'matthew': 23.0, 'he': 11.0, 'crackers': 11.0, 'friends': 2.0}
  - Matched pattern 'direct_value_assignment': (\w+) had (\d+)
  - Captured groups: ('Matthew', '23')
  - Calculation template: Matthew = 23.0
  - Matched pattern 'distribution_operation_crackers_friend': How many crackers did each friend
  - No matching pattern found

### 问题 chal-402
- **问题**: For Gwen's birthday she received 8 dollars from her mom. Her dad gave her 5 more dollars. If she spent 4 dollars. How much more money did she receive from her mom than she did from her dad?
- **期望答案**: 3.0
- **原始预测**: 9.0
- **新预测**: 9.0
- **置信度**: 1.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [8.0, 5.0, 4.0]
  - Extracted entities: {'she': 8.0}
  - Received+Gave-Spent: 8.0 + 5.0 - 4.0 = 9.0
  - Direct arithmetic result: 9.0

### 问题 chal-490
- **问题**: After Olivia visited a supermarket there were 29 dollars left. If there were 54 dollars in her wallet initially How much did she spend?
- **期望答案**: 25.0
- **原始预测**: -25.0
- **新预测**: -25.0
- **置信度**: 1.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [29.0, 54.0]
  - Extracted entities: {'dollars': 54.0}
  - Spending pattern: 29.0 - 54.0 = -25.0
  - Direct arithmetic result: -25.0

### 问题 chal-603
- **问题**: The Razorback shop makes $ 115 dollars off each jersey and $ 25 off each t-shirt. During the Arkansas and Texas tech game they sold 113 t-shirts and 78 jerseys. How much more does a jersey cost than a t-shirt?
- **期望答案**: 90.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [115.0, 25.0, 113.0, 78.0]
  - Extracted entities: {'they': 113.0, 'off': 25.0, 't': 113.0, 'jerseys': 78.0}
  - No matching pattern found

### 问题 chal-512
- **问题**: Paul got a box of 521 crayons and 66 erasers for his birthday. At the end of the school year he only had 154 left while not having lost a single erasers. How many crayons had been lost or given away?
- **期望答案**: 367.0
- **原始预测**: 587.0
- **新预测**: 587.0
- **置信度**: 1.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [521.0, 66.0, 154.0]
  - Extracted entities: {'only': 154.0, 'crayons': 521.0, 'erasers': 66.0, 'left': 154.0}
  - Given+Lost pattern: 521.0 + 66.0 = 587.0
  - Direct arithmetic result: 587.0

### 问题 chal-472
- **问题**: There are 896 skittles in Steven's skittles collection. Steven also has 517 erasers and 90 scales. If the skittles are organized into 8 groups How big is each group?
- **期望答案**: 112.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [896.0, 517.0, 90.0, 8.0]
  - Extracted entities: {'also': 517.0, 'skittles': 896.0, 'erasers': 517.0, 'scales': 90.0, 'groups': 8.0}
  - Matched pattern 'direct_quantity_assignment': There (?:are|were) (\d+) (\w+)
  - Captured groups: ('896', 'skittles')
  - Calculation template: skittles = 896.0
  - No matching pattern found

### 问题 chal-140
- **问题**: Jessica cut some roses from her flower garden to put in her vase. There are now 19 roses in the vase. If there were 3 roses in the vase initially How many roses did she cut?
- **期望答案**: 16.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [19.0, 3.0]
  - Extracted entities: {'roses': 3.0}
  - Matched pattern 'direct_quantity_assignment': There (?:are|were) (\d+) (\w+)
  - Captured groups: ('3', 'roses')
  - Calculation template: roses = 3.0
  - No matching pattern found

### 问题 chal-436
- **问题**: Melissa scored 12 points in each game. If she scored a total of 36 points How many games did she play?
- **期望答案**: 3.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [12.0, 36.0]
  - Extracted entities: {'melissa': 12.0, 'points': 36.0}
  - No matching pattern found

### 问题 chal-178
- **问题**: The Razorback t-shirt shop makes $ 98 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 163 t-shirts. If they sold 89 t-shirts during the Arkansas game How much money did they make from selling the t-shirts during the arkansas game?
- **期望答案**: 8722.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [98.0, 163.0, 89.0]
  - Extracted entities: {'they': 89.0, 't': 89.0}
  - Matched pattern 'earning_calculation': How much money did \w+ make
  - No matching pattern found

### 问题 chal-586
- **问题**: Julia played tag with 2 kids on monday, 14 kids on tuesday and 16 kids on wednesday. How many kids did she play with on tuesday and wednesday?
- **期望答案**: 30.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [2.0, 14.0, 16.0]
  - Extracted entities: {'kids': 16.0}
  - No matching pattern found

### 问题 chal-902
- **问题**: Haley grew 9 trees in her backyard. After a typhoon 4 died. Then she grew 5 more trees. How many trees does she have left?
- **期望答案**: 10.0
- **原始预测**: 0.0
- **新预测**: 0.0
- **置信度**: 1.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [9.0, 4.0, 5.0]
  - Extracted entities: {'trees': 9.0, 'died': 4.0}
  - Remaining pattern: 9.0 - 9.0 = 0.0
  - Direct arithmetic result: 0.0

### 问题 chal-82
- **问题**: Brenda's mother made 14 cookies for 2 guests. If each of them had the same number of cookies How many did each of them have?
- **期望答案**: 7.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [14.0, 2.0]
  - Extracted entities: {'cookies': 14.0, 'guests': 2.0}
  - Matched pattern 'per_unit_calculation': How many \w+ (?:per|each) \w+
  - No matching pattern found

### 问题 chal-275
- **问题**: Brenda's mother made cookies for 10 guests but 9 guests did not come. If she prepared 18 cookies and each guest had the same number of cookies How many did each of them have?
- **期望答案**: 18.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [10.0, 9.0, 18.0]
  - Extracted entities: {'guests': 9.0, 'cookies': 18.0}
  - Matched pattern 'per_unit_calculation': How many \w+ (?:per|each) \w+
  - No matching pattern found

### 问题 chal-546
- **问题**: Dan has $ 4. For a total of $ 3 he bought 10 candy bar each one costing the same amount of money. How much money is left?
- **期望答案**: 1.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [4.0, 3.0, 10.0]
  - Extracted entities: {'he': 3.0, 'candy': 10.0}
  - No matching pattern found

### 问题 chal-55
- **问题**: A waiter had 12 customers. After some left he still had 14 customers. Then he got 10 new customers How many customers does he have now?
- **期望答案**: 24.0
- **原始预测**: -12.0
- **新预测**: -12.0
- **置信度**: 1.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [12.0, 14.0, 10.0]
  - Extracted entities: {'waiter': 12.0, 'still': 14.0, 'he': 10.0, 'customers': 14.0, 'new': 10.0}
  - Remaining pattern: 12.0 - 24.0 = -12.0
  - Direct arithmetic result: -12.0

### 问题 chal-425
- **问题**: The Razorback t-shirt shop sells each t-shirt for $ 51 dollars. During the Arkansas and Texas tech game they offered a discount of $ 8 per t-shirt and sold 130 t-shirts. How much money did they make from selling the t-shirts?
- **期望答案**: 5590.0
- **原始预测**: 
- **新预测**: None
- **置信度**: 0.0
- **是否改进**: ✗
- **推理步骤**:
  - Extracted numbers: [51.0, 8.0, 130.0]
  - Extracted entities: {'and': 130.0, 'per': 8.0, 't': 130.0}
  - Matched pattern 'earning_calculation': How much money did \w+ make
  - No matching pattern found
