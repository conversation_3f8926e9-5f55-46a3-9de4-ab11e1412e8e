{"meta_patterns": [{"name": "binary_operation", "description": "二元运算（加减乘除等）", "pattern_template": "(\\w+) {verb} (\\d+) {object} than (\\w+)", "template_template": "{arg1} = {arg3} {op} {arg2}", "parameters": [{"verb": ["had", "did"], "object": ["more", "less", "fewer"], "op": ["+", "-"]}]}, {"name": "multiplication_operation", "description": "乘法/倍数关系", "pattern_template": "(\\w+) has (\\d+) times as many as (\\w+)", "template_template": "{arg1} = {arg3} * {arg2}", "parameters": [{}]}, {"name": "state_transition", "description": "状态转移（初始-变化-结果）", "pattern_template": "(\\w+) had (\\d+) {object} (initially|originally)", "template_template": "{arg1}_initial = {arg2}", "parameters": [{"object": ["marbles", "roses", "dollars", "crackers", "trees", "customers", "points", "games"]}]}, {"name": "distribution_operation", "description": "平均分配/分组", "pattern_template": "How many {object} did each {recipient}", "template_template": "total / number_of_{recipient}", "parameters": [{"object": ["crackers", "roses", "marbles", "points", "candies"], "recipient": ["friend", "child", "group", "person"]}]}, {"name": "sum_operation", "description": "总和/合计", "pattern_template": "How many {object} (?:in all|in total|altogether)", "template_template": "sum_all_{object}", "parameters": [{"object": ["roses", "marbles", "dollars", "crackers", "trees", "customers", "points", "games"]}]}]}