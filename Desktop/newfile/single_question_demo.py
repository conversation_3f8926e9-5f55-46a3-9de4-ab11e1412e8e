#!/usr/bin/env python3
"""
COT-DIR多问题详细演示（带日志记录）
加载数据集，处理指定数量的问题，并展示详细的中间过程。
结果会同时打印到控制台和指定的输出文件。
"""

import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# 将项目根目录添加到sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from src.reasoning_engine.cotdir_integration import (COTDIRIntegratedWorkflow,
                                                     COTDIRStep, Entity,
                                                     Relation,
                                                     ValidationResult)


def setup_logging(log_file="demo_output.txt"):
    """配置日志记录以同时输出到文件和控制台。"""
    log_file_path = Path(log_file)
    # 确保日志文件存在且为空
    with open(log_file_path, "w", encoding='utf-8') as f:
        pass # 创建或清空文件

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG) # 设置根级别为DEBUG

    # 移除所有现有的处理器，以避免重复日志
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    logging.info(f"🚀 日志系统已启动，输出到: {log_file}")

# ==================== 主演示函数 ====================
def run_demo():
    """运行完整的COT-DIR演示。"""
    
    parser = argparse.ArgumentParser(description="COT-DIR 演示脚本")
    parser.add_argument('--output_file', type=str, default='demo_output.txt', help='指定输出日志文件的路径')
    args = parser.parse_args()

    setup_logging(args.output_file)

    try:
        logging.info("处理内置问题...")
        
        test_problems = [
            {"question": "Chenny is 10 years old. Alyana is 4 years younger than Chenny. How old is Anne if she is 2 years older than Alyana?", "answer": "8"},
            {"question": "Marty has 100 centimeters of ribbon that he must cut into 4 equal parts. Each of the cut parts must be divided into 5 equal parts. How long will each final cut be?", "answer": "5"},
            {"question": "Lillian's garden doesn't have any bird feeders in it so she wants to add some. She builds 3 and buys 3 others. Each bird feeder seems to attract 20 birds throughout the day until Lillian notices that the birds seem to prefer the feeders she made herself which attract 10 more birds each than the store-bought ones. How many birds can Lillian expect to see in her garden each day if the same amount keep coming to her bird feeders?", "answer": "150"}
        ]
        
        logging.info(f"处理 {len(test_problems)} 个内置问题...")

        workflow = COTDIRIntegratedWorkflow()

        for i, problem_data in enumerate(test_problems):
            question = problem_data["question"]
            expected_answer = problem_data["answer"]
            
            logging.info(f"\n{'='*80}")
            logging.info(f"🎯 COT-DIR处理问题 {i+1}/3")
            logging.info(f"{'='*80}")
            logging.info(f"📝 输入问题: {question}")
            logging.info(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            result = workflow.process(question, problem_type="arithmetic")
            
            logging.info(f"\n📍 步骤 2: 实体发现 (Entity Discovery)")
            logging.info(f"{'─'*60}")
            entities = result.get('entities', [])
            if entities:
                logging.info(f"   🔍 成功发现 {len(entities)} 个实体:")
                for entity in entities:
                    logging.info(f"     - 名称: {entity['name']}, 类型: {entity['entity_type']}, 置信度: {entity['confidence']:.2f}")
            else:
                logging.info("   未发现实体。")
            
            logging.info(f"\n📍 步骤 3: 隐式关系发现 (IRD)")
            logging.info(f"{'─'*60}")
            relations = result.get('metadata', {}).get('relations_count', 0)
            if relations > 0:
                logging.info(f"   🔗 成功发现 {relations} 个关系")
            else:
                logging.info("   未发现关系。")
            
            logging.info(f"\n📍 步骤 4: 多层推理 (MLR)")
            logging.info(f"{'─'*60}")
            steps = result.get('reasoning_process', {}).get('total_steps', 0)
            if steps > 0:
                logging.info(f"   🧠 执行了 {steps} 步推理")
            else:
                logging.info("   无推理步骤。")
            
            logging.info(f"\n📍 步骤 5: 置信度验证 (CV)")
            logging.info(f"{'─'*60}")
            verification = result.get('verification_details', {})
            if verification:
                logging.info("🔍 验证维度:")
                for dim, score in verification.items():
                    logging.info(f"   - {dim}: {score:.2f}")
            
            confidence = result.get('overall_confidence', 0)
            logging.info(f"\n📊 综合置信度: {confidence:.2f}")
            
            logging.info(f"\n📍 步骤 6: 最终结果生成")
            logging.info(f"{'─'*60}")
            answer = result.get('answer', {}).get('value', '无法确定')
            logging.info(f"   🎯 最终答案: {answer}")
            
        logging.info("\n🎉 演示完成！")

    except Exception as e:
        logging.error(f"演示过程中发生严重错误: {e}", exc_info=True)

if __name__ == "__main__":
    run_demo() 