# 模式库梳理总结

## 概述
当前模式库包含**12个JSON模式**和**15个直接算术模式**，总共**27个模式**，覆盖了SVAMP数据集中的多种数学问题类型。

## 当前准确率
- **SVAMP数据集**: 19.00% (38/200)
- **模式覆盖范围**: 基础算术运算、比较运算、多步运算

---

## 1. JSON模式库 (patterns.json)

### 1.1 赋值模式 (Assignment Patterns)
| 模式名称 | 正则表达式 | 模板 | 覆盖问题类型 |
|---------|-----------|------|-------------|
| `assignment` | `(\w+) is (\d+) years old` | `{arg1} = {arg2}` | 年龄赋值 |
| `direct_value_assignment` | `(\w+) had (\d+)` | `{arg1} = {arg2}` | 直接数值赋值 |
| `direct_quantity_assignment` | `There (?:are\|were) (\d+) (\w+)` | `{arg2} = {arg1}` | 数量陈述 |

### 1.2 二元运算模式 (Binary Operation Patterns)
| 模式名称 | 正则表达式 | 模板 | 覆盖问题类型 |
|---------|-----------|------|-------------|
| `addition` | `(\w+)'s age is (\d+) more than (\w+)` | `{arg1} = {arg3} + {arg2}` | 年龄加法关系 |
| `subtraction` | `(\w+) is (\d+) years younger than (\w+)` | `{arg1} = {arg3} - {arg2}` | 年龄减法关系 |
| `more_than_relation` | `(\w+) (?:did\|had) (\d+) more \w+ than (\w+)` | `{arg1} = {arg3} + {arg2}` | 比较加法关系 |
| `less_than_relation` | `(\w+) (?:did\|had) (\d+) (?:less\|fewer) \w+ than (\w+)` | `{arg1} = {arg3} - {arg2}` | 比较减法关系 |
| `multiplication_times` | `(\w+) has (\d+) times as many as (\w+)` | `{arg1} = {arg3} * {arg2}` | 倍数关系 |
| `remaining_after_loss` | `(\w+) had (\d+) \w+ left` | `{arg1}_remaining = {arg2}` | 剩余数量 |

### 1.3 计算模式 (Calculation Patterns)
| 模式名称 | 正则表达式 | 模板 | 覆盖问题类型 |
|---------|-----------|------|-------------|
| `simple_addition` | `How many \w+ (?:did \w+ \w+\|were there) in (?:all\|total)` | `sum_all_entities` | 总和计算 |
| `difference_calculation` | `How many more \w+ (?:than\|did) (\w+)` | `max_entity - min_entity` | 差值计算 |

---

## 2. 直接算术模式库 (pattern_based_solver.py)

### 2.1 优先级1: 除法模式 (Division Patterns)
| 模式类型 | 触发条件 | 计算逻辑 | 示例 |
|---------|---------|---------|------|
| 分组除法 | `"how many packs/bags/groups"` | `max(numbers) / min(numbers)` | "10 friends, 2 per pack" → 5 |
| 平均分配 | `"each" + "group/bag/pack/friend"` | `max(numbers) / min(numbers)` | "14 cookies, 7 bags" → 2 |

### 2.2 优先级2: 比较模式 (Comparison Patterns)
| 模式类型 | 触发条件 | 计算逻辑 | 示例 |
|---------|---------|---------|------|
| 差值比较 | `"how many more"` | `max(numbers) - min(numbers)` | "19 red vs 11 yellow" → 8 |
| 反向推理 | `"more X than Y" + "how many did Y"` | `numbers[0] - numbers[1]` | "44 total, 9 more" → 35 |
| 加法比较 | `"X more Y than Z"` | `numbers[0] + numbers[1]` | "4 base + 79 more" → 83 |

### 2.3 优先级3: 求和模式 (Sum Patterns)
| 模式类型 | 触发条件 | 计算逻辑 | 示例 |
|---------|---------|---------|------|
| 总和计算 | `"in total/all/altogether"` | `sum(numbers)` | "36+13+49 campers" → 98 |
| 损失求和 | `"given/lost" + "how many given away"` | `numbers[0] + numbers[1]` | "52 given + 535 lost" → 587 |

### 2.4 优先级3.5-3.7: 复合运算模式 (Compound Operation Patterns)
| 模式类型 | 触发条件 | 计算逻辑 | 示例 |
|---------|---------|---------|------|
| 收支平衡 | `"received" + "gave" + "spent"` | `received + gave - spent` | "2+4-3 dollars" → 3 |
| 使用计算 | `"had X" + "left Y" + "used"` | `had - left` | "40 had - 39 left" → 1 |

### 2.5 优先级4-5: 基础运算模式 (Basic Operation Patterns)
| 模式类型 | 触发条件 | 计算逻辑 | 示例 |
|---------|---------|---------|------|
| 消费模式 | `"spend/spent"` | `numbers[0] - numbers[1]` | 支出计算 |
| 损失模式 | `"gave/lost"` | `numbers[0] - numbers[1]` | 损失计算 |
| 获得模式 | `"received/got/found"` | `numbers[0] + numbers[1]` | 获得计算 |

---

## 3. 模式优先级系统

### 优先级顺序 (从高到低)
1. **直接算术模式** (PRIORITY 1-5) - 更具体，优先执行
2. **JSON模式匹配** (PRIORITY 2) - 通用模板，后执行

### 直接算术模式内部优先级
1. **除法模式** - 最具体的问题类型
2. **比较模式** - 差值、反向推理、加法比较
3. **求和模式** - 总和、损失求和
4. **复合运算** - 多步计算
5. **基础运算** - 简单加减法

---

## 4. 当前覆盖的问题类型

### ✅ 已覆盖类型
- **简单赋值**: 年龄、数量陈述
- **基础运算**: 加法、减法、乘法、除法
- **比较运算**: 差值、倍数关系
- **复合运算**: 收支平衡、使用计算
- **反向推理**: "X比Y多N，求Y"
- **分组问题**: 平均分配、包装计算

### ❌ 未覆盖类型
- **多步推理**: 需要中间变量的问题
- **单位换算**: 时间、货币、重量转换
- **复杂逻辑**: 条件判断、嵌套关系
- **几何问题**: 面积、周长、体积
- **概率统计**: 百分比、比例问题

---

## 5. 性能统计

### 成功案例分布
- **除法问题**: 5/200 (2.5%)
- **比较问题**: 8/200 (4.0%)
- **求和问题**: 6/200 (3.0%)
- **复合运算**: 4/200 (2.0%)
- **基础运算**: 15/200 (7.5%)

### 失败原因分析
- **模式不匹配**: 60% - 问题类型超出当前模式库
- **数字选择错误**: 25% - 选择了错误的数字对
- **计算逻辑错误**: 15% - 模板应用错误

---

## 6. 下一步优化方向

### 6.1 模式库扩展
- 添加多步推理模式
- 增加单位换算支持
- 补充几何问题模式

### 6.2 智能优化
- 改进数字选择算法
- 增强模式匹配精度
- 优化优先级系统

### 6.3 HybridModel集成
- 模式求解器作为第一层
- LLM作为第二层补充
- 置信度阈值控制

---

## 7. 技术架构

### 核心组件
- `PatternBasedSolver`: 模式匹配引擎
- `SimplePatternModel`: 模型接口封装
- `patterns.json`: 模式定义文件
- `_try_direct_arithmetic()`: 直接算术模式

### 数据流
1. 问题文本输入
2. 数字和实体提取
3. 直接算术模式匹配 (优先级1-5)
4. JSON模式匹配 (优先级2)
5. 结果输出

### 扩展性
- 模式库可动态加载
- 优先级系统可调整
- 新模式类型可添加
- 支持自定义模板 