import itertools
import json
import os

META_FILE = 'meta_patterns.json'
MAIN_PATTERN_FILE = 'src/reasoning_engine/patterns.json'
GEN_PATTERN_FILE = 'patterns_generated.json'


def load_meta_patterns(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)['meta_patterns']

def generate_concrete_patterns(meta_pattern):
    param_dicts = meta_pattern['parameters']
    if not param_dicts or param_dicts == [{}]:
        # 无参数，直接生成
        return [{
            'name': meta_pattern['name'],
            'type': meta_pattern['name'],
            'pattern': meta_pattern['pattern_template'],
            'template': meta_pattern['template_template'],
            'description': meta_pattern.get('description', '')
        }]
    param_keys = list(param_dicts[0].keys())
    param_values = [param_dicts[0][k] for k in param_keys]
    concrete_patterns = []
    for values in itertools.product(*param_values):
        param_map = dict(zip(param_keys, values))
        pattern = meta_pattern['pattern_template']
        template = meta_pattern['template_template']
        for k, v in param_map.items():
            pattern = pattern.replace('{' + k + '}', v)
            template = template.replace('{' + k + '}', v)
        concrete_patterns.append({
            'name': f"{meta_pattern['name']}_{'_'.join(values)}",
            'type': meta_pattern['name'],
            'pattern': pattern,
            'template': template,
            'description': meta_pattern.get('description', '')
        })
    return concrete_patterns

def load_main_patterns(file_path):
    if not os.path.exists(file_path):
        return []
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        return data.get('patterns', [])

def save_main_patterns(patterns, file_path):
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump({'patterns': patterns}, f, indent=2, ensure_ascii=False)

def merge_patterns(main_patterns, new_patterns):
    existing = {(p['name'], p['pattern']) for p in main_patterns}
    merged = main_patterns.copy()
    added = 0
    for p in new_patterns:
        key = (p['name'], p['pattern'])
        if key not in existing:
            merged.append(p)
            existing.add(key)
            added += 1
    return merged, added

def main():
    meta_patterns = load_meta_patterns(META_FILE)
    all_concrete_patterns = []
    for meta in meta_patterns:
        all_concrete_patterns.extend(generate_concrete_patterns(meta))
    # 保存生成的具体模式
    with open(GEN_PATTERN_FILE, 'w', encoding='utf-8') as f:
        json.dump({'patterns': all_concrete_patterns}, f, indent=2, ensure_ascii=False)
    print(f"已生成 {len(all_concrete_patterns)} 条具体模式，保存到 {GEN_PATTERN_FILE}")
    # 合并到主模式库
    main_patterns = load_main_patterns(MAIN_PATTERN_FILE)
    merged, added = merge_patterns(main_patterns, all_concrete_patterns)
    save_main_patterns(merged, MAIN_PATTERN_FILE)
    print(f"已自动合并到主模式库 {MAIN_PATTERN_FILE}，新增 {added} 条模式，总数 {len(merged)}")

if __name__ == '__main__':
    main() 