{"dataset_name": "GSM8K", "total_problems": 500, "classification_results": [{"id": "problem_0", "problem_text": "A small sunflower has 3 dozen seeds and a large sunflower has 50% more seeds than a small sunflower....", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_1", "problem_text": "According to its nutritional info, a bag of chips has 250 calories per serving. If a 300g bag has 5 ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_2", "problem_text": "<PERSON> has ten more crabs than <PERSON><PERSON>, who has 4 fewer crabs than <PERSON>. If <PERSON> has 40 crabs, calculate the...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_3", "problem_text": "How many girls are in the school if 40% of a school population is made up of 240 boys?", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_4", "problem_text": "<PERSON><PERSON> had dance practice for 1 hour on Tuesdays and 2 hours on Thursdays.  On Saturdays, she had da...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_5", "problem_text": "<PERSON> bought a big pizza with 60 pieces. He ate 2/5 of the pieces on the first day, 10 pieces on th...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_6", "problem_text": "Although <PERSON><PERSON><PERSON> works in a windowless office, she loves the outdoors. She will be on vacation for t...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_7", "problem_text": "<PERSON> buys beef jerky that comes 30 sticks to a bag and costs $18.00 per bag.  If <PERSON> buys 1 bag...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_8", "problem_text": "A real estate agent has spent $5 on each newspaper ad and spent $75 on each television ad. He bought...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_9", "problem_text": "<PERSON> sells large stuffed animals for three times the price of small stuffed animals. Today, she so...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_10", "problem_text": "<PERSON>'s iPhone is four times as old as <PERSON>'s iPhone. <PERSON>'s iPhone is two times older than <PERSON><PERSON>'s i...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_11", "problem_text": "<PERSON> notices that prices for lumber have gone up 50% in the last few months after she bought some l...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_12", "problem_text": "Brittany's quilted comforter has many 1-foot by 1-foot colored squares. The comforter had 14 red squ...", "complexity_level": "L2_中等推理", "confidence": 0.18, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.18, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_13", "problem_text": "<PERSON> has 4 roommates. Each month the electricity bill is $100. How much will each roommate pay per ...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_14", "problem_text": "<PERSON> sells magazines at 11/8 of the price she bought the magazines. If she bought the magazines a...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_15", "problem_text": "<PERSON><PERSON> wanted brownies for her birthday.  She made a batch for herself; one dozen cream cheese swirl ...", "complexity_level": "L2_中等推理", "confidence": 0.18, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.18, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_16", "problem_text": "Twenty dozen cups cost $1200 less than the total cost of half a dozen plates sold at $6000 each. Cal...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.14833333333333332, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_17", "problem_text": "<PERSON><PERSON> is trying to count the jelly beans in a jar. He asks his friends how many they think are in t...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_18", "problem_text": "A train has 172 people traveling on it. At the first stop 47 people get off and 13 more people get o...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_19", "problem_text": "<PERSON> can run 10 miles per hour for 3 hours. After that, she runs 5 miles per hour. How many miles c...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_20", "problem_text": "A $2000 watch was put on sale so that Mr<PERSON> bought it at 75% of its original price. He then sol...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.056428571428571425, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_21", "problem_text": "<PERSON> has started his own housekeeping business and is calculating how much profit he will make from h...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_22", "problem_text": "<PERSON><PERSON> and <PERSON> wanted to get to know each other. They realized that the sum of their ages is 20. Wha...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_23", "problem_text": "<PERSON><PERSON> is collecting different kinds of beads for making bracelets. Her mother gave her 20 metalli...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_24", "problem_text": "The ratio of the electric poles and electric wires needed to connect and supply the electricity in a...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_25", "problem_text": "An ice cream truck is traveling through a neighborhood. Children from various homes have started cha...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_26", "problem_text": "<PERSON> is measuring how many surfers can ride a big wave without falling. She sees that when a wave...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_27", "problem_text": "<PERSON> has a rope that is 20 meters long. Her friend wants to buy the rope for $2 a meter. <PERSON> plan...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_28", "problem_text": "In a race with 240 Asians, 80 were Japanese, and the rest were Chinese. If the number of boys on the...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_29", "problem_text": "<PERSON> can buy flowers in packages of 3 for $2.50 or in packages of 2 for $1. How much money does h...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_30", "problem_text": "<PERSON> drinks 8 cups of water every day.  If there are 16 cups in a gallon, how many gallons of wate...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_31", "problem_text": "<PERSON> likes eating carrots. If he eats 4 carrots each on weekdays and 5 carrots each on Saturday and...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_32", "problem_text": "<PERSON> has a phone plan of 1000 minutes per month. Every day he has a 15-minute call with his boss, a...", "complexity_level": "L2_中等推理", "confidence": 0.10142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_33", "problem_text": "<PERSON> is trying to figure out how much to pay on all her debts each month. Her student loans have ...", "complexity_level": "L2_中等推理", "confidence": 0.10142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_34", "problem_text": "<PERSON> decides to travel to 3 different countries.  He has to pay $400 for the supplies he needs, in to...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_35", "problem_text": "<PERSON><PERSON> loves fresh fruit and is headed to the store with $10 he earned mowing lawns. Including tax, ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_36", "problem_text": "<PERSON> has $12.48 and wants to buy 16 bolts from a bin at the hardware store. Each bolt costs $0.03. ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_37", "problem_text": "Grayson recycles cans and bottles for money each week. An aluminum can is worth two cents and a plas...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.018}}, {"id": "problem_38", "problem_text": "<PERSON> has a flower bed that is 111 feet long.  <PERSON> wants to fill her flower bed with plants.  Pat's fl...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_39", "problem_text": "<PERSON>'s basement was damp and musty, so he bought a dehumidifier to remove moisture out of the air. ...", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_40", "problem_text": "<PERSON> buys 1 bag of cookies a week.  The bag has 36 cookies and she puts 4 cookies in her son's lunc...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_41", "problem_text": "<PERSON> ate 2 hot dogs. <PERSON> ate three times more hot dogs than <PERSON>. <PERSON> ate half the amount <PERSON>...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_42", "problem_text": "<PERSON>'s garden doesn't have any bird feeders in it so she wants to add some. She builds 3 and buys...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_43", "problem_text": "When <PERSON> was first hired, he was paid at a rate of $10 per hour.  After 2 months, he was given a r...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_44", "problem_text": "<PERSON> and <PERSON> had 12 sandwiches. <PERSON> ate a third of the sandwiches, and <PERSON> ate a quarter of t...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_45", "problem_text": "<PERSON> bought a dozen cups and twice as many dishes as cups to take to the church's charity event. At ...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_46", "problem_text": "A three-ounce box of flavored jello makes 10 small jello cups.  <PERSON> wants to make small jello cups ...", "complexity_level": "L2_中等推理", "confidence": 0.17642857142857143, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.17642857142857143, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_47", "problem_text": "<PERSON> plans to go to the movies this week. He always gets a ticket for $7 and popcorn for $7. If he ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_48", "problem_text": "Great Grandma <PERSON> has three children.  And each of her children has three children of their own, w...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.018}}, {"id": "problem_49", "problem_text": "<PERSON><PERSON><PERSON> wants to buy Christmas gifts for her 5 friends. 2 of her friends want 5 gifts and the other ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_50", "problem_text": "Last month, <PERSON><PERSON> made $80 from selling lemonade and mowing lawns. The first week, she mowed <PERSON><PERSON>'...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_51", "problem_text": "<PERSON> lives in an apartment building with 15 floors. Each floor contains 8 units, and 3/4 of the b...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_52", "problem_text": "A customs officer at the main port of SeaSide clearances counted 2 containers of imported vehicles, ...", "complexity_level": "L1_浅层推理", "confidence": 0.17666666666666667, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.17666666666666667, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_53", "problem_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON> were asked to bring fruits. <PERSON><PERSON><PERSON> brought 5 apples and 8 oranges, while <PERSON><PERSON> ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_54", "problem_text": "<PERSON> decides to give his wife an anniversary getaway.  The plane tickets cost $5000 each.  The hotel ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_55", "problem_text": "<PERSON><PERSON> bought five lollipops and four candies that cost $3.20. If each lollipop costs $0.40, how muc...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_56", "problem_text": "Students in class 3B are collecting school points for behavior. If they get enough points in total, ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.09, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_57", "problem_text": "<PERSON> gave half of his stickers to <PERSON>. <PERSON> used half of the stickers and gave the rest to Kris.  K...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_58", "problem_text": "<PERSON> does laundry twice a week. Each load of laundry uses 20 gallons of water, and a gallon of water...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_59", "problem_text": "Tom plants 10 trees a year.  Every year he also chops down 2 trees a year.  He starts with 50 trees....", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.075, "L3_深层推理": 0.*********999999994}}, {"id": "problem_60", "problem_text": "<PERSON> is peeling and cutting potatoes in preparation for making potato salad for his big family reun...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_61", "problem_text": "<PERSON> has 100 centimeters of ribbon that he must cut into 4 equal parts. Each of the cut parts must ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_62", "problem_text": "<PERSON><PERSON> has 5 less cookies than <PERSON> has. <PERSON> has 12 more cookies than the Cookie Monster, and Summe...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.11666666666666667, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_63", "problem_text": "An electronics seller bought 5 phones for $700 each and gives the seller $4000 in dollar bills. How ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_64", "problem_text": "The Hortex company produces bottled carrot juices. Every day it can produce 4200 bottles of these ju...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09785714285714285, "L3_深层推理": 0.*********999999994}}, {"id": "problem_65", "problem_text": "If <PERSON> and <PERSON> have 100 feet of fence between them, and they agree to split it with <PERSON> getting ...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_66", "problem_text": "<PERSON>'s neighbor gives her a basket of 9 eggs every time she babysits their daughter. To make a Spa...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_67", "problem_text": "<PERSON>'s french onion soup recipe calls for 2 pounds of onions. He likes to double that amount. His...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.07875}}, {"id": "problem_68", "problem_text": "<PERSON> is on a diet. She eats two carrots, a salad, and a yogurt every day. The salad costs her $6, w...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_69", "problem_text": "<PERSON><PERSON><PERSON> records 18 4-minute TikTok videos each week. She spends 2 hours a week writing amateur songs...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_70", "problem_text": "A company bought $400000 worth of equipment from a retailer business, but pieces of equipment worth ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.14833333333333332, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_71", "problem_text": "<PERSON> delivers 600 newspapers in a day. He delivers 198 newspapers to District A, some to District B...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.056428571428571425, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_72", "problem_text": "<PERSON> ordered 5 croissants at $3.00 apiece, 4 cinnamon rolls at $2.50 each, 3 mini quiches for $4.0...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_73", "problem_text": "A perfume company is trying to create new scents. They already have 4 vanilla scents and 8 fruity sc...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.14285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_74", "problem_text": "<PERSON> wants to place 20 more than double the number of books in a shelving system with 6 rows and 6 ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_75", "problem_text": "<PERSON> loves to go swimming and has to swim across a 20-mile lake.  He can swim at a pace of 2 miles ...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_76", "problem_text": "<PERSON> decides to do body exercises for a whole week. He does 100 pushups, 50 squats, and 20 dumbbel...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_77", "problem_text": "<PERSON> and <PERSON> went together to the market to buy onions and potatoes. <PERSON> bought 4 times the numb...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_78", "problem_text": "<PERSON><PERSON> is designing her own dress, and decides to make it a longer dress by extending the dress by 5...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_79", "problem_text": "A pirate crew is digging for buried treasure on the island marked X on a map. They dug ten holes the...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "problem_80", "problem_text": "<PERSON> just started watching a new show.  Each episode is 20 minutes long, and there are half as many ...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.09785714285714285, "L3_深层推理": 0.01125}}, {"id": "problem_81", "problem_text": "<PERSON> qualified for a spot on the running team, so she went shopping for some athletic gear. She bo...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_82", "problem_text": "<PERSON> needed chicken sausages and fish sausages to make sausage buns at a party. He bought 38 chicke...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_83", "problem_text": "A cup of mushrooms weighs 100 grams and has 3 grams of protein.  If <PERSON> eats 200 grams of mushrooms...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_84", "problem_text": "A fruit vendor bought 50 watermelons for $80. He sold all of them at a profit of 25%. How much was e...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_85", "problem_text": "<PERSON> makes homemade macaroni and cheese once a week.  The pasta costs $1.00 a box, and he spends $3.0...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_86", "problem_text": "<PERSON> plants 2 flowers a day in his garden. After 15 days, how many flowers does he have if 5 did not...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_87", "problem_text": "<PERSON> gets 80 emails a day.  20% of those emails don't require any response.  He responds to the res...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_88", "problem_text": "My wife wants to evenly split the check but wants me to pay an additional 20% tip on our $50 dinner ...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_89", "problem_text": "<PERSON> has a monthly saving target of $1125. In April, he wants to save twice as much daily in the se...", "complexity_level": "L1_浅层推理", "confidence": 0.2175, "detailed_scores": {"L0_显式计算": 0.*****************, "L1_浅层推理": 0.2175, "L2_中等推理": 0.08642857142857144, "L3_深层推理": 0.01125}}, {"id": "problem_90", "problem_text": "<PERSON>'s farm is 200 acres, and <PERSON>'s farm is 100 acres more than twice that. How man...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.13499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_91", "problem_text": "<PERSON> plans a road trip from New Jersey to Rochester. It takes 6 days to travel by bus and half as ...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_92", "problem_text": "<PERSON>, <PERSON>, and <PERSON> are going to share 60 cherries. If <PERSON> has 30 cherries, and has 10 mor...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_93", "problem_text": "<PERSON> is 19 years old now. When he turns 24, he will be half the age of his dad but twice as old as h...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_94", "problem_text": "<PERSON> thinks he needs 56 popsicle sticks to recreate the Washington Monument for his history project. ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_95", "problem_text": "<PERSON> had $500. She spent 20% of it on clothes and then 25% of the remaining money on CDs. How much ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_96", "problem_text": "The glee club ordered 20 pizzas and ate 70% of them. The football team ordered twice as many pizzas ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_97", "problem_text": "<PERSON> and <PERSON> have 30 minutes to walk to school together. It takes them 6 minutes to get to the corn...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_98", "problem_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> have collections of Magic Cards.  There is a total of 341 cards.  <PERSON> has...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_99", "problem_text": "Doctor <PERSON> is scheduling his time for Monday. He is spending nine hours at the clinic where he wor...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.09785714285714285, "L3_深层推理": 0.01125}}, {"id": "problem_100", "problem_text": "<PERSON> runs a bicycle store. His main products are three types of bikes: MTB, BMX, and Trekking. The...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_101", "problem_text": "<PERSON> wants to buy a car for $10000 and a phone for $800. <PERSON> has $5000 from working on weeken...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_102", "problem_text": "<PERSON> has been selling her Dad's collection of 250 books for three years. Each book sells at 20$, an...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_103", "problem_text": "<PERSON> grew 3\" over the summer.  She is now 2\" shorter than <PERSON>, who is twice the size of <PERSON>.  ...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_104", "problem_text": "100 people apply for a job at Google. Of the people that apply, only 30% receive interviews. Of thos...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_105", "problem_text": "<PERSON> had two eyeshadow palettes with four colors each and three makeup sets that came with six eyesha...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.07142857142857144, "L3_深层推理": 0.0}}, {"id": "problem_106", "problem_text": "A plague infects ten people. Every day, each infected person infects six others. How many people are...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "problem_107", "problem_text": "<PERSON><PERSON> picks out 4 blouses from the 30% off rack.  The regular price for each blouse is $20.  How muc...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_108", "problem_text": "At the Burger Palace restaurant, there is an enormous jar containing red, blue and green jelly beans...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_109", "problem_text": "<PERSON> saw a 10-foot shark with 2 6-inch remoras attached to it. What percentage of the shark's body ...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_110", "problem_text": "<PERSON> is two years older than <PERSON>.  Two years ago <PERSON> was 5 years older than half <PERSON>'s age.  If Ja...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_111", "problem_text": "<PERSON> lives in an old house where the pipes will freeze if the temperature inside the house falls b...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_112", "problem_text": "<PERSON> is sewing a quilt out of old souvenir t-shirts. He has one shirt from each vacation he has been...", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.0405}}, {"id": "problem_113", "problem_text": "<PERSON> is making a comforter for his king-sized bed. He needs two pieces of fabric that are 2 feet long...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.105, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_114", "problem_text": "<PERSON> agreed to pay <PERSON> and <PERSON> $10 an hour to help clean out her attic and basement.  <PERSON> work...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_115", "problem_text": "In the first half of a soccer match, team A scores 4 goals while team B scores 2 goals fewer than te...", "complexity_level": "L2_中等推理", "confidence": 0.165, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.165, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_116", "problem_text": "A restaurant has 40 tables with 4 legs and 50 tables with 3 legs. Calculate the total number of legs...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_117", "problem_text": "<PERSON> has 30 sheep. She gets 1 kg of milk from half of them and 2 kg of milk from the other half ever...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_118", "problem_text": "<PERSON><PERSON> wants to buy new crayons. She needs them in 5 different colors and prepared $20 for this pu...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_119", "problem_text": "<PERSON><PERSON><PERSON> can lift a mountain ten times higher than <PERSON><PERSON><PERSON><PERSON> can.  But <PERSON><PERSON><PERSON><PERSON> can lift a mountain 4 t...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_120", "problem_text": "<PERSON> is half of <PERSON>’s age and five years younger than <PERSON><PERSON>. <PERSON><PERSON> is 26. How old is <PERSON>?", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_121", "problem_text": "<PERSON> wrote 9 novels last year. If this is 3 quarters of the number of novels she has written this ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_122", "problem_text": "There are 6 girls in the park. If there are twice the number of boys in the park, how many kids are ...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_123", "problem_text": "In a neighborhood, the number of rabbits pets is twelve less than the combined number of pet dogs an...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_124", "problem_text": "There are 6 trees in <PERSON>'s yard.  <PERSON> has half the number of trees that <PERSON> has.  Harry ha...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_125", "problem_text": "A basket contains 25 oranges among which 1 is bad, 20% are unripe, 2 are sour and the rest are good....", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_126", "problem_text": "<PERSON> collects stickers. Two years ago, he had 100 stickers in his collection. Last year, <PERSON> collecte...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_127", "problem_text": "<PERSON> is replacing the carpet in his bedroom.  The new carpet he's chosen costs $12 per square foo...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_128", "problem_text": "<PERSON> gets paid $20 per hour to teach and $30 to be a cheerleading coach. If she works 50 weeks a yea...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09785714285714285, "L3_深层推理": 0.*********999999994}}, {"id": "problem_129", "problem_text": "<PERSON> plans to sell all his toys and use the money to buy video games. He has 13 lego sets and he sel...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_130", "problem_text": "<PERSON><PERSON><PERSON> writes many checks every year.  Once per month he writes a check to pay the electric bill.  H...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.07142857142857144, "L3_深层推理": 0.0}}, {"id": "problem_131", "problem_text": "44 seniors need to receive awards.  Each senior receives a picture frame that costs $20.  Each pictu...", "complexity_level": "L2_中等推理", "confidence": 0.14285714285714285, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.14285714285714285, "L3_深层推理": 0.108}}, {"id": "problem_132", "problem_text": "<PERSON> was a pen pal with 5 people.  He stopped being penpals with 2 of them.  They each send 2 letter...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_133", "problem_text": "<PERSON><PERSON> bought five cell phones for $150 each for a 3-month installment. A 2% interest will be charge...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_134", "problem_text": "<PERSON> has 4 dogs. They each need a certain amount of exercise per day. The first needs to walk 1 mi...", "complexity_level": "L2_中等推理", "confidence": 0.1692857142857143, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.1692857142857143, "L3_深层推理": 0.*********999999994}}, {"id": "problem_135", "problem_text": "For one medical dosage, <PERSON><PERSON><PERSON> had to combine 14 mL of one medicine with 3 times that amount of the ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_136", "problem_text": "<PERSON><PERSON> and <PERSON> make greek orange pie. <PERSON> brought five boxes with ten oranges in each box, wh...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.08642857142857144, "L3_深层推理": 0.01125}}, {"id": "problem_137", "problem_text": "<PERSON> runs 12 miles a day for 5 days a week.  If he runs 10 miles an hour how many hours does he run...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_138", "problem_text": "<PERSON>'s math and science books weigh 2 pounds each.  Her French book weighs 4 pounds and her English...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_139", "problem_text": "<PERSON><PERSON> put 80 post-it notes in her purse before she headed out to her job at the coffee shop.  On h...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.06749999999999999, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_140", "problem_text": "<PERSON> used a piece of wire 4 feet long to support tomato plants in the garden. The wire was cut into...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_141", "problem_text": "<PERSON> starts with $200. He buys 3 books for 16 dollars each and 3 pencils for 6 dollars each. How much...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_142", "problem_text": "<PERSON> has saved 40% more in money earned by chores than his brother <PERSON>.  <PERSON> has saved $10.00...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.075, "L3_深层推理": 0.*********999999994}}, {"id": "problem_143", "problem_text": "<PERSON> is grocery shopping at a supermarket and is making sure she has enough in her budget for the i...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.07875}}, {"id": "problem_144", "problem_text": "<PERSON> is trying to increase his typing speed. He starts with 47 words per minute (WPM). After some l...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_145", "problem_text": "<PERSON> ordered one chicken meal that costs $12, 5 packs of milk that costs $3 each, 4 apples that cos...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_146", "problem_text": "While <PERSON> is gathering apples from her family’s orchard, her sister comes outside to help her. Jo...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_147", "problem_text": "In a school activity, fifty-four students are to be separated into six groups of equal size. If the ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_148", "problem_text": "<PERSON> loves going to the movies and every month his parents give him $150 to spend at the movies. Ti...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.09}}, {"id": "problem_149", "problem_text": "<PERSON> slips on a banana peel and breaks her arm. The doctor charges her $200 for the cast, $300/hou...", "complexity_level": "L2_中等推理", "confidence": 0.14999999999999997, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.013333333333333334, "L2_中等推理": 0.14999999999999997, "L3_深层推理": 0.11474999999999998}}, {"id": "problem_150", "problem_text": "<PERSON> gets a job as a waitress.  She makes $10 an hour from wages and another $15 an hour from tips....", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.075, "L3_深层推理": 0.*********999999994}}, {"id": "problem_151", "problem_text": "<PERSON> and 12 of his friends are going to see a film at the cinema, and meet up with 7 more friends t...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_152", "problem_text": "<PERSON>, <PERSON> and <PERSON><PERSON> were at the beach playing and they decided to gather some seashells. <PERSON> coll...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_153", "problem_text": "After tracking his workout routine for 8 weeks, <PERSON> discovered that he had spent 4 hours working ou...", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_154", "problem_text": "<PERSON><PERSON><PERSON> works for 8 hours every day.  She has a timer to remind her to get up and walk for 5 minute...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_155", "problem_text": "On a certain day, the total cost of filling up 20 helium balloons was $900. Two days later, the cost...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_156", "problem_text": "<PERSON><PERSON>'s handbag cost $20 less than 3 times as much as her shoes cost. If her shoes cost $80, how m...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_157", "problem_text": "An 8-year old child wants to buy a toy car which costs $12. He already has $4 savings. How many days...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.105, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_158", "problem_text": "<PERSON> had 25 lollipops. He kept 5 lollipops and shared the remaining equally with his four friends. Ho...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_159", "problem_text": "Chase and Rider can ride their bikes thrice a day for 5 days; but on two other days, they ride twice...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_160", "problem_text": "The bakers at the Beverly Hills Bakery baked 200 loaves of bread on Monday morning. They sold 93 loa...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_161", "problem_text": "<PERSON> is buying pizza for her cousin's soccer game. There are 12 team members and 3 coaches. Each te...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_162", "problem_text": "<PERSON> has 2/3 as many measuring spoons as measuring cups. If he has two dozen cups and gifts Pedr...", "complexity_level": "L1_浅层推理", "confidence": 0.1408333333333333, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1408333333333333, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_163", "problem_text": "A new program had 60 downloads in the first month. The number of downloads in the second month was t...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_164", "problem_text": "Candy has a chair rental business. During the weekdays, 60 chairs are rented each day; but during we...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.08083333333333333, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_165", "problem_text": "<PERSON><PERSON> went to his team's changing room and saw half as many robots as helmets and half as many he...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_166", "problem_text": "The vending machines sell chips for 40 cents and candy bars for 75 cents. <PERSON> spent $5 and got 3 ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_167", "problem_text": "If <PERSON><PERSON> works for the same company for 40 years, she gets an annual pension of $50,000/year. Starti...", "complexity_level": "L2_中等推理", "confidence": 0.13142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_168", "problem_text": "On a particular week, a tow truck pulled ten cars for each of the first three days and then four few...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.07142857142857144, "L3_深层推理": 0.0}}, {"id": "problem_169", "problem_text": "<PERSON> is buying fabric for new curtains. There are five windows. Each window is 35 inches wide and Lu...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_170", "problem_text": "A bag of flour is divided into 8 portions of 2 kilograms each. How much flour (in kilograms) was in ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_171", "problem_text": "A whirligig spins at five times the speed of a thingamabob. A whatchamacallit spins eleven times fas...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.08642857142857144, "L3_深层推理": 0.01125}}, {"id": "problem_172", "problem_text": "<PERSON> has 30 marbles. He gives 1/5  of them to <PERSON> and gives 10 to <PERSON>. How many marbles are lef...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_173", "problem_text": "<PERSON> hand-picks Junebugs off of her plants every summer.  On Monday, she removed 39 Junebugs.  On bo...", "complexity_level": "L2_中等推理", "confidence": 0.15785714285714283, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.08083333333333333, "L2_中等推理": 0.15785714285714283, "L3_深层推理": 0.05175}}, {"id": "problem_174", "problem_text": "<PERSON> has 9 books and <PERSON> had twice the number of <PERSON>’s books, but he lost 2 of them. How many ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_175", "problem_text": "<PERSON>'s razors come 4 to a pack and cost $4.00 a pack.  They are currently on sale for buy one get...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_176", "problem_text": "The ratio of popsicles that <PERSON> and <PERSON> have is 5:6. If the total number of popsicles they have to...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_177", "problem_text": "<PERSON> has $6000 he wishes to spend on his upcoming business trip to South Africa. He buys 6 business ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_178", "problem_text": "In a certain school, two classes have a total of 80 students. Each class has the same amount of stud...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_179", "problem_text": "If <PERSON> is 9 years old and her brother is twice her age, how old will her brother be in 3 years?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_180", "problem_text": "<PERSON> can make and upload 72 vlogs per month. But she was only able to make 18 vlogs for the first we...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_181", "problem_text": "<PERSON> bought 8 packs of 5 canvas bags for $4 each. She painted them and sold them at a craft fair for...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_182", "problem_text": "A DVD can be played 1000 times before it breaks. There are two DVDs in the public library, one has b...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.045, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_183", "problem_text": "A dance studio offers lessons to students. It costs $25 per session to rent the studio plus $1.50 pe...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_184", "problem_text": "There are 96 fourth-graders at Small Tree  School. 43 of them are girls. On Friday, 5 fourth-grade g...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_185", "problem_text": "<PERSON> starts driving from his house and travels west for 5 hours. Then he turns around and travels e...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_186", "problem_text": "<PERSON> had a son <PERSON> when he was 19.  <PERSON> is now twice as old as his sister <PERSON>, who will turn 12...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_187", "problem_text": "<PERSON> is very tired. He decides to take a nap, but he wants to finish his homework first. If <PERSON> has...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_188", "problem_text": "A robe takes 2 bolts of blue fiber and half that much white fiber.  How many bolts in total does it ...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_189", "problem_text": "<PERSON><PERSON> works for a trading company. He buys a mobile device for $20 and sells it for twice the amount...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_190", "problem_text": "<PERSON> wants to buy herself a new jacket and 2 pairs of shoes. The jacket she wants costs $30 and each...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.07875}}, {"id": "problem_191", "problem_text": "<PERSON> decided to play a prank on her friend. She got a case of 12 sodas and shook 3 of them up. Then...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_192", "problem_text": "<PERSON> orders his favorite bagels online.  Each pack of bagels costs $10.00 and has 9 bagels in the...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_193", "problem_text": "<PERSON> is a tennis pro.  He spends most of the day teaching others lessons on how to improve their ...", "complexity_level": "L2_中等推理", "confidence": 0.20285714285714285, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.058333333333333334, "L2_中等推理": 0.20285714285714285, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_194", "problem_text": "<PERSON> eats 18 pretzels a day. If his brother eats 1/2 as many, how many does his brother eat in a we...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_195", "problem_text": "There are 36 penguins sunbathing in the snow.  One-third of them jump in and swim in the ocean.  Ano...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_196", "problem_text": "A landscaping company is delivering flagstones to a customer’s yard. Each flagstone weighs 75 pounds...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_197", "problem_text": "In a family of 5, three people eat three eggs each day while the rest eat two eggs each day. If they...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.08642857142857144, "L3_深层推理": 0.01125}}, {"id": "problem_198", "problem_text": "<PERSON><PERSON> is having her birthday party at a movie theater. The fee to rent the theater is $125 for a par...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.07875}}, {"id": "problem_199", "problem_text": "<PERSON> has 5 hanging baskets to fill.  In each basket she wants to add 3 petunias and 2 sweet pot...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_200", "problem_text": "The <PERSON><PERSON><PERSON> family drove and hiked 6 hours to their vacation spot. They drove an average of 50 miles ...", "complexity_level": "L2_中等推理", "confidence": 0.15785714285714286, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15785714285714286, "L3_深层推理": 0.*********999999994}}, {"id": "problem_201", "problem_text": "<PERSON>'s baseball team has 7 more players than Carlton's. If Carlton's team has 13 players, how many p...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_202", "problem_text": "A marketing company pays its employees on a commission-based salary system. If you sell goods worth ...", "complexity_level": "L2_中等推理", "confidence": 0.10500000000000001, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.10500000000000001, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_203", "problem_text": "<PERSON> bought last year's best-selling book for $19.50. This is with a 25% discount from the original ...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.06749999999999999, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_204", "problem_text": "<PERSON> decides to buy new phones for him, his 2 kids, and his wife.  Each phone after the first 2 is h...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_205", "problem_text": "<PERSON> and <PERSON> own competing lemonade stands across the street from one another. When <PERSON> bragge...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_206", "problem_text": "<PERSON> sleeps for 10 hours a night.  He works 2 hours less than he sleeps and he walks his dog for a...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_207", "problem_text": "A cobra, which has 70 spots, has twice as many spots as a mamba. If there are 40 cobras and 60 mamba...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_208", "problem_text": "<PERSON>'s locker is 24 cubic inches. <PERSON>'s locker is half as big as <PERSON>'s locker. <PERSON>'s locke...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_209", "problem_text": "On Mondays, Wednesdays, and Fridays, college student <PERSON><PERSON> has three 1-hour  classes each day.  On Tu...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_210", "problem_text": "Belen has two kinds of pennies, a 2010 penny and a 1959 penny. The 2010 penny is three-quarters of t...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_211", "problem_text": "<PERSON>'s father's age is eight more than twice <PERSON>'s age. If <PERSON>'s mother is four years younger than...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_212", "problem_text": "<PERSON><PERSON> is 10 years old. <PERSON><PERSON> is 4 years younger than <PERSON><PERSON>. How old is <PERSON> if she is 2 years old...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_213", "problem_text": "After <PERSON> saved some money, she then spent the rest of her money on an $11 sweater and gave her b...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_214", "problem_text": "<PERSON> has twice as many socks as <PERSON> and half times as many dishes as jack. <PERSON> collected twice as...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_215", "problem_text": "<PERSON> has $100 and wants to spend it to open a rock stand. He can buy rocks for $5 each and sell them...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_216", "problem_text": "<PERSON><PERSON> is knitting a sweater with two sleeves, a collar, and a decorative rosette. The body of the sw...", "complexity_level": "L2_中等推理", "confidence": 0.13142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.0405}}, {"id": "problem_217", "problem_text": "<PERSON> lives near a beach and loves going there every day to have fun. On a particular week, he found...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_218", "problem_text": "<PERSON><PERSON> changes his face mask two times every time he goes out. If he goes out three times a day, how...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_219", "problem_text": "<PERSON> has $2 more than twice the money <PERSON> has. If <PERSON><PERSON> has $8, how much money is <PERSON> having?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_220", "problem_text": "Of the 20 available cars for rent, 12 are automatic cars, 4 are manual cars, and the rest are semi-a...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_221", "problem_text": "While walking down the street with his 3 younger siblings, <PERSON> found $20. To be fair to his sibling...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_222", "problem_text": "If every second, a bubbling spring creates a new jellyfish, how many jellyfish would 5 springs worki...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_223", "problem_text": "<PERSON> and <PERSON> are trying to divide 100 dollars between them. <PERSON> gets 4 times as much as <PERSON>. How ...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_224", "problem_text": "There are 700 bees in a hive. There are twice as many worker bees as baby bees, and there are twice ...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_225", "problem_text": "<PERSON> and four of his friends each ordered their own pizzas after football practice. Each pizza had ...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_226", "problem_text": "<PERSON><PERSON> went to their farm to pick some apples and found half as many bugs as ants in the garden. If the...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_227", "problem_text": "<PERSON><PERSON><PERSON> loves to eat fruit. She bought three apples at $1.50 each, five oranges at $0.80 each, and six...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.07875}}, {"id": "problem_228", "problem_text": "<PERSON> has one dog that is one-fourth the weight of <PERSON><PERSON><PERSON>s dog and another dog that is half the weig...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_229", "problem_text": "<PERSON> plans to watch two movies this weekend. The first movie is 1 hour and 30 minutes long while the ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_230", "problem_text": "Each solid 10-foot section of a redwood tree weighs 400 pounds. Termites ate 30% of this redwood's w...", "complexity_level": "L2_中等推理", "confidence": 0.10142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_231", "problem_text": "<PERSON> buys 3 bags of M&Ms. The first bag has 300 M&Ms in it. The second bag has 12 more M&Ms than the...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_232", "problem_text": "<PERSON> is selling gingerbread and apple pie for a fundraiser. On Saturday, he sold 10 boxes of ginger...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_233", "problem_text": "<PERSON> is spending time at the beach building sandcastles. He eventually notices that each level of a ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_234", "problem_text": "<PERSON> plays video games for 2 hours every day. He also has a part-time job where he earns $10 an ho...", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_235", "problem_text": "<PERSON> just got a new kitten. Spaying her cost $200, her 3 vaccines costs $20 each, and the kitten bro...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_236", "problem_text": "<PERSON> is part of a choir that has 52 members, 50% of which are boys and 50% of which are girls.  The ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_237", "problem_text": "<PERSON> puts clean sheets on 4 twin beds and 1 king size bed every week.  His laundry machine will only...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_238", "problem_text": "<PERSON> gets 10 new CDs.  Each CD cost $15.  He gets them for 40% off.  He decides he doesn't like 5 o...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_239", "problem_text": "<PERSON> is counting the money in his piggy bank. He has 100 pennies, 40 nickels, 20 dimes, and 40 piec...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_240", "problem_text": "The elevator in <PERSON>'s building supports a maximum load of 700 kg. An adult weighs an average of 80 ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_241", "problem_text": "There are three trees in <PERSON>'s backyard. The shortest tree has a height of 6 feet, and the second t...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_242", "problem_text": "<PERSON> notices that kids in the neighborhood are always getting things stuck in trees. Since he is an...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_243", "problem_text": "<PERSON> and 3 of his friends order 7 pizzas for lunch. Each pizza is cut into 8 slices. If <PERSON> and h...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_244", "problem_text": "While working at the restaurant, each of the forty customers who came into the restaurant gave Rafae...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_245", "problem_text": "<PERSON> adopts a dog.  He takes the dog to the groomer, which costs $100.  The groomer offers him a 30%...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_246", "problem_text": "There is one set of twins and one set of triplets. One twin is 7 years older than one triplet. If th...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_247", "problem_text": "Two cars are driving on a highway.  The first car is traveling at an average speed of 60 miles per h...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_248", "problem_text": "<PERSON> teaches 5 dance classes, every day, on the weekdays and 8 classes on Saturday.  If each class h...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_249", "problem_text": "<PERSON><PERSON> caught 3 tunas last Monday, the first tuna he caught weighs 56 kilograms, the second tuna he...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_250", "problem_text": "<PERSON> decides to take up juggling to perform at the school talent show a month in the future.  He sta...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_251", "problem_text": "<PERSON> wants to sell beeswax candles.  For every pound of beeswax, he can make 10 tapered candles. ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_252", "problem_text": "A tank of water has a depth of 17 feet on Monday. On Tuesday, the tank had 7 feet more water. On Wed...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_253", "problem_text": "<PERSON> slept 8 hours on Monday. For the next two days, she slept 2 hours less, each, because she had ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_254", "problem_text": "<PERSON> bought 60 apples from the store. He ate 2/5 of them and gave his sister 25% of the remaining ...", "complexity_level": "L2_中等推理", "confidence": 0.17642857142857143, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.17642857142857143, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_255", "problem_text": "The jumbo bottle of shampoo costs $24.00.  The directions say to use 2 pumps of shampoo and this wil...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09785714285714285, "L3_深层推理": 0.*********999999994}}, {"id": "problem_256", "problem_text": "To participate in the local community tree-planting campaign, Mr. <PERSON> planted twenty trees of Whi...", "complexity_level": "L2_中等推理", "confidence": 0.16499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.09583333333333333, "L2_中等推理": 0.16499999999999998, "L3_深层推理": 0.06974999999999999}}, {"id": "problem_257", "problem_text": "<PERSON> buys a plane.  The plane cost $150,000.  He pays $5000 a month to rent a hanger to keep it in....", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.056428571428571425, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_258", "problem_text": "The farm has 30 cows and the zoo has 20 sheep.  The zoo has twice as many cows as the farm does.  Th...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_259", "problem_text": "<PERSON> counts six birds nesting in the bushes, 2/3rd of that number of birds flying overhead, and 3 g...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1408333333333333, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_260", "problem_text": "Mr. <PERSON> sold his house for $400 000. He paid the transfer fees that amount to 3% of the selling pric...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_261", "problem_text": "For every muffin, <PERSON><PERSON><PERSON> needed 5 tablespoons of flour, 3 tablespoons of sugar, and 0.25 of a tabl...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_262", "problem_text": "Every hour, <PERSON> can bake 2 banana bread loaves in the oven.  Each banana bread loaf is cut into 8 ...", "complexity_level": "L2_中等推理", "confidence": 0.13142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.09674999999999999}}, {"id": "problem_263", "problem_text": "<PERSON> bought ten packets of crayons for her Art class. Six of the packets had eight pieces of col...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.07142857142857144, "L3_深层推理": 0.0}}, {"id": "problem_264", "problem_text": "<PERSON><PERSON> baked 55 cookies. She ate 5 five cookies and placed the rest equally into five jars. How many c...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_265", "problem_text": "<PERSON> runs a car shop and services 3 cars a day.  He is open every day of the week except Sunday and ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_266", "problem_text": "In a grocery store, four apples cost $5.20, and three oranges cost $3.30. How much will <PERSON> pay fo...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_267", "problem_text": "<PERSON> had 24 french fries, but <PERSON> took 5 of them.  <PERSON> took twice as many as <PERSON>.  Ginger gav...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_268", "problem_text": "At the beginning of the party, there were 25 men and 15 women. After an hour, 1/4 of the total numbe...", "complexity_level": "L2_中等推理", "confidence": 0.165, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.058333333333333334, "L2_中等推理": 0.165, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_269", "problem_text": "For each small task accomplished, <PERSON><PERSON> gets $0.8 while <PERSON> gets $0.5. If each of them finished 2...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_270", "problem_text": "The recent floods in Mamou’s country have left many families without food or shelter. To help, Mamo<PERSON>...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.075, "L3_深层推理": 0.*********999999994}}, {"id": "problem_271", "problem_text": "A company's HR hires 20 new employees every month to add to its total workforce. If the company's in...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.06785714285714285, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_272", "problem_text": "<PERSON> is planning a birthday party and needs .75 gift bags per invited guest, because 1/4 of att...", "complexity_level": "L2_中等推理", "confidence": 0.18785714285714286, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.18785714285714286, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_273", "problem_text": "<PERSON>'s phone can hold 6 times more photographs than can <PERSON>'s phone.  The maximum number of ph...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_274", "problem_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> are baking cookies. <PERSON><PERSON> bakes 30 cookies and <PERSON><PERSON><PERSON> bakes twice as many. If the...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_275", "problem_text": "A customer’s loyalty card at a store gives them rewards of $1 off their next purchase for every $20 ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_276", "problem_text": "<PERSON> plans to save money from working.  He gets paid $2 per hour and works 5 hours a day for 4 days ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_277", "problem_text": "<PERSON> is trying to decide whether he really needs to do his homework. There's a 50% chance that tom...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_278", "problem_text": "A large bag of Starbursts candy has 232 pieces of individually wrapped candies.  If this bag has 54 ...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_279", "problem_text": "<PERSON> had 3 pounds of candy, <PERSON> had 5 pounds of candy, and <PERSON> had 4 pounds of candy after <PERSON>...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_280", "problem_text": "<PERSON> has three times the number of pets as <PERSON>. <PERSON> has two more pets than <PERSON>. If <PERSON> has ...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "problem_281", "problem_text": "<PERSON><PERSON> posted her selfie on Instagram. She received 2000 likes on the photo after 1 week. Three week...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.045}}, {"id": "problem_282", "problem_text": "<PERSON> is feeding his livestock hay. Each goat needs 5 pounds, and each sheep needs 3 pounds less than...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_283", "problem_text": "<PERSON> and <PERSON> are sisters from Los Angeles who love collecting signatures from celebrities. Dur...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_284", "problem_text": "<PERSON> wants to buy some gumballs that cost a nickel each. If he has 8 quarters, 6 dimes, 14 nickels,...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_285", "problem_text": "<PERSON> is a cartoonist.  She can draw 5 large-sized picture scenes per day, or she can draw 6 medium...", "complexity_level": "L2_中等推理", "confidence": 0.13142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_286", "problem_text": "<PERSON> had $21. Then he saved $11 from his allowance and spent $5 on a comic book and $19 on a puzz...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_287", "problem_text": "<PERSON> spent a quarter of her money, while <PERSON><PERSON> spent one-third of her money. They each had $60. How...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_288", "problem_text": "The red rope was four times the length of the blue rope. The blue rope was 7 centimeters shorter tha...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_289", "problem_text": "<PERSON> has 2 twenty dollar bills. He buys six squirt guns for $2 each.  He also buys 3 packs of water...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_290", "problem_text": "<PERSON>'s dad brought him to watch some horse racing and his dad bet money. On the first race, he los...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_291", "problem_text": "To heat during the winter, <PERSON> ordered 850 kilos of coal. The coal is delivered in 50 kg bags, costi...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_292", "problem_text": "<PERSON> bought 3 apples, 5 bananas and 6 oranges at the grocery store. <PERSON> ate 2 pieces of the fruit. ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_293", "problem_text": "<PERSON>'s locker is half as big as <PERSON>'s locker. <PERSON>'s locker is 1/4 as big as <PERSON>'s locker. If ...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_294", "problem_text": "<PERSON> invited 20 people to a birthday party.  Each guest will eat 2 hot dogs.  He already has 4 hot d...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_295", "problem_text": "<PERSON> had 4 friends who came to visit him on a certain day. The first friend pressed on the doorbel...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_296", "problem_text": "The bananas at the supermarket cost $0.80 each, or a bunch for $3.00.  <PERSON> buys 10 bunches that av...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12428571428571428, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_297", "problem_text": "A pound of almonds costs $10 while a pound of walnuts costs $15. How much more does it cost for a mi...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.108}}, {"id": "problem_298", "problem_text": "<PERSON> rents his car out 10 times a month for 3 hours each time.  He gets paid $25 an hour.  If his ca...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_299", "problem_text": "<PERSON> traveled 110 miles in 2 hours. If <PERSON> then traveled an additional 140 miles in 3 hours, what's t...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_300", "problem_text": "A classroom has a whiteboard which is shared between the 4 teachers who take turns using the classro...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_301", "problem_text": "<PERSON><PERSON> had $70 from selling pictures he took as a hobby.  His sister <PERSON> gave him half of her $90 al...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_302", "problem_text": "<PERSON> is planting tulips. He can fit 6 red tulips in a row and 8 blue tulips in a row. If <PERSON> ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_303", "problem_text": "<PERSON> took a test yesterday that consisted of 75 questions. He completed the test at a rate of 5 ques...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_304", "problem_text": "<PERSON> has five pigs. Each one eats 4 pounds of feed, twice a day. If <PERSON> ordered 300 pounds of f...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_305", "problem_text": "Bubbles collects stuffed animals. She has three stuffed puppies, five stuffed koalas, two stuffed ze...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.08642857142857144, "L3_深层推理": 0.01125}}, {"id": "problem_306", "problem_text": "<PERSON> made 20 Valentine's cards to pass out.  Her dad brought her 2 boxes of pre-made Valentine's ca...", "complexity_level": "L2_中等推理", "confidence": 0.13142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_307", "problem_text": "<PERSON><PERSON> was running a car wash with his friend <PERSON><PERSON><PERSON> to raise money for a baseball camp. They needed...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_308", "problem_text": "<PERSON> went on a trip through Germany. She booked a hotel for 3 nights and rode the bus 7 times durin...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_309", "problem_text": "A bakery has 40 less than seven times as many loaves of bread as <PERSON> had last Friday. If <PERSON> had sev...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_310", "problem_text": "<PERSON> and her mom go to the museum. The cost of admission is $12 for adults and $10 for children....", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_311", "problem_text": "<PERSON>'s cat is 5 times faster than her turtle. If the cat can run 15 feet/second, how many feet can...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_312", "problem_text": "<PERSON> walked from Holloway Shores to Sun Valley Shores for 8 hours on a particular day. She also wal...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_313", "problem_text": "There are 50 deer in a field.  50 percent of them are bucks.  20 percent of the bucks are 8 points. ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_314", "problem_text": "There are 50 books in a small library. Half of them are written in English, and 10% in German. All o...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_315", "problem_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> went to the beach today. <PERSON><PERSON> caught 10 starfish, 6 sea horses, and 3 clownfish....", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_316", "problem_text": "A tank has a capacity of 18000 gallons. Wanda and Ms. B decided to pump water from a pond to fill th...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.108}}, {"id": "problem_317", "problem_text": "A 76-star flag has three rows of 8 stars, two rows of 6 stars and the rest are 5-star rows. How many...", "complexity_level": "L2_中等推理", "confidence": 0.165, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.165, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_318", "problem_text": "<PERSON> earned $33 for 3 hours of dog walking. If she continues to walk dogs at the same rate, how much...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_319", "problem_text": "Last night <PERSON> killed ten wolves and 15 cougars while hunting. Today <PERSON> killed three times as man...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_320", "problem_text": "<PERSON> got 3 fish.  They each need $1 worth of food a day.  How much does she spend on food in the mont...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_321", "problem_text": "<PERSON><PERSON><PERSON> has 2 fewer jewels than <PERSON>. <PERSON> has 5 more jewels than half of <PERSON>'s jewels. If Ray...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_322", "problem_text": "<PERSON> counts two zebras with 17 stripes each, a zebra with 36 stripes, and another zebra with half th...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_323", "problem_text": "<PERSON> drives for 3 hours at a speed of 60 mph and then turns around because he realizes he forgot som...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.108}}, {"id": "problem_324", "problem_text": "My kitchen floor has a total area of 200 SqFt. I want to install new square floor tiles that cost $1...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_325", "problem_text": "<PERSON> owns a chocolate factory. He produces 50,000 bars of chocolate each month. <PERSON> produces 8,00...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_326", "problem_text": "<PERSON><PERSON><PERSON> loves riding a bike. He rode it at least 5 times a week and makes 25 kilometers each time. H...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_327", "problem_text": "<PERSON> is doing the laundry, and thinks she has missed some socks. There are 50 socks that need was...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_328", "problem_text": "Two thirds of <PERSON>'s puppies are Pomeranians. One third of the Pomeranians are girls. If there are 6...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_329", "problem_text": "Mel uses a 900-watt air conditioner for 8 hours a day. This means that each hour the AC uses 900 wat...", "complexity_level": "L2_中等推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.*****************, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_330", "problem_text": "The amount of water passing through a river at one point in time is 4000 gallons. After a day of hea...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.14833333333333332, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_331", "problem_text": "<PERSON> has some coins. There are 30 more gold coins than silver coins. If she had 70 gold coins, h...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_332", "problem_text": "A raspberry bush has 6 clusters of 20 fruit each and 67 individual fruit scattered across the bush. ...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_333", "problem_text": "It takes <PERSON> 5 minutes to get to the first side of the <PERSON><PERSON><PERSON>'s cube.  The second and third sides ea...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_334", "problem_text": "<PERSON><PERSON><PERSON> bought 3 pairs of shorts, 3 pairs of pants, and 3 pairs of shoes. One pair of shorts costs $1...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.09}}, {"id": "problem_335", "problem_text": "<PERSON> is 16 years old now. Two years ago, <PERSON>’s age was twice the age of <PERSON>. How old is <PERSON> now...", "complexity_level": "L0_显式计算", "confidence": 0.255, "detailed_scores": {"L0_显式计算": 0.255, "L1_浅层推理": 0.2175, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_336", "problem_text": "<PERSON> is a dean of a private school where he teaches one class. <PERSON> is also a dean of a public school...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_337", "problem_text": "<PERSON>’s favorite food is cheese. He ate a sandwich every day this week for lunch and used 2 slices of...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_338", "problem_text": "<PERSON> has three glue sticks that are partially used. One has 1/6 left, the second has 2/3 left and...", "complexity_level": "L2_中等推理", "confidence": 0.18, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.058333333333333334, "L2_中等推理": 0.18, "L3_深层推理": 0.09674999999999999}}, {"id": "problem_339", "problem_text": "<PERSON> found a blueprint online for a toothpick sculpture she wanted to make. It requires 200 tooth...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_340", "problem_text": "In a set of magicians cards, there are 15 red cards, and 60% more green cards. Yellow cards are as m...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_341", "problem_text": "<PERSON> has four $10 bills and six $20 bills that he saved after working for Mr. <PERSON> on his farm. Ali ...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.058333333333333334, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_342", "problem_text": "<PERSON> arm wrestles 20 people.  He beats 80%.  How many people did he lose to?", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_343", "problem_text": "A glass of milk is 8 ounces of milk.  <PERSON> drinks 2 glasses of milk.  If milk has 3 calories per oun...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_344", "problem_text": "<PERSON> ate 6 grapes. Her brother wanted to make grape juice and used up 5 times as many grapes the...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_345", "problem_text": "<PERSON> is returning her overdue books to the library. She owes $0.50 cents each on 8 books, plus a fl...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_346", "problem_text": "<PERSON> needs 63 cupcakes for a birthday party happening on Saturday. He already has 8 chocolate cupcak...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_347", "problem_text": "A builder works for 4 weeks every month and for 6 days every week. If he gets paid $50 every day, ho...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_348", "problem_text": "A pink frog weighs the same as a blue beetle, the same as a green toad, 10 pounds less than a yellow...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_349", "problem_text": "<PERSON><PERSON> went to the grocery store and bought rice, beans, and pork for use in their home. It took her...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_350", "problem_text": "Out of the 200 Grade 5 students, 2/5 are boys and 2/3 of the girls are in the girl scout. How many g...", "complexity_level": "L2_中等推理", "confidence": 0.18, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.18, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_351", "problem_text": "<PERSON> has 2 houses with 3 bedrooms each.  Each bedroom has 2 windows each.  There are an additional 4...", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_352", "problem_text": "When <PERSON> wakes up, his house is 40 degrees. He spends 3 hours baking, and every hour the oven is ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_353", "problem_text": "<PERSON><PERSON><PERSON> owns a flower shop. He sells a sunflower that costs $2 each and a bouquet of sunflower that ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_354", "problem_text": "There are 20 students in <PERSON>'s class. 5 of them are good at math only, 8 of them perform well...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_355", "problem_text": "<PERSON> wants to make a video to share online of him doing a science experiment that creates a fount...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_356", "problem_text": "A pet store currently has 5 dogs, 2 cats, and 10 birds. How many legs in total do the pets in the st...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_357", "problem_text": "<PERSON> chews 4 pieces of gum a day.  A pack of gum has 15 pieces of chewing gum per pack.  How many ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_358", "problem_text": "Susan earns $5 every 10 minutes for an online task she does. If she works between 8 a.m. and 11 a.m....", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_359", "problem_text": "<PERSON>'s clothes have to be sent to the dry cleaners weekly.  Her weekly drop-off includes 5 blouses...", "complexity_level": "L2_中等推理", "confidence": 0.13142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.108}}, {"id": "problem_360", "problem_text": "<PERSON> likes to have a glass of water with breakfast, lunch and dinner.  Finally, he has one before he...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "problem_361", "problem_text": "<PERSON> purchased a box of sweets that contains 15 packs, and each pack has 60 pieces. She kept two pac...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_362", "problem_text": "In a student council election, candidate <PERSON> got 20% of the votes while candidate <PERSON> got 50% more than ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_363", "problem_text": "A Reddit group has 1000 members. If each member posts an average of 3 posts per day, what's the tota...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.12428571428571429, "L3_深层推理": 0.0225}}, {"id": "problem_364", "problem_text": "Four dogs sat in a line within the veterinarian's waiting room.  The first dog was the <PERSON><PERSON><PERSON><PERSON><PERSON>...", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_365", "problem_text": "<PERSON> is making soup for his family for dinner. He has a pot with enough soup to fill four adult's ...", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.07142857142857144, "L3_深层推理": 0.0}}, {"id": "problem_366", "problem_text": "<PERSON>'s car fuel efficiency is 10 MPG (miles per gallon). If the current price for regular gas is $3/...", "complexity_level": "L2_中等推理", "confidence": 0.1764285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.09583333333333333, "L2_中等推理": 0.1764285714285714, "L3_深层推理": 0.06974999999999999}}, {"id": "problem_367", "problem_text": "Mr. <PERSON> works at The Best Cookeries Around restaurant. On a particular day, 50 people entered the ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.09, "L3_深层推理": 0.07875}}, {"id": "problem_368", "problem_text": "<PERSON> counted 50 cars packed in their school parking lot when entering class one morning. During th...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.058333333333333334, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_369", "problem_text": "Each person in a certain household consumes 0.2 kg of rice every meal. Supposing 5 members of the ho...", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.14285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_370", "problem_text": "<PERSON> has a certain number of marbles. If he receives 2 dozen more marbles, he will have 60 marbles. I...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_371", "problem_text": "<PERSON> works at a daycare that pays him $30 every day. He worked for an entire week and spent a tota...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_372", "problem_text": "When the water is cold <PERSON> swims a mile in 16 minutes. When the water is warm <PERSON> swims a mile in 2 ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_373", "problem_text": "<PERSON><PERSON> is helping at her mom's office. She has a pile of 60 letters needing stamps, and a pile of le...", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_374", "problem_text": "<PERSON> likes to walk around the edge of the local park, which is a rectangle that measures 1.5 miles b...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.105, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "problem_375", "problem_text": "To make 1 liter of juice, <PERSON> needs 5 kilograms of oranges. Each kilogram of oranges costs $3. How m...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_376", "problem_text": "At the local Pick Your Own fruit orchard, you could pick your own peaches for $2.00 per pound, plums...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.09}}, {"id": "problem_377", "problem_text": "<PERSON> is way behind on his math homework. He has 100 math problems to complete in total. He complete...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.08083333333333333, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_378", "problem_text": "<PERSON> and her friends watch 4 movies every Saturday and half the number of movies on Sunday than on S...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_379", "problem_text": "A basic manicure starts at $35 while a pedicure starts at $40. A certain salon offers 20% off if you...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_380", "problem_text": "<PERSON> bought a car for $20000 in 2007. The price of the car depreciates at a constant rate of 21% per ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_381", "problem_text": "<PERSON> is a door-to-door saleswoman. She sold a third of her vacuum cleaners at the green house, 2 ...", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_382", "problem_text": "While on vacation in Bali, <PERSON><PERSON> bought a hat from a craftsman worth $70. If she gave the craftsman f...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_383", "problem_text": "<PERSON> needs to buy potting soil to fill the raised beds in his vegetable garden. He has 10 raised beds...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_384", "problem_text": "<PERSON> works as a pet groomer. This week, she has 8 dogs that need to be bathed, 5 cats that need t...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_385", "problem_text": "One dwarf can mine 12 pounds of ore per day with his bare hands. He can mine twice as much with an i...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_386", "problem_text": "<PERSON> has to take two math tests to pass 6th grade. She must correctly answer 70% of the total ques...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.09, "L3_深层推理": 0.07875}}, {"id": "problem_387", "problem_text": "A company spends $15000 on advertising for a year, and then spends a third of that amount on adverti...", "complexity_level": "L1_浅层推理", "confidence": 0.23083333333333333, "detailed_scores": {"L0_显式计算": 0.*****************, "L1_浅层推理": 0.23083333333333333, "L2_中等推理": 0.075, "L3_深层推理": 0.01125}}, {"id": "problem_388", "problem_text": "<PERSON> is shopping at a clothing store. The store has a buy one get one 50% off deal on T-shirts. Jake...", "complexity_level": "L2_中等推理", "confidence": 0.17642857142857143, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.058333333333333334, "L2_中等推理": 0.17642857142857143, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_389", "problem_text": "<PERSON> and <PERSON> decide to see who can get home from school the fastest. <PERSON> lives further away than ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_390", "problem_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> are trying to catch them all, <PERSON><PERSON><PERSON> that is.  Together they have caught ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_391", "problem_text": "There are 4 roses in the vase. There are 7 more dahlias than roses in the vase. How many flowers are...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_392", "problem_text": "<PERSON> is catering a birthday party for her sister and invited 16 people. 10 people want the chicken ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_393", "problem_text": "<PERSON>’s basketball games are 4 quarters that are each 12 minutes long. In the last quarter, there wa...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_394", "problem_text": "<PERSON> buys milk for 2 dollars, eggs for 3 dollars, light bulbs for 3 dollars, cups for 3 dollars, and...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_395", "problem_text": "<PERSON>'s weight is 55 kg. <PERSON>’s weight is 16 kg more than <PERSON>’s weight. <PERSON>’s weight is 8 ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_396", "problem_text": "<PERSON> makes herself iced coffee by brewing hot coffee and adding ice cubes. Each ice cube cools th...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_397", "problem_text": "<PERSON> makes her own madeleine cookies and eats 2 a night as a treat.  She wants to make enough coo...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_398", "problem_text": "A tomato vendor decides to switch who he buys his tomatoes for.  He sells 500 tomatoes a day.  He us...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_399", "problem_text": "<PERSON> has been collecting shells since she turned 5 years old, every month she collects one shell. ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_400", "problem_text": "<PERSON> is making popsicles to sell and to save money he is making his own popsicle sticks. He can...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.09}}, {"id": "problem_401", "problem_text": "Each sleeve of graham crackers makes the base for 8 large smores.  There are 3 sleeves in a box.  If...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_402", "problem_text": "Ducks need to eat 3.5 pounds of insects each week to survive. If there is a flock of ten ducks, how ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_403", "problem_text": "<PERSON> has 30 roses. She gave 24 roses to her mother. She bought 15 more roses.  How many roses di...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_404", "problem_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON> painted 56 easter eggs. <PERSON> and <PERSON> painted the same num...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_405", "problem_text": "<PERSON> wants to buy a $320.00 pair of shoes and a matching belt that is $32.00.  Her part-time job pay...", "complexity_level": "L2_中等推理", "confidence": 0.11999999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.11999999999999998, "L3_深层推理": 0.08549999999999999}}, {"id": "problem_406", "problem_text": "A new arcade opens up and <PERSON> decides to play with his 3 friends.  <PERSON> can play a game with 1 quar...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_407", "problem_text": "<PERSON> and his brother take turns spelling out the longest letter words they know and counting the nu...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_408", "problem_text": "<PERSON> drinks a bottle of water every half hour.  A normal sudoku puzzle takes him 45 minutes.  An ext...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_409", "problem_text": "There are 9 Fast and the Furious movies, <PERSON><PERSON> has seen each one in the theatre three times. She has...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.12428571428571429, "L3_深层推理": 0.0225}}, {"id": "problem_410", "problem_text": "<PERSON> is taking a history test. She correctly answers 80% of the multiple-choice questions, 90% of the...", "complexity_level": "L2_中等推理", "confidence": 0.1614285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.1614285714285714, "L3_深层推理": 0.126}}, {"id": "problem_411", "problem_text": "<PERSON> really wants to ride the biggest roller coaster at the park. You have to be 4 feet tall to ride...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_412", "problem_text": "There are 29 pupils in a class. The teacher has 9 coupons; each coupon can be redeemed for 100 bottl...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_413", "problem_text": "Cedar Falls Middle School has students in grades 4 – 7 and each year they are challenged to earn as ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_414", "problem_text": "<PERSON> and <PERSON><PERSON><PERSON> are movie stars heading to the premiere of their latest film. Bern<PERSON>tte wants ...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_415", "problem_text": "<PERSON><PERSON><PERSON> is 44 years old today.  In 7 years, he will be three times as old as his nephew.  How old is ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_416", "problem_text": "<PERSON> is practicing for his role in a theater production. He has to memorize his lines for two scenes...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_417", "problem_text": "<PERSON><PERSON><PERSON> bought a computer, 2 monitors, and a printer for $2,400. He paid $400 less for the printer tha...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_418", "problem_text": "<PERSON> and <PERSON><PERSON> are at the beach. <PERSON> rents a canoe for $30 an hour and <PERSON><PERSON> rents a banana boa...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_419", "problem_text": "<PERSON> sold 80 cookies for $1 each and 60 cupcakes for $4 each. She gave her two sisters $10 each f...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_420", "problem_text": "<PERSON> needs to get more toys for his doggie shelter. Each dog needs one toy. <PERSON> currently has 4 t...", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_421", "problem_text": "To raise money for their class fund, each of the 30 students from one class sold lollipops that cost...", "complexity_level": "L2_中等推理", "confidence": 0.18428571428571427, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.18428571428571427, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_422", "problem_text": "<PERSON> is popping popcorn for a snack. As the pan of kernels heats up, the kernels start popping fa...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.075, "L3_深层推理": 0.*********999999994}}, {"id": "problem_423", "problem_text": "<PERSON> bought 3 boxes of Coco Crunch and 5 boxes of Fruit Loops this week. Last week she bought 4 bo...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_424", "problem_text": "Out of the 150 teachers on the school basketball court, 60% are history teachers. If the rest of the...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.12583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_425", "problem_text": "In the first week, <PERSON> read for 15 minutes each night before going to sleep. In the second week, sh...", "complexity_level": "L2_中等推理", "confidence": 0.15785714285714286, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.15785714285714286, "L3_深层推理": 0.*********999999994}}, {"id": "problem_426", "problem_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> divided the home chores so that each person had something to do while the ot...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_427", "problem_text": "A company decided to take its employees on a tour to explore an ancient site. The employees were div...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_428", "problem_text": "<PERSON>'s teacher, leaves as homework the reading of a 200-page book. The assignment is to be completed...", "complexity_level": "L2_中等推理", "confidence": 0.1464285714285714, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.06749999999999999, "L2_中等推理": 0.1464285714285714, "L3_深层推理": 0.05175}}, {"id": "problem_429", "problem_text": "<PERSON> wants to buy a bookshelf and a sofa and has received two offers. Furniture United's offer inc...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_430", "problem_text": "<PERSON> took his allowance of $20 and added an extra $10 to it.  He then invested this sum of money, ...", "complexity_level": "L1_浅层推理", "confidence": 0.20833333333333331, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_431", "problem_text": "<PERSON> decided to make muffaletta sandwiches for the big game.  Each sandwich required 1 pound each o...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.09}}, {"id": "problem_432", "problem_text": "<PERSON> has 4 bedrooms in his house.  They measure 20 by 12 feet each.  The living room is 5 times bigge...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_433", "problem_text": "Digimon had its 20th anniversary.  When it came out <PERSON> was twice as old as <PERSON>.  If <PERSON> is 28 now...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_434", "problem_text": "<PERSON> went to a store to buy some sweets. He bought 7 candies of type A and 10 candies of type B. One...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.07875}}, {"id": "problem_435", "problem_text": "<PERSON> was told he could earn $3.00 for doing his laundry,  $1.50 for cleaning his room, $0.75 for ta...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.09}}, {"id": "problem_436", "problem_text": "The light on a lighthouse blinks 255 times in 5 minutes. How long will it take the light to blink 45...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_437", "problem_text": "<PERSON> works at a pet store and is distributing straw among the rodents. The rats are kept in 3 cag...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_438", "problem_text": "<PERSON><PERSON><PERSON>'s plants need to be watered every day. She has 20 plants. 4 of her plants need half of a cup ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_439", "problem_text": "<PERSON> buys a ring for his bride-to-be.  The diamond cost $600 and the gold cost $300.  He pays a 30%...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.06749999999999999, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_440", "problem_text": "<PERSON><PERSON> is 60 inches tall. His two-story house is 7 times as high as he is. The floor to the ceilin...", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_441", "problem_text": "The great pyramids sit at an angle of 32 degrees from the ground when you stand at the entrance of S...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_442", "problem_text": "<PERSON> went to buy books from the store and spent $300 on the books. If each book was $15 and she gav...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_443", "problem_text": "Twenty students are working together to raise money for a charity. Each earns the same amount. The c...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_444", "problem_text": "If <PERSON> eats Cheerios every day for breakfast, he'll lose 1.25 pounds/week. If he eats donuts ever...", "complexity_level": "L2_中等推理", "confidence": 0.165, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.165, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_445", "problem_text": "Two friends are playing pick-up sticks. While playing, they noticed that there are 9 red sticks, and...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_446", "problem_text": "To be promoted to the next school year, <PERSON> takes 3 tests that together must total at least 42 poin...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_447", "problem_text": "Mrs. <PERSON> made a dozen bread rolls for breakfast. After feeding her 6 children with one each, she...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_448", "problem_text": "<PERSON> is choosing between two jobs. Job A pays $15 an hour for 2000 hours a year, and is in a state w...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.09}}, {"id": "problem_449", "problem_text": "A hospital sees 500 people a day.  Each patient is seen for an average of 24 minutes.  The doctors c...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.045}}, {"id": "problem_450", "problem_text": "<PERSON> is counting his Pokemon cards. He has 30 fire type, 20 grass type, and 40 water type. If he lo...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.*****************, "L3_深层推理": 0.*********999999994}}, {"id": "problem_451", "problem_text": "Samwell owns an appliances store. For this week, one-fourth of their sales are smart TVs, one-eighth...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.16499999999999998, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.029249999999999998}}, {"id": "problem_452", "problem_text": "The area of <PERSON>'s rectangular bedroom is 360 square feet. If the length of his room is 3 yards, w...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_453", "problem_text": "The number of songs in a playlist is 300. If <PERSON> has 20 such playlists, and each song is 10 hours l...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_454", "problem_text": "If <PERSON> weighs 150 pounds and <PERSON> weighs 20 pounds less than <PERSON>.  And their friend <PERSON> weighs tw...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_455", "problem_text": "<PERSON> bought 23 cookies and <PERSON> gave her 42 cookies. The other day her brother ate 44 of those co...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_456", "problem_text": "<PERSON> decides to run 3 sprints 3 times a week.  He runs 60 meters each sprint.  How many total meter...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_457", "problem_text": "<PERSON> works for a manufacturing company in the packaging division.  He gets paid $0.20 for every pac...", "complexity_level": "L2_中等推理", "confidence": 0.16142857142857142, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.16142857142857142, "L3_深层推理": 0.063}}, {"id": "problem_458", "problem_text": "In 10 years, <PERSON> will be 18 years old. In how many years will her age be thrice her present age?", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_459", "problem_text": "<PERSON> buys one small pizza at $8 and one family-size pizza that costs 3 times as much as the small p...", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_460", "problem_text": "<PERSON><PERSON> took part in a 100-meter race.  She started off in first, but then fell back 5 spots.  She th...", "complexity_level": "L2_中等推理", "confidence": 0.165, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.165, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_461", "problem_text": "A phone tree is used to contact families and relatives of <PERSON>'s deceased coworker. <PERSON> decided to ca...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_462", "problem_text": "<PERSON> sells 20 cupcakes for $2 for each cupcake and  40  cookies at $1 each.  She buys five trays ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_463", "problem_text": "3 customers were kicked out of the Walmart for refusing to wear masks. A number equals to four times...", "complexity_level": "L1_浅层推理", "confidence": 0.18583333333333332, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.18583333333333332, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_464", "problem_text": "<PERSON> and <PERSON> were relay race partners on a running team. <PERSON> was able to run each mile twice ...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.10142857142857144, "L3_深层推理": 0.0225}}, {"id": "problem_465", "problem_text": "<PERSON> takes two bus trips five days a week. If each bus trip costs her $2.20, how much would she sav...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.*****************, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_466", "problem_text": "<PERSON> decides to build a program capable of identifying cancer cells.  He gets initial funding of $10...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_467", "problem_text": "<PERSON><PERSON> gets $5 as pocket money every day from her parents. She buys 4 lollipops worth 25 cents each ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.15, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_468", "problem_text": "The great dragon, <PERSON><PERSON>, sat high atop mount Farbo, breathing fire upon anything within a distance of...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.13499999999999998, "L2_中等推理": 0.11285714285714285, "L3_深层推理": 0.0225}}, {"id": "problem_469", "problem_text": "A pet shop had 6 cages of rodents. 3 of the cages have 10 hamsters and the other 3 have 5 guinea pig...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_470", "problem_text": "A teacher uses a 5-inch piece of chalk to write math equations on a chalkboard for his students. The...", "complexity_level": "L2_中等推理", "confidence": 0.17642857142857143, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.17642857142857143, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_471", "problem_text": "If Chester eats 3 eggs a day for 30 days and then increases it to 5 eggs a day for 30 days, how many...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.135, "L3_深层推理": 0.*********999999994}}, {"id": "problem_472", "problem_text": "The employees of Google went on a day trip. 4 buses were rented that have the capacity of holding 60...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.09, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_473", "problem_text": "The <PERSON><PERSON><PERSON><PERSON> sisters are driving home with 7 kittens adopted from the local animal shelter when the...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_474", "problem_text": "The price of a laptop is $1000. If you get a 20% discount, how much do you have to pay?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.13499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_475", "problem_text": "<PERSON> needs to drink 60 ml of water for each kilometer she runs. If her gym teacher tells her to ru...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_476", "problem_text": "<PERSON><PERSON> bought 20 sacks of rice and gave 3 sacks to her cousin and 4 sacks to her brother, if there ar...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_477", "problem_text": "<PERSON> earns $10 an hour on Math tutoring.  He tutored 5 hours for the first week and 8 hours for the...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_478", "problem_text": "In a company of 50 employees, 20% of the employees are management.  Out of this 20%, only 30% overse...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_479", "problem_text": "<PERSON>'s rate per hour for the first 40 hours she works each week is $10. She also receives an overti...", "complexity_level": "L2_中等推理", "confidence": 0.15785714285714286, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.15785714285714286, "L3_深层推理": 0.*********999999994}}, {"id": "problem_480", "problem_text": "<PERSON> has 6 more candies than <PERSON>. <PERSON> has twice as many candies as <PERSON>. If <PERSON> has 54 candi...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_481", "problem_text": "In his training as a professional athlete, <PERSON> runs 5000 meters every day. His coach, however, wan...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.08083333333333333, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_482", "problem_text": "<PERSON>'s family wants to compare the cost of the two different amusement parks. The first amusement pa...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.10142857142857142, "L3_深层推理": 0.06749999999999999}}, {"id": "problem_483", "problem_text": "A house and a lot cost $120,000. If the house cost three times as much as the lot, how much did the ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.13499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_484", "problem_text": "Movie A was one-fourth the length of Movie B. Movie B was 5 minutes longer than Movie C. Movie C was...", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_485", "problem_text": "Grandma walks 3 miles every day on her favorite walking trail, which includes 2 miles of walking on ...", "complexity_level": "L2_中等推理", "confidence": 0.17642857142857143, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.045, "L2_中等推理": 0.17642857142857143, "L3_深层推理": 0.07424999999999998}}, {"id": "problem_486", "problem_text": "<PERSON> is learning to write and decides to keep re-writing the alphabet until she knows it. She write...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.018}}, {"id": "problem_487", "problem_text": "<PERSON> is fundraising for his charity by selling brownies for $3 a slice and cheesecakes for $4 a sli...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "problem_488", "problem_text": "A nurses’ station orders bandages in bulk packs of 50. On the first day, the nurses used 38 bandages...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_489", "problem_text": "<PERSON> and <PERSON> are cousins. <PERSON> was born 6 years before <PERSON>. <PERSON> had a son at the...", "complexity_level": "L1_浅层推理", "confidence": 0.1725, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_490", "problem_text": "Comet Halley orbits the sun every 75 years. <PERSON>'s dad saw the Comet when he was 30 years old. Bill ...", "complexity_level": "L1_浅层推理", "confidence": 0.*****************, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_491", "problem_text": "A family of 6 (2 adults and 4 kids) are to divide a watermelon such that each adult gets a slice tha...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.12785714285714284, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_492", "problem_text": "<PERSON> decides he wants to replace his movie collection with digital versions.  He has 600 movies.  A ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.075, "L3_深层推理": 0.*********999999994}}, {"id": "problem_493", "problem_text": "<PERSON><PERSON> has 3 more than twice the number of books that <PERSON> has. If <PERSON><PERSON> has 21 books, how many does...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_494", "problem_text": "<PERSON>, <PERSON>, and <PERSON> ran together. <PERSON> ran 4 miles. <PERSON> ran 5 times what <PERSON> ran and...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.1725, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_495", "problem_text": "If a bag of marbles costs $20 and the price increases by 20% of the original price every two months,...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.105, "L3_深层推理": 0.033749999999999995}}, {"id": "problem_496", "problem_text": "At the end of the school year, <PERSON> asked her teacher if she could have the 3 boxes of 64 crayons si...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.14642857142857144, "L3_深层推理": 0.*********999999994}}, {"id": "problem_497", "problem_text": "<PERSON><PERSON> is 8 years old. His brother is twice his age. <PERSON><PERSON>'s sister is 25% younger than him. What is th...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_498", "problem_text": "In Mr<PERSON><PERSON>'s class of 30 students, 20% of the class are football players.  Out of the remaining cl...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "problem_499", "problem_text": "<PERSON> buys a bedroom set for $3000.  He sells his old bedroom for $1000 and uses that to pay for part ...", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.*****************, "L2_中等推理": 0.056428571428571425, "L3_深层推理": 0.033749999999999995}}], "distribution": {"L0_显式计算": 309, "L1_浅层推理": 108, "L2_中等推理": 83, "L3_深层推理": 0}, "confidence_stats": {"L0_显式计算": [0.21, 0.15, 0.21, 0.21, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.255, 0.21, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.255, 0.15, 0.255, 0.21, 0.15, 0.21, 0.21, 0.15, 0.15, 0.15, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.3, 0.255, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.15, 0.15, 0.3, 0.3, 0.15, 0.21, 0.15, 0.15, 0.255, 0.21, 0.255, 0.255, 0.21, 0.15, 0.15, 0.15, 0.15, 0.3, 0.15, 0.255, 0.21, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.21, 0.15, 0.21, 0.255, 0.15, 0.15, 0.255, 0.15, 0.3, 0.21, 0.21, 0.255, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.21, 0.21, 0.15, 0.255, 0.255, 0.15, 0.15, 0.255, 0.21, 0.21, 0.21, 0.255, 0.255, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.21, 0.21, 0.15, 0.15, 0.21, 0.15, 0.21, 0.21, 0.15, 0.15, 0.15, 0.15, 0.3, 0.21, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.3, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.255, 0.15, 0.255, 0.15, 0.255, 0.21, 0.15, 0.15, 0.15, 0.15, 0.255, 0.15, 0.15, 0.15, 0.255, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.3, 0.15, 0.15, 0.3, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.15, 0.15, 0.21, 0.15, 0.15, 0.21, 0.15, 0.15, 0.21, 0.15, 0.21, 0.21, 0.15, 0.15, 0.21, 0.15, 0.15], "L1_浅层推理": [0.*****************, 0.*****************, 0.*****************, 0.20833333333333331, 0.18583333333333332, 0.1725, 0.16333333333333333, 0.20833333333333331, 0.1725, 0.17666666666666667, 0.1725, 0.1725, 0.*****************, 0.1725, 0.1725, 0.18583333333333332, 0.1725, 0.1725, 0.*****************, 0.2175, 0.20833333333333331, 0.1725, 0.18583333333333332, 0.18583333333333332, 0.16333333333333333, 0.1725, 0.1725, 0.1725, 0.1725, 0.*****************, 0.1725, 0.1725, 0.1725, 0.1725, 0.1725, 0.16333333333333333, 0.1725, 0.1408333333333333, 0.20833333333333331, 0.18583333333333332, 0.20833333333333331, 0.1725, 0.1725, 0.1725, 0.1725, 0.1725, 0.1725, 0.1725, 0.*****************, 0.1725, 0.15, 0.*****************, 0.*****************, 0.*****************, 0.1725, 0.*****************, 0.*****************, 0.*****************, 0.1725, 0.1725, 0.*****************, 0.*****************, 0.1725, 0.1725, 0.*****************, 0.20833333333333331, 0.18583333333333332, 0.20833333333333331, 0.*****************, 0.1725, 0.1725, 0.*****************, 0.16333333333333333, 0.1725, 0.18583333333333332, 0.18583333333333332, 0.1725, 0.20833333333333331, 0.15, 0.16333333333333333, 0.1725, 0.20833333333333331, 0.1725, 0.15, 0.15, 0.*****************, 0.23083333333333333, 0.1725, 0.*****************, 0.1725, 0.1725, 0.1725, 0.16333333333333333, 0.18583333333333332, 0.20833333333333331, 0.*****************, 0.15, 0.1725, 0.18583333333333332, 0.*****************, 0.18583333333333332, 0.*****************, 0.15, 0.18583333333333332, 0.1725, 0.1725, 0.1725, 0.*****************], "L2_中等推理": [0.11999999999999998, 0.18, 0.13499999999999998, 0.18, 0.16142857142857142, 0.10142857142857142, 0.10142857142857142, 0.16142857142857142, 0.17642857142857143, 0.16142857142857142, 0.16142857142857142, 0.1464285714285714, 0.1464285714285714, 0.15, 0.165, 0.14285714285714285, 0.16142857142857142, 0.1692857142857143, 0.1464285714285714, 0.14999999999999997, 0.15, 0.1464285714285714, 0.13142857142857142, 0.15, 0.15785714285714283, 0.16142857142857142, 0.15, 0.20285714285714285, 0.13499999999999998, 0.15785714285714286, 0.10500000000000001, 0.13499999999999998, 0.13499999999999998, 0.1464285714285714, 0.11999999999999998, 0.13142857142857142, 0.1464285714285714, 0.1464285714285714, 0.10142857142857142, 0.17642857142857143, 0.16499999999999998, 0.1464285714285714, 0.13142857142857142, 0.165, 0.18785714285714286, 0.13142857142857142, 0.13499999999999998, 0.11999999999999998, 0.11999999999999998, 0.13142857142857142, 0.13499999999999998, 0.16142857142857142, 0.11999999999999998, 0.165, 0.11999999999999998, 0.*****************, 0.1464285714285714, 0.18, 0.15, 0.18, 0.13142857142857142, 0.1764285714285714, 0.15, 0.15, 0.13499999999999998, 0.16142857142857142, 0.17642857142857143, 0.16142857142857142, 0.11999999999999998, 0.1614285714285714, 0.18428571428571427, 0.15785714285714286, 0.1464285714285714, 0.13499999999999998, 0.165, 0.13499999999999998, 0.16142857142857142, 0.165, 0.17642857142857143, 0.15785714285714286, 0.13499999999999998, 0.13499999999999998, 0.17642857142857143], "L3_深层推理": []}, "percentage_distribution": {"L0_显式计算": 61.8, "L1_浅层推理": 21.6, "L2_中等推理": 16.6, "L3_深层推理": 0.0}, "average_confidence": {"L0_显式计算": 0.1757766990291257, "L1_浅层推理": 0.18124228395061728, "L2_中等推理": 0.1489931153184165, "L3_深层推理": 0.0}, "dir_score": 0.548}