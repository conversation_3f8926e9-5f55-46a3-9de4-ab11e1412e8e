{"dataset_name": "MATH", "total_problems": 500, "classification_results": [{"id": "problem_0", "problem_text": "A circle has center $(3, -2)$ and radius 4. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_1", "problem_text": "A circle has center $(3, -2)$ and radius 3. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_2", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_3", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_4", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_5", "problem_text": "Solve for $x$: $5^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_6", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_7", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_8", "problem_text": "A circle has center $(3, -2)$ and radius 7. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_9", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_10", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_11", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_12", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_13", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_14", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_15", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_16", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_17", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_18", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_19", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_20", "problem_text": "Solve for $x$: $3^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_21", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_22", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_23", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 11$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_24", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_25", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_26", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_27", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_28", "problem_text": "A circle has center $(3, -2)$ and radius 6. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_29", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 17$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_30", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_31", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 6?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_32", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_33", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_34", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_35", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 5, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_36", "problem_text": "A circle has center $(3, -2)$ and radius 7. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_37", "problem_text": "A circle has center $(3, -2)$ and radius 2. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_38", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 6} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_39", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_40", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_41", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_42", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_43", "problem_text": "A circle has center $(3, -2)$ and radius 3. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_44", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 6?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_45", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_46", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 4, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_47", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_48", "problem_text": "A circle has center $(3, -2)$ and radius 6. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_49", "problem_text": "In how many ways can 4 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_50", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 6, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_51", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 12$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_52", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_53", "problem_text": "A circle has center $(1, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_54", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 10, the remainder is 3...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_55", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_56", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_57", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 5?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_58", "problem_text": "In how many ways can 3 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_59", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_60", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_61", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_62", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_63", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 5} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_64", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 9, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_65", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_66", "problem_text": "A circle has center $(3, -5)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_67", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_68", "problem_text": "Solve for $x$: $5^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_69", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 8, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_70", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_71", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_72", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_73", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_74", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_75", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_76", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_77", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 10} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_78", "problem_text": "In how many ways can 4 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_79", "problem_text": "In how many ways can 5 distinct balls be placed into 6 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_80", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_81", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_82", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_83", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_84", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 10$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_85", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_86", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_87", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 17$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_88", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_89", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_90", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_91", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_92", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_93", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_94", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_95", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 18$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_96", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 22$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_97", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_98", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_99", "problem_text": "A circle has center $(3, -2)$ and radius 6. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_100", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_101", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_102", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_103", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_104", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 10$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_105", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_106", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 10, the remainder is 3...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_107", "problem_text": "A circle has center $(1, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_108", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_109", "problem_text": "A circle has center $(3, -1)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_110", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_111", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 6} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_112", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_113", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_114", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 21$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_115", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_116", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_117", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_118", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_119", "problem_text": "A circle has center $(5, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_120", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_121", "problem_text": "Solve for $x$: $2^{x+4} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_122", "problem_text": "A circle has center $(3, -2)$ and radius 3. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_123", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_124", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_125", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_126", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_127", "problem_text": "A circle has center $(6, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_128", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_129", "problem_text": "In how many ways can 2 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_130", "problem_text": "In how many ways can 4 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_131", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_132", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_133", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_134", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_135", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_136", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_137", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_138", "problem_text": "A circle has center $(3, -2)$ and radius 7. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_139", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 10} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_140", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_141", "problem_text": "In how many ways can 4 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_142", "problem_text": "In how many ways can 5 distinct balls be placed into 4 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_143", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_144", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_145", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_146", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_147", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_148", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 8, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_149", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_150", "problem_text": "A circle has center $(2, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_151", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_152", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_153", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_154", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 6} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_155", "problem_text": "In how many ways can 8 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_156", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_157", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_158", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_159", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_160", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_161", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_162", "problem_text": "Solve for $x$: $2^{x+2} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_163", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_164", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_165", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 7$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_166", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_167", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_168", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 11$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_169", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_170", "problem_text": "A circle has center $(3, -2)$ and radius 3. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_171", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_172", "problem_text": "In how many ways can 5 distinct balls be placed into 6 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_173", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 9, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_174", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_175", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_176", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_177", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_178", "problem_text": "A circle has center $(3, -1)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_179", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 4?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_180", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_181", "problem_text": "Solve for $x$: $2^{x+2} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_182", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_183", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_184", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_185", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_186", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_187", "problem_text": "In how many ways can 5 distinct balls be placed into 4 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_188", "problem_text": "Solve for $x$: $3^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_189", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_190", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_191", "problem_text": "In how many ways can 4 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_192", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_193", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_194", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_195", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_196", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_197", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_198", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_199", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_200", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_201", "problem_text": "In how many ways can 5 distinct balls be placed into 4 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_202", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_203", "problem_text": "In how many ways can 5 distinct balls be placed into 6 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_204", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_205", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 22$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_206", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_207", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_208", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 6, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_209", "problem_text": "In how many ways can 4 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_210", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_211", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 4} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_212", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 6?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_213", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_214", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_215", "problem_text": "Solve for $x$: $2^{x+4} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_216", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_217", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 2?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_218", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 22$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_219", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_220", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 6} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_221", "problem_text": "Solve for $x$: $3^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_222", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_223", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_224", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_225", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_226", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_227", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_228", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_229", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 4} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_230", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_231", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_232", "problem_text": "In how many ways can 5 distinct balls be placed into 4 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_233", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 4} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_234", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_235", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 2?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_236", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 5, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_237", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_238", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 6, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_239", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_240", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_241", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_242", "problem_text": "In how many ways can 3 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_243", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_244", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_245", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_246", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 6?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_247", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_248", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 5?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_249", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_250", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_251", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_252", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 5} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_253", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 8, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_254", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_255", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_256", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_257", "problem_text": "In how many ways can 5 distinct balls be placed into 4 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_258", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 4} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_259", "problem_text": "A circle has center $(3, -2)$ and radius 6. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_260", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 4} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_261", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_262", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_263", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_264", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_265", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_266", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_267", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_268", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_269", "problem_text": "Solve for $x$: $4^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_270", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 12$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_271", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_272", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_273", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_274", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_275", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 8, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_276", "problem_text": "In how many ways can 2 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_277", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_278", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 10} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_279", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_280", "problem_text": "A circle has center $(2, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_281", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_282", "problem_text": "A circle has center $(3, -1)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_283", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_284", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_285", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_286", "problem_text": "A circle has center $(4, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_287", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_288", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_289", "problem_text": "A circle has center $(5, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_290", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_291", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 7$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_292", "problem_text": "A circle has center $(6, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_293", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 4} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_294", "problem_text": "A circle has center $(1, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_295", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_296", "problem_text": "A circle has center $(3, -2)$ and radius 4. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_297", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 6, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_298", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_299", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_300", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_301", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_302", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_303", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_304", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 10, the remainder is 3...", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_305", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_306", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_307", "problem_text": "Solve for $x$: $5^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_308", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_309", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_310", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_311", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_312", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_313", "problem_text": "A circle has center $(3, -1)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_314", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 10} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_315", "problem_text": "Solve for $x$: $2^{x+2} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_316", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 21$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_317", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_318", "problem_text": "Solve for $x$: $4^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_319", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_320", "problem_text": "Solve for $x$: $2^{x+4} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_321", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_322", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_323", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_324", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_325", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_326", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_327", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_328", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_329", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 9, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_330", "problem_text": "A circle has center $(3, -2)$ and radius 2. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_331", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_332", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_333", "problem_text": "Solve for $x$: $1^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_334", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_335", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_336", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_337", "problem_text": "A circle has center $(3, -2)$ and radius 3. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_338", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_339", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 9} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_340", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 4?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_341", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 11$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_342", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_343", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_344", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_345", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_346", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_347", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_348", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_349", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_350", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_351", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 4?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_352", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_353", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_354", "problem_text": "Solve for $x$: $2^{x+3} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_355", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_356", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 6?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_357", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 10$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_358", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 9, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_359", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_360", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_361", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_362", "problem_text": "A circle has center $(3, -2)$ and radius 6. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_363", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_364", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_365", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_366", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_367", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_368", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 7$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_369", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 5} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_370", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_371", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 6, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_372", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 11$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_373", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_374", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_375", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_376", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_377", "problem_text": "A circle has center $(3, -2)$ and radius 2. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_378", "problem_text": "A circle has center $(1, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_379", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_380", "problem_text": "In how many ways can 6 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_381", "problem_text": "Solve for $x$: $5^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_382", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_383", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_384", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_385", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 9} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_386", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_387", "problem_text": "In how many ways can 7 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_388", "problem_text": "Solve for $x$: $5^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_389", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_390", "problem_text": "In how many ways can 8 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_391", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 9} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_392", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_393", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_394", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_395", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_396", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_397", "problem_text": "Solve for $x$: $2^{x+3} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_398", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_399", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_400", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_401", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_402", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 11$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_403", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_404", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_405", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_406", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 21$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_407", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_408", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_409", "problem_text": "A circle has center $(3, -4)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_410", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_411", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_412", "problem_text": "A circle has center $(3, -2)$ and radius 3. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_413", "problem_text": "Solve for $x$: $3^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_414", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_415", "problem_text": "Solve for $x$: $3^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_416", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 5?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_417", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_418", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 8$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_419", "problem_text": "A circle has center $(3, -2)$ and radius 2. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_420", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_421", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_422", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_423", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_424", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_425", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 6} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_426", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_427", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_428", "problem_text": "In how many ways can 5 distinct balls be placed into 1 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_429", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_430", "problem_text": "Solve for $x$: $2^{x+2} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_431", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_432", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_433", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_434", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 4, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_435", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_436", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_437", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 12$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_438", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_439", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_440", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_441", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_442", "problem_text": "A circle has center $(2, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_443", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 4, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_444", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 8} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_445", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 6, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_446", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_447", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_448", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_449", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_450", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_451", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_452", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_453", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 10$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_454", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_455", "problem_text": "A circle has center $(4, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_456", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 1?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_457", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_458", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_459", "problem_text": "A circle has center $(3, -2)$ and radius 4. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_460", "problem_text": "A circle has center $(3, -2)$ and radius 8. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_461", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_462", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_463", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 8} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_464", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 11$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_465", "problem_text": "Solve for $x$: $4^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_466", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 4, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_467", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_468", "problem_text": "A circle has center $(1, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_469", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_470", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 10} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_471", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 5, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_472", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_473", "problem_text": "A circle has center $(3, -2)$ and radius 2. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_474", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 17$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_475", "problem_text": "A circle has center $(3, -1)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_476", "problem_text": "Solve for $x$: $2^{x+1} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_477", "problem_text": "Solve for $x$: $2^{x+2} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_478", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_479", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_480", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 5, the remainder is 3?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_481", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_482", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_483", "problem_text": "In how many ways can 5 distinct balls be placed into 5 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_484", "problem_text": "In how many ways can 5 distinct balls be placed into 2 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_485", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 4?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_486", "problem_text": "A circle has center $(3, -2)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_487", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_488", "problem_text": "A circle has center $(3, -3)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_489", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_490", "problem_text": "A circle has center $(3, -1)$ and radius 5. Find the equation of the circle.", "complexity_level": "L2_中等推理", "confidence": 0.13499999999999998, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.1275, "L2_中等推理": 0.13499999999999998, "L3_深层推理": 0.05175}}, {"id": "problem_491", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_492", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_493", "problem_text": "In how many ways can 5 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_494", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 6} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_495", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 6$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_496", "problem_text": "In how many ways can 8 distinct balls be placed into 3 distinct boxes?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "problem_497", "problem_text": "Solve for $x$: $2^{x+4} + 2^{x-1} = 20$.", "complexity_level": "L2_中等推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.0, "L2_中等推理": 0.19499999999999998, "L3_深层推理": 0.09224999999999998}}, {"id": "problem_498", "problem_text": "Find the value of $x$ such that $\\sqrt{x + 7} = 9$.", "complexity_level": "L1_浅层推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.12, "L1_浅层推理": 0.15, "L2_中等推理": 0.12, "L3_深层推理": 0.0405}}, {"id": "problem_499", "problem_text": "What is the sum of all positive integers $n$ such that when $n$ is divided by 7, the remainder is 6?", "complexity_level": "L0_显式计算", "confidence": 0.21, "detailed_scores": {"L0_显式计算": 0.21, "L1_浅层推理": 0.20833333333333331, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}], "distribution": {"L0_显式计算": 203, "L1_浅层推理": 106, "L2_中等推理": 191, "L3_深层推理": 0}, "confidence_stats": {"L0_显式计算": [0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21], "L1_浅层推理": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15], "L2_中等推理": [0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998, 0.13499999999999998, 0.19499999999999998, 0.19499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.13499999999999998, 0.19499999999999998], "L3_深层推理": []}, "percentage_distribution": {"L0_显式计算": 40.6, "L1_浅层推理": 21.2, "L2_中等推理": 38.2, "L3_深层推理": 0.0}, "average_confidence": {"L0_显式计算": 0.21000000000000069, "L1_浅层推理": 0.15000000000000024, "L2_中等推理": 0.1639005235602099, "L3_深层推理": 0.0}, "dir_score": 0.976}