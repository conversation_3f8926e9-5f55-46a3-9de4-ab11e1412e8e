{"dataset_name": "ASDiv", "total_problems": 500, "classification_results": [{"id": "asdiv_004_var590", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var5", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var726", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var407", "problem_text": "If they scored 15, 17, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var693", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var602", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var510", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var266", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var498", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var631", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var629", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var886", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var716", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var382", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var730", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var636", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var64", "problem_text": "If a town's population increased from 12,000 to 14,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var893", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var300", "problem_text": "If a town's population increased from 12,000 to 15,598 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var281", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var350", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var760", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var39", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var437", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var993", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var155", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var337", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var441", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var566", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var790", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/3 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var457", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 4.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var783", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var622", "problem_text": "If they scored 15, 18, 9, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var980", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var447", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var386", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var119", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var851", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var416", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var28", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/1 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var824", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var395", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var816", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var425", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var847", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var658", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.10 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var26", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var856", "problem_text": "If Plant A grew 3.7 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var891", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var80", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var725", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/1 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var322", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var452", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var840", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var173", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var522", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var245", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var944", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var651", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var887", "problem_text": "If they scored 15, 20, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var512", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var30", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var922", "problem_text": "If a town's population increased from 12,3 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var68", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var917", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var915", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var295", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var476", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var683", "problem_text": "If they scored 15, 20, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var775", "problem_text": "If a town's population increased from 12,000 to 15,602 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var356", "problem_text": "If they scored 15, 18, 15, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var421", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var867", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var150", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var50", "problem_text": "If they scored 18, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var86", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var257", "problem_text": "If a town's population increased from 12,000 to 15,598 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var285", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var167", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var129", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var52", "problem_text": "If Plant A grew 3.6 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var680", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var276", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var710", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var256", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var600", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var296", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var684", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var459", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var532", "problem_text": "If 44% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var808", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var47", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var974", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var90", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var571", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var85", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var931", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var368", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var43", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var55", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var321", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var870", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var9", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var818", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var591", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var962", "problem_text": "If they scored 15, 18, 12, and 22 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var904", "problem_text": "If Plant A grew 5.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var263", "problem_text": "If a recipe calls for 2/4 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var252", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var734", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var932", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var829", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var177", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var933", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var539", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var661", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var102", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var455", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var116", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var991", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var415", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var707", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.5 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var316", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var11", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var864", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var835", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 3.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var19", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var898", "problem_text": "If Plant A grew 2.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var523", "problem_text": "If Plant A grew 5.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var347", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var817", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var698", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var928", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var223", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var429", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var718", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var118", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var868", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var81", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var796", "problem_text": "If Plant A grew 3.7 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var219", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var530", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var40", "problem_text": "If 45% of 203 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var934", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var239", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var404", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var2", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var822", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var205", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var519", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var563", "problem_text": "If a town's population increased from 12,000 to 15,598 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var183", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var499", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var525", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var83", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var903", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var956", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var554", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var688", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var610", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var330", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var419", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var179", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var16", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var505", "problem_text": "If a town's population increased from 12,000 to 15,602 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var335", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var217", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var583", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var862", "problem_text": "If they scored 15, 18, 12, and 22 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var954", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var275", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var632", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var937", "problem_text": "If they scored 14, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var211", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var650", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var612", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var264", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var905", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/7 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var426", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var556", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var711", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var550", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var562", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var526", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var280", "problem_text": "If they scored 15, 18, 12, and 17 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var65", "problem_text": "If a town's population increased from 15,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var737", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var878", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var351", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var994", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var318", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var758", "problem_text": "If 45% of 203 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var381", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var752", "problem_text": "If a recipe calls for 2/6 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var548", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var334", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var746", "problem_text": "If Plant A grew 3.4 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var67", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var885", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var892", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var233", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var972", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var293", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var480", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var357", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var558", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var663", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var585", "problem_text": "If a recipe calls for 2/4 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var981", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var992", "problem_text": "If they scored 15, 18, 13, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var274", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var449", "problem_text": "If Plant A grew 6.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var653", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var166", "problem_text": "If they scored 15, 17, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var959", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var828", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var110", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var143", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var135", "problem_text": "If they scored 15, 18, 9, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var871", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var346", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var306", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var114", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var841", "problem_text": "If a recipe calls for 2/3 cup of red paint and 3/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var29", "problem_text": "If they scored 18, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var268", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var834", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var225", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var108", "problem_text": "If a town's population increased from 9,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var178", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var762", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var17", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var618", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var942", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var182", "problem_text": "If they scored 15, 18, 12, and 17 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var889", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var248", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var69", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var605", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var420", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var843", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var839", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var336", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var376", "problem_text": "If they scored 15, 20, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var238", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var599", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var431", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var198", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var595", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.9 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var241", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var325", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var35", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var294", "problem_text": "If they scored 15, 15, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var938", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var794", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var507", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var490", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var270", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var367", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var943", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var812", "problem_text": "If they scored 15, 18, 12, and 23 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var234", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var895", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var747", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var666", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var92", "problem_text": "If a town's population increased from 12,000 to 15,597 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var973", "problem_text": "If they scored 14, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var422", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var620", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var144", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var751", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var879", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var677", "problem_text": "If they scored 15, 19, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var799", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var643", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var604", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var549", "problem_text": "If Plant A grew 4.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var349", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var708", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var582", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var345", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var282", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var754", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var454", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/3 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var662", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var625", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var560", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var451", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var918", "problem_text": "If they scored 15, 18, 12, and 22 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var652", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var920", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var594", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var896", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var487", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var743", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var803", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var910", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var598", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var109", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var630", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var642", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var432", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var210", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var463", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var224", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var806", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var968", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var641", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var644", "problem_text": "If a town's population increased from 12,000 to 12,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var570", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var106", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var240", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.9 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var13", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var94", "problem_text": "If Plant A grew 3.4 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var120", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var292", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var949", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var103", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var327", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var96", "problem_text": "If Plant A grew 3.6 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var76", "problem_text": "If Plant A grew 2.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var697", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var951", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var3", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 5.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var687", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var668", "problem_text": "If a town's population increased from 10,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var821", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var370", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var958", "problem_text": "If they scored 14, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var348", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var389", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var169", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var722", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var364", "problem_text": "If Plant A grew 3.6 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var727", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var782", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var739", "problem_text": "If a town's population increased from 10,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var160", "problem_text": "If a recipe calls for 5/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var288", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var272", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var54", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var428", "problem_text": "If they scored 15, 19, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var0", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var450", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var180", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var955", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.5 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var369", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var260", "problem_text": "If they scored 15, 18, 12, and 23 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var146", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var488", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var273", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var462", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var731", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var881", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var485", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var520", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 5.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var434", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var800", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 3.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var342", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var664", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var845", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var660", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var244", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var231", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var255", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var341", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var194", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var791", "problem_text": "If they scored 18, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var788", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var536", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var112", "problem_text": "If a recipe calls for 2/3 cup of red paint and 3/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var659", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.5 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var553", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var320", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var700", "problem_text": "If a recipe calls for 5/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var84", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var57", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var733", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var408", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var848", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var279", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/7 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var963", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var939", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var971", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var741", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var780", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var761", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var645", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var670", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 4.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var792", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var126", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var201", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var766", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/3 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var153", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var385", "problem_text": "If they scored 12, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var228", "problem_text": "If they scored 15, 20, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var502", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var763", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var515", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var79", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var767", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var477", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/7 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var575", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var555", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var988", "problem_text": "If a recipe calls for 2/3 cup of red paint and 3/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var312", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var784", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var975", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var744", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var74", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/6 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var305", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var184", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var807", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var899", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var709", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var58", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var500", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var685", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var107", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var333", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var648", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var440", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var924", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var265", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var196", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var468", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var732", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var493", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var798", "problem_text": "If a town's population increased from 13,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var115", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var6", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var304", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var597", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var467", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var504", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var158", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var453", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var635", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var215", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var105", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var262", "problem_text": "If a town's population increased from 12,3 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var759", "problem_text": "If Plant A grew 3.2 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var702", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var926", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var243", "problem_text": "If 44% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var299", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var983", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var831", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var657", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var637", "problem_text": "If they scored 15, 18, 12, and 17 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var540", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var36", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var121", "problem_text": "If they scored 12, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var25", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var392", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var45", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var836", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var919", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var104", "problem_text": "If 44% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var363", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var940", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var253", "problem_text": "If a town's population increased from 12,3 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var742", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var579", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var91", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var911", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var181", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var433", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var409", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var978", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var287", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var311", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var101", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var14", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 4.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var1", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var290", "problem_text": "If a town's population increased from 15,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var331", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var509", "problem_text": "If they scored 15, 18, 14, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var982", "problem_text": "If Plant A grew 3.3 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var442", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var191", "problem_text": "If they scored 15, 17, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var469", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var212", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var545", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var61", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var858", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var588", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var34", "problem_text": "If 45% of 203 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var378", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var990", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var444", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var872", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var538", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var966", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var913", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var837", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var391", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}], "distribution": {"L0_显式计算": 199, "L1_浅层推理": 200, "L2_中等推理": 101, "L3_深层推理": 0}, "confidence_stats": {"L0_显式计算": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15], "L1_浅层推理": [0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998], "L2_中等推理": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15], "L3_深层推理": []}, "percentage_distribution": {"L0_显式计算": 39.800000000000004, "L1_浅层推理": 40.0, "L2_中等推理": 20.200000000000003, "L3_深层推理": 0.0}, "average_confidence": {"L0_显式计算": 0.14999999999999947, "L1_浅层推理": 0.17916666666666703, "L2_中等推理": 0.15000000000000024, "L3_深层推理": 0.0}, "dir_score": 0.804}