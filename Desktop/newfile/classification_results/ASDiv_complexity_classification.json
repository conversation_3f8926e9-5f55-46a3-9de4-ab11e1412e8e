{"dataset_name": "ASDiv", "total_problems": 500, "classification_results": [{"id": "asdiv_003_var140", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var932", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var804", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var310", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var766", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/3 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var710", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var918", "problem_text": "If they scored 15, 18, 12, and 22 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var691", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/6 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var937", "problem_text": "If they scored 14, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var790", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/3 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var917", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var806", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var250", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var296", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var497", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var355", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var312", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var318", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var474", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var125", "problem_text": "If Plant A grew 6.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var727", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var292", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var168", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var480", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var28", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/1 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var709", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var472", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var506", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var443", "problem_text": "If they scored 15, 18, 15, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var892", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var889", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var248", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var456", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var949", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var289", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var926", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var911", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var477", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/7 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var71", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var31", "problem_text": "If Plant A grew 3.4 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var849", "problem_text": "If Plant A grew 6.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var381", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var800", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 3.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var401", "problem_text": "If a town's population increased from 12,000 to 15,597 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var586", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var725", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/1 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var624", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var993", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var462", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var520", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 5.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var981", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var459", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var155", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var324", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var843", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var433", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var807", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var574", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var687", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var636", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var58", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var306", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var187", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var633", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var920", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var392", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var532", "problem_text": "If 44% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var516", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var811", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var20", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var223", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var661", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var204", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var83", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var208", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var348", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var936", "problem_text": "If Plant A grew 5.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var169", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var103", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var639", "problem_text": "If a recipe calls for 2/4 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var439", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var554", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var931", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var436", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var18", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var101", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var973", "problem_text": "If they scored 14, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var664", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var418", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var363", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var74", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/6 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var534", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var369", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var179", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var427", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var818", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var222", "problem_text": "If a town's population increased from 12,3 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var342", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var281", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var194", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var803", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var628", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var343", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var322", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var49", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var722", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var43", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var596", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var767", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var145", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var210", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var128", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var694", "problem_text": "If Plant A grew 3.4 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var137", "problem_text": "If they scored 15, 16, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var6", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var124", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var713", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var256", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var608", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var817", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var912", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var481", "problem_text": "If they scored 15, 18, 12, and 23 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var813", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var144", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var619", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var556", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var135", "problem_text": "If they scored 15, 18, 9, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var764", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var393", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var549", "problem_text": "If Plant A grew 4.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var0", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var180", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var504", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var200", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var345", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var451", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var301", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var919", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var711", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var485", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var522", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var873", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var501", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var25", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var699", "problem_text": "If a town's population increased from 13,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var900", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var616", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var669", "problem_text": "If they scored 17, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var492", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var846", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var286", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var720", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var491", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var48", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var457", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 4.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var454", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/3 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var916", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var373", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var27", "problem_text": "If they scored 17, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var810", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var752", "problem_text": "If a recipe calls for 2/6 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var939", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var225", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var925", "problem_text": "If a town's population increased from 12,000 to 12,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var703", "problem_text": "If a recipe calls for 2/4 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var901", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var2", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var186", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var530", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var196", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var32", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var826", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var592", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var447", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var315", "problem_text": "If 45% of 203 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var553", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var775", "problem_text": "If a town's population increased from 12,000 to 15,602 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var565", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var577", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var424", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var54", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var593", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var268", "problem_text": "If a recipe calls for 2/3 cup of red paint and 2/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var64", "problem_text": "If a town's population increased from 12,000 to 14,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var648", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var823", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var178", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var671", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var834", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var765", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var66", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var398", "problem_text": "If Plant A grew 3.7 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var515", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var731", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var621", "problem_text": "If they scored 17, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var879", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var21", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var964", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var397", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var346", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var791", "problem_text": "If they scored 18, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var216", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/7 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var414", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var857", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var567", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var469", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var944", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var275", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var572", "problem_text": "If 42% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var579", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var922", "problem_text": "If a town's population increased from 12,3 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var540", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var875", "problem_text": "If a town's population increased from 10,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var957", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var928", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var218", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var261", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var573", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var821", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var141", "problem_text": "If they scored 18, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var130", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.10 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var907", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var880", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var898", "problem_text": "If Plant A grew 2.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var663", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var317", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var878", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var316", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var160", "problem_text": "If a recipe calls for 5/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var339", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var7", "problem_text": "If a town's population increased from 12,000 to 15,599 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var872", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var899", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var984", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var122", "problem_text": "If Plant A grew 3.2 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var174", "problem_text": "If a town's population increased from 12,3 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var968", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var57", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var829", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var470", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var211", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var249", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var647", "problem_text": "If they scored 15, 18, 12, and 23 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var272", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var67", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var511", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var503", "problem_text": "If a recipe calls for 4/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var461", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var771", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var267", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var63", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var689", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var580", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var870", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var143", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var473", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var19", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var91", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var560", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var206", "problem_text": "If Plant A grew 3.7 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var688", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var615", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var97", "problem_text": "If a town's population increased from 12,000 to 15,597 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var656", "problem_text": "If Plant A grew 3.8 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var851", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var426", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var129", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var602", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var969", "problem_text": "If 44% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var325", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var411", "problem_text": "If a recipe calls for 2/4 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var544", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var164", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var635", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var202", "problem_text": "If Plant A grew 2.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var779", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var177", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var627", "problem_text": "If they scored 15, 18, 15, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var415", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var591", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var100", "problem_text": "If a recipe calls for 5/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var835", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 3.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var808", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var816", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var613", "problem_text": "If they scored 15, 18, 12, and 21 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var335", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var161", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var793", "problem_text": "If a town's population increased from 12,000 to 15,602 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var978", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var294", "problem_text": "If they scored 15, 15, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var902", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var668", "problem_text": "If a town's population increased from 10,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var224", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var336", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var678", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var487", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var866", "problem_text": "If Plant A grew 4.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var606", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var264", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var399", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var651", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var890", "problem_text": "If they scored 15, 21, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var753", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var460", "problem_text": "If 47% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var853", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var183", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var956", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var266", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var618", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var81", "problem_text": "If a recipe calls for 2/1 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var429", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var197", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var212", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var361", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var589", "problem_text": "If they scored 18, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var86", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var897", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var991", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var796", "problem_text": "If Plant A grew 3.7 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var650", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var653", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var215", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var638", "problem_text": "If a town's population increased from 12,000 to 18,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var659", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.5 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var705", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var495", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var308", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var39", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var157", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var961", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var988", "problem_text": "If a recipe calls for 2/3 cup of red paint and 3/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var110", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var951", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var389", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var372", "problem_text": "If they scored 15, 18, 15, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var328", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var115", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var557", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var270", "problem_text": "If they scored 13, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var769", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var825", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/1 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var505", "problem_text": "If a town's population increased from 12,000 to 15,602 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var73", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var482", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var282", "problem_text": "If 45% of 201 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var712", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var209", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var774", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var864", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var788", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var552", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var79", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var41", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var524", "problem_text": "If a town's population increased from 12,000 to 13,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var797", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var220", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var838", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var82", "problem_text": "If they scored 12, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var750", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var61", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var933", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var159", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var729", "problem_text": "If 46% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var732", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var695", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var233", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.7 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var291", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var980", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var139", "problem_text": "If Plant A grew 5.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var679", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var94", "problem_text": "If Plant A grew 3.4 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var675", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var314", "problem_text": "If 45% of 197 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var836", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var102", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var673", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var238", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var438", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var273", "problem_text": "If Plant A grew 1.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var193", "problem_text": "If a town's population increased from 11,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var136", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var625", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var59", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var90", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var230", "problem_text": "If a recipe calls for 4/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var229", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var883", "problem_text": "If a recipe calls for 2/3 cup of red paint and 3/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var500", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var726", "problem_text": "If they scored 15, 18, 12, and 18 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var930", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var376", "problem_text": "If they scored 15, 20, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var149", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var77", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var863", "problem_text": "If a town's population increased from 12,000 to 16,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var814", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var950", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.9 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var190", "problem_text": "If they scored 15, 18, 13, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var523", "problem_text": "If Plant A grew 5.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var935", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var199", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var859", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var276", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var585", "problem_text": "If a recipe calls for 2/4 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var578", "problem_text": "If a town's population increased from 12,000 to 15,599 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var588", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var40", "problem_text": "If 45% of 203 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var508", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var150", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var558", "problem_text": "If 48% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var175", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var982", "problem_text": "If Plant A grew 3.3 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var302", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var403", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var700", "problem_text": "If a recipe calls for 5/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var84", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var913", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var227", "problem_text": "If 45% of 202 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var605", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var300", "problem_text": "If a town's population increased from 12,000 to 15,598 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var724", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var237", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var11", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var945", "problem_text": "If Plant A grew 3.4 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var858", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var550", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var417", "problem_text": "If Plant A grew 4.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var408", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var185", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var583", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var582", "problem_text": "If a town's population increased from 14,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var34", "problem_text": "If 45% of 203 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var92", "problem_text": "If a town's population increased from 12,000 to 15,597 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var521", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var114", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var422", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var123", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var85", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var240", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.9 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var364", "problem_text": "If Plant A grew 3.6 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var783", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var170", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var475", "problem_text": "If Plant A grew 3.7 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var298", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var350", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var290", "problem_text": "If a town's population increased from 15,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var677", "problem_text": "If they scored 15, 19, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var906", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var127", "problem_text": "If a town's population increased from 12,000 to 15,598 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var107", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var112", "problem_text": "If a recipe calls for 2/3 cup of red paint and 3/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var46", "problem_text": "If a town's population increased from 12,000 to 15,602 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var338", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var507", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var531", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var840", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var471", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var721", "problem_text": "If they scored 15, 18, 12, and 19 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var56", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 3.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var13", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var53", "problem_text": "If a town's population increased from 12,1 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var837", "problem_text": "If a recipe calls for 2/2 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var304", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var37", "problem_text": "If they scored 15, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var794", "problem_text": "If a town's population increased from 12,2 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.13142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var947", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var26", "problem_text": "If a recipe calls for 1/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var413", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var235", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_004_var425", "problem_text": "If 45% of 198 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var620", "problem_text": "If a town's population increased from 12,000 to 15,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var189", "problem_text": "If a recipe calls for 2/3 cup of red paint and 4/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var3", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 5.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var847", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var514", "problem_text": "If they scored 16, 18, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var986", "problem_text": "If they scored 15, 18, 10, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var431", "problem_text": "If a town's population increased from 12,000 to 17,600 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var819", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_005_var277", "problem_text": "If a town's population increased from 12,000 to 15,597 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var76", "problem_text": "If Plant A grew 2.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var446", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var942", "problem_text": "If a recipe calls for 3/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_002_var490", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var623", "problem_text": "If a recipe calls for 5/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_005_var5", "problem_text": "If a town's population increased from 12,000 to 15,601 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_004_var643", "problem_text": "If 43% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_002_var166", "problem_text": "If they scored 15, 17, 12, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var568", "problem_text": "If they scored 15, 18, 11, and 20 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_001_var938", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_002_var862", "problem_text": "If they scored 15, 18, 12, and 22 points respectively, what was their total score?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.10333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_005_var634", "problem_text": "If a town's population increased from 12,000 to 15,603 people, what was the percentage increase?", "complexity_level": "L0_显式计算", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.09, "L2_中等推理": 0.07142857142857142, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var541", "problem_text": "If a recipe calls for 2/5 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_001_var205", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 1.8 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var378", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/5 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var665", "problem_text": "If 45% of 199 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_003_var347", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/2 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_003_var548", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}, {"id": "asdiv_004_var293", "problem_text": "If 45% of 200 students exercise daily, how many students exercise daily?", "complexity_level": "L1_浅层推理", "confidence": 0.19499999999999998, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.19499999999999998, "L2_中等推理": 0.09, "L3_深层推理": 0.0225}}, {"id": "asdiv_001_var575", "problem_text": "If Plant A grew 3.5 cm and Plant B grew 2.6 cm, what is the total growth?", "complexity_level": "L1_浅层推理", "confidence": 0.16333333333333333, "detailed_scores": {"L0_显式计算": 0.15, "L1_浅层推理": 0.16333333333333333, "L2_中等推理": 0.12, "L3_深层推理": 0.045}}, {"id": "asdiv_003_var502", "problem_text": "If a recipe calls for 2/3 cup of red paint and 1/4 cup of blue paint, how much paint is needed in to...", "complexity_level": "L2_中等推理", "confidence": 0.15, "detailed_scores": {"L0_显式计算": 0.06, "L1_浅层推理": 0.11833333333333333, "L2_中等推理": 0.15, "L3_深层推理": 0.063}}], "distribution": {"L0_显式计算": 212, "L1_浅层推理": 195, "L2_中等推理": 93, "L3_深层推理": 0}, "confidence_stats": {"L0_显式计算": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15], "L1_浅层推理": [0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.16333333333333333, 0.16333333333333333, 0.19499999999999998, 0.19499999999999998, 0.16333333333333333], "L2_中等推理": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15], "L3_深层推理": []}, "percentage_distribution": {"L0_显式计算": 42.4, "L1_浅层推理": 39.0, "L2_中等推理": 18.6, "L3_深层推理": 0.0}, "average_confidence": {"L0_显式计算": 0.1499999999999994, "L1_浅层推理": 0.17924786324786357, "L2_中等推理": 0.15000000000000024, "L3_深层推理": 0.0}, "dir_score": 0.762}