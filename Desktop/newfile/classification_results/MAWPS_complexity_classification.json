{"dataset_name": "MAWPS", "total_problems": 500, "classification_results": [{"id": "mawps_004_var79", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var592", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var700", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var928", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var793", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var663", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var892", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1161", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var253", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var625", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var735", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var118", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var195", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var768", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var816", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var654", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var488", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var319", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var706", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var694", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1012", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var947", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var861", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var647", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var918", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var69", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var465", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var640", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1180", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var857", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var44", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var860", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var434", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var82", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var192", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var314", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var92", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var483", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var996", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var811", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var345", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var264", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var516", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var10", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var743", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var132", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var661", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var565", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var587", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var372", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var316", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var396", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var385", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var991", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1102", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var422", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1130", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var157", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var86", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var639", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var200", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var350", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var930", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var763", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var437", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var839", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var43", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1156", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var612", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1122", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var724", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var39", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var538", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var559", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var222", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var783", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1036", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var48", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var702", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var909", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var14", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var17", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var30", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var980", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var854", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var289", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1094", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var880", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var254", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var324", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var426", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var407", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var604", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var622", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var788", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1028", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var167", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var964", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var309", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var263", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var473", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1136", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var279", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var47", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var653", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var256", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var520", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var976", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var652", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var979", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1076", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var501", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1010", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var209", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var922", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var937", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var823", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var163", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1082", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var791", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var796", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var521", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var819", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var549", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1078", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var680", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var921", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var977", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var12", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var995", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1015", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var435", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var984", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var883", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var190", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1125", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1092", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var187", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1178", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1038", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var948", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1065", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var878", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var704", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var725", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var175", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var858", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var707", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var495", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var445", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var875", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var834", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var806", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var586", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var790", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var540", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var20", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var740", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var428", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var693", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var450", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var478", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var62", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var952", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var162", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1058", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1022", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1192", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var987", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var683", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var673", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var391", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var275", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var74", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var772", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var70", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var566", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var699", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var432", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var402", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var346", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1074", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var503", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var317", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var34", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var97", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var943", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var184", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var970", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var470", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var907", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1084", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var424", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var509", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var476", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var841", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var595", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var32", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var446", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var469", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var99", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1117", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1172", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var958", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var290", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1056", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var121", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1011", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var72", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var281", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var84", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var475", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var348", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var563", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var523", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var955", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var202", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var726", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var778", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var204", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var297", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var870", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1157", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var756", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var913", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var633", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1114", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1071", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1070", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1190", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var651", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1133", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var852", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var581", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1137", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var561", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var944", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var990", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1176", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1099", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var596", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var686", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var287", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var9", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1051", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var251", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var751", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var577", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var410", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var305", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var193", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var874", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var512", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var303", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var113", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var106", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var978", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var903", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var13", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var65", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var643", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var901", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var539", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1158", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var517", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var872", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1008", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1144", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var93", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var736", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var542", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var846", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var4", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var347", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var535", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var835", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var621", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var172", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var379", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var479", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var674", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var81", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1131", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var527", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1085", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var211", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var992", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var662", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var779", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var464", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1127", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var583", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var49", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var733", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var286", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var929", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var174", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1154", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1129", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1193", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var519", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var902", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var109", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var123", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1027", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var672", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var357", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var183", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var886", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var732", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var405", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1110", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var401", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1039", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var231", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var427", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var942", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1088", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var57", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var376", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var338", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var951", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var299", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var334", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1166", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var848", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1066", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var38", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var998", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var444", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var22", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var131", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var610", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var703", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var562", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var312", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var258", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var746", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1091", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var154", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var760", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var471", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var374", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var26", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var614", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var868", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1185", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var329", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var526", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1024", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var626", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var356", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1138", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var632", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var669", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var555", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var618", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var436", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var135", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var442", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var224", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1083", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1123", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var50", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1050", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1128", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var568", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var404", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var543", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var684", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var394", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var285", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1167", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var77", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var648", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var16", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var794", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var667", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var636", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var711", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var462", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var989", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var641", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var85", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1087", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var574", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var217", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var688", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1014", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1109", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var676", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var270", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var307", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var530", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1061", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var160", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var234", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var967", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var544", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var237", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var792", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var170", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var24", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var472", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var100", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1020", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var298", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1003", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var419", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var537", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var899", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var780", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1175", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var900", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var682", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var416", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var847", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var259", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1107", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var112", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1044", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1108", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var934", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var560", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var66", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var616", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1018", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var803", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var717", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var982", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1181", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var993", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1048", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var720", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var261", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1053", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var96", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var355", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var214", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var988", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1013", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var431", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var228", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var564", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var282", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var953", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var486", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var27", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1021", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var60", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var213", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var547", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var173", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var203", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var660", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1135", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var802", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var260", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var755", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var133", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var665", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1093", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var635", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var496", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var452", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var833", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var931", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1037", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var129", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var578", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1005", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var456", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var533", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var451", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1182", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1032", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var685", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var233", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var775", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var513", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1064", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var143", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var359", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var631", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var340", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var296", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var813", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1149", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1059", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var969", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1023", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var609", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var776", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var752", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var481", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var933", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var274", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}], "distribution": {"L0_显式计算": 500, "L1_浅层推理": 0, "L2_中等推理": 0, "L3_深层推理": 0}, "confidence_stats": {"L0_显式计算": [0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3], "L1_浅层推理": [], "L2_中等推理": [], "L3_深层推理": []}, "percentage_distribution": {"L0_显式计算": 100.0, "L1_浅层推理": 0.0, "L2_中等推理": 0.0, "L3_深层推理": 0.0}, "average_confidence": {"L0_显式计算": 0.29999999999999993, "L1_浅层推理": 0.0, "L2_中等推理": 0.0, "L3_深层推理": 0.0}, "dir_score": 0.0}