{"dataset_name": "MAWPS", "total_problems": 500, "classification_results": [{"id": "mawps_001_var1071", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var229", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var37", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var576", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var542", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var247", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1138", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1023", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var5", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var737", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var749", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var244", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1157", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var505", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var877", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var212", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var683", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var772", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1163", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var127", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var911", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var398", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var376", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var156", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var453", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var485", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var363", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1015", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1084", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var320", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var500", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var678", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1192", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var531", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var467", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var39", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var265", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1117", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var641", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var261", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var249", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1144", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1028", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var152", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var529", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var378", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var116", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1057", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var831", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var441", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var759", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var488", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var835", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1168", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var704", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var311", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var45", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1008", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1097", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var708", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var222", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1096", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var941", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var304", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var259", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var411", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1122", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1134", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var87", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var724", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var707", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var623", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var482", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var404", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var703", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var68", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var176", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var935", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var272", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1169", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var636", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var550", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var147", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var205", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var12", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var855", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1164", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1029", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var99", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var289", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var396", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var510", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var154", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var913", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1033", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var605", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var951", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var386", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var513", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var480", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var62", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1112", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var23", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var387", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var850", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var958", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var403", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var275", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var118", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var548", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var46", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var890", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var840", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var909", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var183", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var220", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var659", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1135", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var425", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var395", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var859", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var962", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var388", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var602", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var860", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var82", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var38", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var802", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var997", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1179", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var789", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var786", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var664", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var119", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var885", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1103", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var187", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var754", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var140", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var181", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var375", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var833", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1089", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var693", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var175", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var146", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var866", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1146", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var812", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var412", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var730", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1142", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var714", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var350", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var937", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var263", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var701", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1051", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var648", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var798", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var130", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var816", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var653", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var490", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var49", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1178", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var444", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var847", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var611", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var316", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1080", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var740", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var750", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var706", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var991", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var269", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var957", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var504", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var58", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var736", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var190", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var439", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1110", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var22", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1114", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var476", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1038", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var132", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var208", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var129", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1158", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var825", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var788", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var214", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1055", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1027", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1088", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1177", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var768", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var702", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var52", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1095", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var810", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var928", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var184", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var431", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var888", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var773", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1006", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var284", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var211", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var427", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var534", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1101", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var107", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var443", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var407", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var159", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var478", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var256", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var7", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1166", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var168", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1031", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var752", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var322", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var837", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var682", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var635", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var434", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var842", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var339", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var658", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1012", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var216", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1171", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var18", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var281", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var473", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var769", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var51", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var760", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var217", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var970", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var642", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var20", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var927", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1100", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var195", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var564", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var889", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var309", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var597", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var656", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var415", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var321", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var114", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var589", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var83", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1078", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var267", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var598", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var762", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var414", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var588", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var634", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var856", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var673", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var748", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var883", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var782", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1141", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var148", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var158", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var556", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var413", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1125", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1062", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var295", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var721", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var834", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var897", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1150", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var230", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var135", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var331", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var827", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var317", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var994", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var671", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var516", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var784", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var647", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var273", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var47", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var162", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var765", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var901", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var491", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var511", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var585", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var75", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1092", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var354", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var783", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var753", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var791", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1175", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var920", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var26", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var268", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var370", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var219", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var916", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var599", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var424", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var90", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var209", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var174", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var893", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var609", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var315", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var94", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var519", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var915", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var910", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var55", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var253", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var795", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var91", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var61", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var817", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var235", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1187", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var29", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1035", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1156", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var16", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var80", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var809", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var409", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1174", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var206", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1001", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var288", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var226", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var110", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var474", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var631", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var906", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var432", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var366", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var838", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var568", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var836", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var848", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var857", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var328", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1151", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1054", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var764", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var954", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var577", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var258", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var643", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1002", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var499", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var418", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var8", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var458", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var21", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var197", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var919", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var663", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var112", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1152", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1120", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var801", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1075", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var614", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var687", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var966", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var908", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var257", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var775", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1081", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var892", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var481", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var349", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var575", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var225", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var872", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1068", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var477", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var956", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1077", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var751", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1022", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var365", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var126", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1026", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1044", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1190", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var945", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var198", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var25", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var215", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var981", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var943", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var616", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var787", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1000", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var379", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var686", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var932", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var777", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var959", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var805", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var933", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1106", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var820", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var896", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var70", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var942", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var337", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var101", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var628", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1126", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var607", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var822", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var420", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var770", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var109", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var59", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var1165", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var539", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var254", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var559", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var904", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var845", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1123", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var394", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var440", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1132", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var359", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_005_var242", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var248", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var982", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var282", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1050", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var629", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var797", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var617", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1018", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var592", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1047", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var239", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var385", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var544", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var742", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var1063", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var9", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var1093", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var287", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var955", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var793", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var558", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var318", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var1087", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1167", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var975", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var28", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var732", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var1119", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var456", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var546", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var761", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var670", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var993", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var871", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var149", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var862", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_001_var455", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var608", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var533", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var327", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var902", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_003_var382", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var570", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var227", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_001_var463", "problem_text": "How many blue balloons does <PERSON> have now?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var454", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_005_var496", "problem_text": "How many hamburgers did they sell this week?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var346", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_002_var900", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_003_var136", "problem_text": "How many balloons does <PERSON> have in total?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.2533333333333333, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_002_var352", "problem_text": "How many walnut trees will the park have when the workers are finished?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.06, "L3_深层推理": 0.0}}, {"id": "mawps_004_var108", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}, {"id": "mawps_004_var332", "problem_text": "How many cupcakes will each person get?", "complexity_level": "L0_显式计算", "confidence": 0.3, "detailed_scores": {"L0_显式计算": 0.3, "L1_浅层推理": 0.24, "L2_中等推理": 0.08285714285714285, "L3_深层推理": 0.0}}], "distribution": {"L0_显式计算": 500, "L1_浅层推理": 0, "L2_中等推理": 0, "L3_深层推理": 0}, "confidence_stats": {"L0_显式计算": [0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3], "L1_浅层推理": [], "L2_中等推理": [], "L3_深层推理": []}, "percentage_distribution": {"L0_显式计算": 100.0, "L1_浅层推理": 0.0, "L2_中等推理": 0.0, "L3_深层推理": 0.0}, "average_confidence": {"L0_显式计算": 0.29999999999999993, "L1_浅层推理": 0.0, "L2_中等推理": 0.0, "L3_深层推理": 0.0}, "dir_score": 0.0}