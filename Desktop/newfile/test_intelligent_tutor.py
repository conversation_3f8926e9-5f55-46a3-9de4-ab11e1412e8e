#!/usr/bin/env python3
"""
Test script for Intelligent Math Tutor System
Demonstrating Chain of Responsibility, State Machine, Strategy Composite, and Observer patterns
"""

import logging
import os
import sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.intelligent_tutor import (ExplorationState,
                                          GuidedLearningState,
                                          IntelligentTutor, MasteryState,
                                          ProblemContext, StudentState)


def create_test_problems():
    """创建测试问题"""
    return [
        ProblemContext(
            problem_text="小明有5个苹果，小红有3个苹果，他们一共有多少个苹果？",
            problem_id="addition_001",
            difficulty_level=1,
            concept_tags=["addition", "counting"],
            expected_answer="8",
            solution_steps=[
                "1. 识别问题：这是一个加法问题",
                "2. 提取数字：小明有5个苹果，小红有3个苹果", 
                "3. 计算：5 + 3 = 8",
                "4. 答案：他们一共有8个苹果"
            ],
            hints_available=["想想你有几个苹果，再拿来几个苹果，现在总共有多少个？"],
            similar_problems=["小华有4个橘子，小李有2个橘子，他们一共有多少个橘子？"]
        ),
        ProblemContext(
            problem_text="小华有10个糖果，他给了小明3个，还剩多少个？",
            problem_id="subtraction_001", 
            difficulty_level=1,
            concept_tags=["subtraction", "counting"],
            expected_answer="7",
            solution_steps=[
                "1. 识别问题：这是一个减法问题",
                "2. 提取数字：小华有10个糖果，给了3个",
                "3. 计算：10 - 3 = 7", 
                "4. 答案：还剩7个糖果"
            ],
            hints_available=["想象你有10个糖果，拿走3个，还剩几个？"],
            similar_problems=["小红有8个饼干，吃了2个，还剩多少个？"]
        ),
        ProblemContext(
            problem_text="一个复杂的数学问题，需要多步推理和高级概念理解。",
            problem_id="complex_001",
            difficulty_level=5,
            concept_tags=["complex_reasoning", "multi_step"],
            expected_answer="unknown",
            solution_steps=[
                "1. 这是一个复杂问题，需要深入分析",
                "2. 涉及多个数学概念的综合运用",
                "3. 需要创造性思维和问题解决能力"
            ],
            hints_available=["这个问题比较复杂，让我们一步一步来分析"],
            similar_problems=[]
        )
    ]


def test_chain_of_responsibility():
    """测试责任链模式"""
    print("🔗 测试责任链模式 (Chain of Responsibility)")
    print("=" * 60)
    
    tutor = IntelligentTutor()
    problems = create_test_problems()
    student_id = "test_student_001"
    
    # 测试不同学生状态下的响应
    test_cases = [
        {
            "name": "新学生首次尝试",
            "student_state": StudentState(student_id=student_id, attempts=0, frustration_level=0.1),
            "problem": problems[0],
            "expected_behavior": "应该提供概念提示"
        },
        {
            "name": "学生多次尝试失败",
            "student_state": StudentState(student_id=student_id, attempts=3, frustration_level=0.8),
            "problem": problems[0], 
            "expected_behavior": "应该提供鼓励和完整解答"
        },
        {
            "name": "学生挫折情绪高",
            "student_state": StudentState(student_id=student_id, attempts=1, frustration_level=0.9),
            "problem": problems[0],
            "expected_behavior": "应该优先提供情感支持"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   预期行为：{test_case['expected_behavior']}")
        
        # 模拟学生状态
        context = tutor.get_or_create_student_context(student_id)
        context.student_state.attempts = test_case['student_state'].attempts
        context.student_state.frustration_level = test_case['student_state'].frustration_level
        
        # 获取响应
        response = tutor.solve_problem(student_id, test_case['problem'])
        
        print(f"   实际响应类型：{response.response_type}")
        print(f"   置信度：{response.confidence_level:.2f}")
        print(f"   响应内容：{response.message[:100]}...")


def test_state_machine():
    """测试状态机模式"""
    print("\n\n🔄 测试状态机模式 (State Machine)")
    print("=" * 60)
    
    tutor = IntelligentTutor()
    problem = create_test_problems()[0]
    student_id = "test_student_002"
    
    print("模拟学生学习过程的状态转换：")
    
    # 1. 探索状态
    print("\n1. 探索状态 (ExplorationState)")
    context = tutor.get_or_create_student_context(student_id)
    context.state = ExplorationState()
    
    response = context.study(problem)
    print(f"   状态：{type(context.state).__name__}")
    print(f"   响应：{response.message[:80]}...")
    
    # 2. 提交错误答案，触发状态转换
    print("\n2. 提交错误答案")
    response = context.check_answer("6", "8")
    print(f"   状态：{type(context.state).__name__}")
    print(f"   响应：{response.message[:80]}...")
    
    # 3. 再次提交错误答案
    print("\n3. 再次提交错误答案")
    response = context.check_answer("7", "8")
    print(f"   状态：{type(context.state).__name__}")
    print(f"   响应：{response.message[:80]}...")
    
    # 4. 提交正确答案，转换到掌握状态
    print("\n4. 提交正确答案")
    response = context.check_answer("8", "8")
    print(f"   状态：{type(context.state).__name__}")
    print(f"   响应：{response.message[:80]}...")


def test_strategy_composite():
    """测试策略组合模式"""
    print("\n\n🎯 测试策略组合模式 (Strategy Composite)")
    print("=" * 60)
    
    tutor = IntelligentTutor()
    problems = create_test_problems()
    student_id = "test_student_003"
    
    # 测试不同学生水平下的教学策略
    test_cases = [
        {
            "name": "初级学生 (Level 1)",
            "student_state": StudentState(student_id=student_id, current_level=1),
            "problem": problems[0],
            "expected_strategies": ["概念解释", "视觉辅助", "例题"]
        },
        {
            "name": "中级学生 (Level 3)", 
            "student_state": StudentState(student_id=student_id, current_level=3),
            "problem": problems[1],
            "expected_strategies": ["概念解释", "例题"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   预期策略：{', '.join(test_case['expected_strategies'])}")
        
        # 设置学生状态
        context = tutor.get_or_create_student_context(student_id)
        context.student_state.current_level = test_case['student_state'].current_level
        
        # 应用教学策略
        teaching_support = tutor.teaching_strategies.apply(context.student_state, test_case['problem'])
        
        print(f"   实际教学支持：")
        print(f"   {teaching_support}")


def test_observer_pattern():
    """测试观察者模式"""
    print("\n\n👁️ 测试观察者模式 (Observer Pattern)")
    print("=" * 60)
    
    tutor = IntelligentTutor()
    problem = create_test_problems()[0]
    student_id = "test_student_004"
    
    print("模拟学生答题过程，观察实时反馈：")
    
    # 模拟连续答题过程
    answers = ["6", "7", "8", "9", "8"]  # 错误、错误、正确、错误、正确
    expected = "8"
    
    for i, answer in enumerate(answers, 1):
        print(f"\n{i}. 提交答案：{answer}")
        
        # 获取响应（包含观察者反馈）
        response = tutor.solve_problem(student_id, problem, answer)
        
        # 提取观察者反馈
        observer_feedback = []
        if "🎯" in response.message or "📈" in response.message or "💪" in response.message:
            lines = response.message.split('\n')
            for line in lines:
                if any(emoji in line for emoji in ["🎯", "📈", "💪", "🌟", "📚", "😊", "🎉"]):
                    observer_feedback.append(line.strip())
        
        if observer_feedback:
            print(f"   观察者反馈：")
            for feedback in observer_feedback:
                print(f"   {feedback}")
        
        # 显示学生进度
        progress = tutor.get_student_progress(student_id)
        print(f"   当前进度：准确率 {progress['accuracy_rate']:.1%}, 挫折度 {progress['frustration_level']:.1f}")


def test_integrated_workflow():
    """测试集成工作流程"""
    print("\n\n🚀 测试集成工作流程")
    print("=" * 60)
    
    tutor = IntelligentTutor()
    problems = create_test_problems()
    student_id = "test_student_005"
    
    print("模拟完整的学习会话：")
    
    # 1. 开始学习简单问题
    print("\n📚 学习阶段 1：简单加法问题")
    problem1 = problems[0]
    
    print("   问题：", problem1.problem_text)
    response = tutor.solve_problem(student_id, problem1)
    print(f"   系统响应：{response.response_type}")
    print(f"   消息：{response.message[:100]}...")
    
    # 2. 提交错误答案
    print("\n❌ 提交错误答案")
    response = tutor.solve_problem(student_id, problem1, "6")
    print(f"   系统响应：{response.response_type}")
    print(f"   状态：{response.metadata.get('state_type', 'unknown')}")
    
    # 3. 提交正确答案
    print("\n✅ 提交正确答案")
    response = tutor.solve_problem(student_id, problem1, "8")
    print(f"   系统响应：{response.response_type}")
    print(f"   状态：{response.metadata.get('state_type', 'unknown')}")
    
    # 4. 学习更复杂的问题
    print("\n📚 学习阶段 2：减法问题")
    problem2 = problems[1]
    
    print("   问题：", problem2.problem_text)
    response = tutor.solve_problem(student_id, problem2)
    print(f"   系统响应：{response.response_type}")
    print(f"   状态：{response.metadata.get('state_type', 'unknown')}")
    
    # 5. 查看最终进度
    print("\n📊 学习进度总结")
    progress = tutor.get_student_progress(student_id)
    for key, value in progress.items():
        if key != "student_id":
            print(f"   {key}: {value}")


def test_pattern_advantages():
    """测试各种模式的优势"""
    print("\n\n💡 设计模式优势分析")
    print("=" * 60)
    
    advantages = {
        "责任链模式": [
            "✅ 渐进式辅导：从提示到完整解答",
            "✅ 灵活处理：根据学生状态选择不同策略",
            "✅ 易于扩展：可以轻松添加新的处理器",
            "✅ 解耦设计：每个处理器职责单一"
        ],
        "状态机模式": [
            "✅ 状态管理：清晰跟踪学生学习状态",
            "✅ 自适应：根据表现自动调整教学策略",
            "✅ 个性化：不同状态提供不同支持",
            "✅ 可预测：状态转换逻辑清晰"
        ],
        "策略组合模式": [
            "✅ 灵活教学：组合多种教学方法",
            "✅ 个性化：根据学生水平选择策略",
            "✅ 可扩展：易于添加新的教学策略",
            "✅ 模块化：每个策略独立实现"
        ],
        "观察者模式": [
            "✅ 实时反馈：及时响应学习事件",
            "✅ 多维度监控：进度、情感、难度等",
            "✅ 松耦合：观察者与核心逻辑分离",
            "✅ 可扩展：易于添加新的观察者"
        ]
    }
    
    for pattern, benefits in advantages.items():
        print(f"\n{pattern}:")
        for benefit in benefits:
            print(f"  {benefit}")


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    print("🎓 智能数学辅导系统 - 设计模式演示")
    print("=" * 80)
    
    try:
        # 运行各种测试
        test_chain_of_responsibility()
        test_state_machine()
        test_strategy_composite()
        test_observer_pattern()
        test_integrated_workflow()
        test_pattern_advantages()
        
        print("\n\n✅ 所有测试完成！")
        print("\n📝 总结：")
        print("   这个智能辅导系统成功集成了四种设计模式：")
        print("   • 责任链模式：实现渐进式解题辅导")
        print("   • 状态机模式：管理学生学习状态")
        print("   • 策略组合模式：提供灵活教学方法")
        print("   • 观察者模式：实现实时反馈系统")
        print("\n   这些模式协同工作，创造了一个智能、个性化、可扩展的数学辅导系统。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误：{e}")
        import traceback
        traceback.print_exc() 