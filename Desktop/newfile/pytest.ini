[tool:pytest]
# pytest configuration
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests  
    performance: Performance tests
    slow: Slow running tests (> 10 seconds)
    smoke: Quick smoke tests
    regression: Regression tests

# Coverage settings
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov-branch
    --cov-fail-under=80

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test discovery
minversion = 6.0
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    node_modules
    __pycache__
    .pytest_cache

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning 