#!/usr/bin/env python3
"""
Test script for HybridModel functionality.
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.base_model import ModelInput
from src.models.model_manager import <PERSON><PERSON>ana<PERSON>


def test_hybrid_model():
    """Test the hybrid model with various problem types."""
    
    print("🚀 Testing HybridModel Integration")
    print("=" * 50)
    
    # Initialize model manager
    manager = ModelManager()
    
    # Initialize hybrid model
    print("📋 Initializing hybrid_solver...")
    success = manager.initialize_model("hybrid_solver")
    if not success:
        print("❌ Failed to initialize hybrid_solver")
        return
    
    print("✅ Hybrid model initialized successfully")
    
    # Test problems
    test_problems = [
        # Pattern solver should succeed (high confidence)
        {
            "problem": "19 red peaches, 11 yellow peaches and 12 green peaches are in the basket. How many more red peaches than yellow peaches are in the basket?",
            "expected": "8.0",
            "description": "Pattern solver success case"
        },
        # Pattern solver should succeed (division)
        {
            "problem": "<PERSON> was sending out birthday invitations to 10 friends. If each package of invitations she bought had 2 invitations in it How many packs does she need so that no friend gets left out?",
            "expected": "5.0", 
            "description": "Division pattern case"
        },
        # <PERSON>tern solver should fail (complex reasoning needed)
        {
            "problem": "Ed had 5 more marbles than Doug. <PERSON> lost 3 of his marbles at the playground. If Ed had 27 marbles How many marbles did Doug have initially?",
            "expected": "22.0",
            "description": "Complex multi-step reasoning (should trigger LLM fallback)"
        },
        # Pattern solver should fail (no clear pattern)
        {
            "problem": "A complex mathematical problem involving calculus and differential equations that requires advanced reasoning.",
            "expected": "unknown",
            "description": "Complex problem (should trigger LLM fallback)"
        }
    ]
    
    print("\n🧪 Running test cases...")
    print("-" * 50)
    
    for i, test_case in enumerate(test_problems, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   Problem: {test_case['problem'][:60]}...")
        print(f"   Expected: {test_case['expected']}")
        
        # Create model input
        problem_input = ModelInput(
            problem_text=test_case['problem'],
            problem_id=f"test_{i}",
            expected_answer=test_case['expected']
        )
        
        # Solve with hybrid model
        result = manager.solve_problem("hybrid_solver", problem_input)
        
        if result:
            print(f"   ✅ Got: {result.answer}")
            print(f"   Confidence: {result.confidence_score:.2f}")
            print(f"   Processing time: {result.processing_time:.3f}s")
            
            # Check metadata
            if result.metadata:
                solver_type = result.metadata.get("solver_type", "unknown")
                llm_fallback = result.metadata.get("llm_fallback_used", False)
                print(f"   Solver type: {solver_type}")
                print(f"   LLM fallback used: {llm_fallback}")
                
                if "pattern_confidence" in result.metadata:
                    print(f"   Pattern confidence: {result.metadata['pattern_confidence']:.2f}")
        else:
            print("   ❌ No result returned")
    
    # Get hybrid model statistics
    print("\n📊 Hybrid Model Statistics")
    print("-" * 30)
    
    hybrid_model = manager.get_model("hybrid_solver")
    if hybrid_model:
        stats = hybrid_model.get_hybrid_stats()
        print(f"Total problems: {stats['total_problems']}")
        print(f"Pattern success rate: {stats['pattern_success_rate']:.2%}")
        print(f"LLM fallback rate: {stats['llm_fallback_rate']:.2%}")
        print(f"Confidence threshold: {stats['pattern_confidence_threshold']}")
        print(f"LLM fallback enabled: {stats['enable_llm_fallback']}")
    
    print("\n✅ HybridModel test completed!")


def test_confidence_threshold_adjustment():
    """Test adjusting the confidence threshold."""
    
    print("\n🔧 Testing Confidence Threshold Adjustment")
    print("=" * 50)
    
    manager = ModelManager()
    manager.initialize_model("hybrid_solver")
    
    hybrid_model = manager.get_model("hybrid_solver")
    if not hybrid_model:
        print("❌ Hybrid model not available")
        return
    
    # Test problem that pattern solver might solve with low confidence
    test_problem = "A simple addition problem: 5 + 3 = ?"
    problem_input = ModelInput(problem_text=test_problem, problem_id="threshold_test")
    
    print("Original threshold test:")
    result1 = hybrid_model.solve_problem(problem_input)
    print(f"   Answer: {result1.answer}, Confidence: {result1.confidence_score:.2f}")
    
    # Lower the threshold
    print("\nLowering confidence threshold to 0.1...")
    hybrid_model.update_confidence_threshold(0.1)
    
    result2 = hybrid_model.solve_problem(problem_input)
    print(f"   Answer: {result2.answer}, Confidence: {result2.confidence_score:.2f}")
    
    # Check if solver type changed
    if result2.metadata:
        print(f"   Solver type: {result2.metadata.get('solver_type', 'unknown')}")
    
    print("✅ Confidence threshold test completed!")


if __name__ == "__main__":
    test_hybrid_model()
    test_confidence_threshold_adjustment() 