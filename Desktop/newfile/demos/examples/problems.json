{"problems": [{"type": "investment_growth", "text": "An investment of $10,000 grows at an annual compound interest rate of 5%. What will be the value of the investment after 3 years?", "template": {"type": "investment_growth", "variables": {"initial_amount": 10000, "rate": 0.05, "time": 3, "compound_period": 1, "final_amount": "final investment value in dollars", "growth": "total growth in dollars"}, "equations": ["final_amount=initial_amount*(1+rate)^time", "growth=final_amount-initial_amount"], "units": {"initial_amount": "dollars", "rate": "decimal", "time": "years", "final_amount": "dollars", "growth": "dollars"}, "explicit_relations": ["The initial investment amount is $10,000.", "The annual interest rate is 5%.", "The investment duration is 3 years."], "implicit_relations": ["The final amount is calculated using the compound interest formula: A = P(1 + r)^t.", "The growth is the difference between the final amount and the initial amount."]}}, {"type": "rate_and_volume", "text": "A water tank needs to be filled with 100 cubic meters of water. If the flow rate is 20 cubic meters per hour, how long will it take to fill the tank?", "template": {"type": "rate_and_volume", "variables": {"volume": 100, "rate": 20, "time": "time to fill the tank in hours"}, "equations": ["time=volume/rate"], "units": {"volume": "cubic meters", "rate": "cubic meters/hour", "time": "hours"}, "explicit_relations": ["The tank needs to be filled with 100 cubic meters of water.", "The flow rate is 20 cubic meters per hour."], "implicit_relations": ["The time to fill the tank is the volume divided by the flow rate."]}}, {"type": "motion", "text": "A car travels at a constant speed of 60 km/h. How long will it take to cover a distance of 180 kilometers?", "template": {"type": "motion", "variables": {"speed": 60, "distance": 180, "time": "time to cover the distance in hours"}, "equations": ["time=distance/speed"], "units": {"speed": "kilometers/hour", "distance": "kilometers", "time": "hours"}, "explicit_relations": ["The car travels at a constant speed of 60 km/h.", "The distance to be covered is 180 kilometers."], "implicit_relations": ["The time to cover the distance is the distance divided by the speed."]}}, {"type": "mixture", "text": "A solution contains 30% acid. How many milliliters of pure water should be added to 100 ml of this solution to make a 20% acid solution?", "template": {"type": "mixture", "variables": {"concentration1": 0.3, "volume1": 100, "target_concentration": 0.2, "water_added": "volume of water to add in milliliters"}, "equations": ["final_concentration=(concentration1*volume1)/(volume1+water_added)"], "units": {"concentration1": "decimal", "volume1": "milliliters", "target_concentration": "decimal", "water_added": "milliliters"}, "explicit_relations": ["The initial solution contains 30% acid.", "The initial volume of the solution is 100 ml.", "The target concentration is 20% acid."], "implicit_relations": ["The amount of acid is conserved: initial amount = final amount.", "The initial amount of acid is the product of the initial volume and concentration.", "The final amount of acid is the product of the final volume and concentration.", "The volume of water to add is the difference between the final volume and the initial volume."]}}, {"type": "sequence", "text": "In a geometric sequence, the first term is 2 and the common ratio is 3. Find the sum of the first 5 terms.", "template": {"type": "sequence", "variables": {"first_term": 2, "ratio": 3, "n": 5, "sum": "sum of terms"}, "equations": ["sum=first_term*(1-ratio^n)/(1-ratio)"], "units": {"first_term": "number", "ratio": "number", "n": "count", "sum": "number"}, "explicit_relations": ["The first term is 2.", "The common ratio is 3.", "The number of terms is 5."], "implicit_relations": ["Each term is the previous term multiplied by the common ratio.", "The sum of the first n terms is given by the formula: Sn = a(1-r^n)/(1-r)."]}}, {"type": "ice_cube_problem", "text": "一个容器中装有80升水，温度为20℃。如果每分钟放入2个0℃的冰块（每个体积为1升），需要多长时间水温会降到10℃？假设水的比热容为4200J/(kg·℃)，冰的比热容为2100J/(kg·℃)。", "template": {"type": "ice_cube_problem", "variables": {"initial_water": 80, "initial_temp": 20, "target_temp": 10, "ice_temp": 0, "ice_volume": 1, "ice_rate": 2, "water_heat_capacity": 4200, "ice_heat_capacity": 2100}, "explicit_relations": ["初始水量为80升", "初始水温为20℃", "目标水温为10℃", "冰块温度为0℃", "每个冰块体积为1升", "每分钟放入2个冰块", "水的比热容为4200J/(kg·℃)", "冰的比热容为2100J/(kg·℃)"], "implicit_relations": ["水的热量变化 = 水的质量 × 比热容 × 温度变化", "冰的热量变化 = 冰的质量 × 比热容 × 温度变化", "总热量变化 = 水的热量变化 + 冰的热量变化", "所需时间 = 总热量变化 / (每分钟的热量变化)"]}}, {"type": "train_distance", "text": "一列火车从A站出发，速度为60km/h。2小时后，另一列火车从B站出发，速度为90km/h，朝着相同的方向行驶。如果两站相距240km，第二列火车需要多长时间才能追上第一列火车？", "template": {"type": "train_distance", "variables": {"distance": 240, "speed1": 60, "speed2": 90, "delay": 2}, "explicit_relations": ["两站相距240公里", "第一列火车速度为60公里/小时", "第二列火车速度为90公里/小时", "第二列火车延迟2小时出发"], "implicit_relations": ["第一列火车行驶距离 = 速度1 × (延迟时间 + 追及时间)", "第二列火车行驶距离 = 速度2 × 追及时间", "两车距离差 = 初始距离", "追及时间 = (初始距离 - 速度1 × 延迟时间) / (速度2 - 速度1)"]}}, {"type": "arithmetic_sequence", "text": "一个等差数列，首项为3，公差为2，求第20项的值和前20项的和。", "template": {"type": "arithmetic_sequence", "variables": {"first_term": 3, "difference": 2, "num_terms": 20}, "explicit_relations": ["首项为3", "公差为2", "求第20项和前20项的和"], "implicit_relations": ["每一项都是前一项加上公差", "第n项通项公式：an = a1 + (n-1)d", "等差数列求和公式：Sn = n(a1 + an)/2", "其中a1是首项，d是公差，n是项数"]}}, {"type": "work_efficiency", "text": "甲独自完成一项工作需要12天，乙独自完成同样的工作需要15天。如果他们一起工作，需要多少天才能完成这项工作？", "template": {"type": "work_efficiency", "variables": {"time1": 12, "time2": 15}, "explicit_relations": ["甲独自需要12天完成", "乙独自需要15天完成"], "implicit_relations": ["甲的效率 = 1/time1", "乙的效率 = 1/time2", "合作效率 = 甲的效率 + 乙的效率", "合作时间 = 1/合作效率"]}}, {"type": "pipe_flow", "text": "一个水池有两个进水管和一个出水管。第一个进水管每小时能注入8吨水，第二个进水管每小时能注入6吨水，出水管每小时能排出5吨水。如果水池原有20吨水，需要多长时间才能装满这个容量为50吨的水池？", "template": {"type": "pipe_flow", "variables": {"initial_water": 20, "capacity": 50, "inflow_rate1": 8, "inflow_rate2": 6, "outflow_rate": 5}, "explicit_relations": ["初始水量为20吨", "水池容量为50吨", "第一个进水管速率为8吨/小时", "第二个进水管速率为6吨/小时", "出水管速率为5吨/小时"], "implicit_relations": ["净注水速率 = 进水管1速率 + 进水管2速率 - 出水管速率", "需要增加的水量 = 容量 - 初始水量", "所需时间 = 需要增加的水量 / 净注水速率"]}}, {"type": "investment_growth", "text": "小明投资10000元，年利率5%，3年后能得到多少钱？", "template": {"type": "investment_growth", "variables": {"initial_amount": 10000, "rate": 0.05, "time": 3, "compound_period": 1, "final_amount": "final investment value in yuan", "growth": "total growth in yuan"}, "equations": ["final_amount=initial_amount*(1+rate)^time", "growth=final_amount-initial_amount"], "units": {"initial_amount": "yuan", "rate": "decimal", "time": "years", "final_amount": "yuan", "growth": "yuan"}, "explicit_relations": ["The initial investment amount is 10000 yuan.", "The annual interest rate is 5%.", "The investment duration is 3 years."], "implicit_relations": ["The final amount is calculated using the compound interest formula: A = P(1 + r)^t.", "The growth is the difference between the final amount and the initial amount."]}}, {"type": "rate_and_volume", "text": "一个水池容积为1000升，水流速度为50升/小时，需要多长时间才能注满？", "template": {"type": "rate_and_volume", "variables": {"volume": 1000, "rate": 50, "time": "time to fill the tank in hours"}, "equations": ["time=volume/rate"], "units": {"volume": "liters", "rate": "liters/hour", "time": "hours"}, "explicit_relations": ["The tank needs to be filled with 1000 liters of water.", "The flow rate is 50 liters per hour."], "implicit_relations": ["The time to fill the tank is the volume divided by the flow rate."]}}, {"type": "rate_and_volume", "text": "Ice cubes, each with a volume of 200 cm³, are dropped into a tank containing 5L of water at a rate of one cube per minute. Simultaneously, water is leaking from the tank through a tube at a rate of 2 mL per second. How long will it take for the water level in the tank to rise to 9L?", "answer": {"time": 50, "unit": "minutes"}}, {"type": "work_efficiency", "text": "<PERSON> and <PERSON> can complete a task in 12 hours when working together. If A works alone, he can complete the task in 20 hours. How long will it take <PERSON> to complete the task alone?", "answer": {"time": 30, "unit": "hours"}}, {"type": "investment_growth", "text": "An initial investment of 1000 dollars grows at an annual rate of 5%. What will be the value of the investment after 3 years?", "answer": {"value": 1157.63, "unit": "dollars"}}], "investment_growth": {"keywords": ["投资", "增长", "利率", "年利率", "本金", "收益"], "examples": [{"text": "小明投资10000元，年利率5%，3年后能得到多少钱？", "answer": {"value": 11576.25, "unit": "元"}}]}, "rate_and_volume": {"keywords": ["流速", "速率", "体积", "容量", "注满", "排空"], "examples": [{"text": "一个水池容积为1000升，水流速度为50升/小时，需要多长时间才能注满？", "answer": {"value": 20, "unit": "小时"}}]}}