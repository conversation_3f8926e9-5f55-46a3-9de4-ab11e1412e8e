[{"lEquations": ["44*7=x"], "lSolutions": [308.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Upon arriving at the circus, they went to the ticket booth and asked how much each ticket cost. If each ticket costs 44 dollars and they bought 7 tickets, how much money did they spend on tickets?", "iIndex": 399, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.866}, {"lEquations": ["63/9=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 63 magazines in her cabinet. She plans to send it to the recycling office in their area. If she places it in boxes which can contain 9 magazines, how many boxes will <PERSON><PERSON> use?", "iIndex": 423, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 3, "screened": true, "quality_score": 0.937}, {"lEquations": ["5.00-4.28=x"], "lSolutions": [0.72], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has $5.00 to buy an airplane that costs $4.28. How much change will <PERSON> get?", "iIndex": 457, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.931}, {"lEquations": ["212+68=x"], "lSolutions": [280.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has an aquarium with 212 fish. <PERSON> wants to buy 68 more fish. How many fish would <PERSON> have then?", "iIndex": 489, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.882}, {"lEquations": ["7.125 - 0.625 = x"], "lSolutions": [6.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s chemistry textbook weighs 7.125 pounds and her geometry textbook weighs 0.625 of a pound. How much more does the chemistry textbook weigh than the geometry textbook?", "iIndex": 350, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.972}, {"lEquations": ["37*19=x"], "lSolutions": [703.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "How many cookies would you have if you had 37 bags of cookies with 19 cookies in each bag?", "iIndex": 387, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.922}, {"lEquations": ["7-3=x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 7 dimes in his bank. His sister borrowed 3 of his dimes. How many dimes does <PERSON> have now ?", "iIndex": 73, "complexity_level": "L1", "dir_score": 0.36, "reasoning_steps": 4, "screened": true, "quality_score": 0.972}, {"lEquations": ["190.0+(0.04*x)=500.0"], "lSolutions": [7750.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> sells electronic supplies. Each week he earns 190 dollars plus commission equal to 0.04 of his sales. This week his goal is to earn no less than 500 dollars. How much sales  he must make to reach his goal?", "iIndex": 500, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 3, "screened": true, "quality_score": 0.891}, {"lEquations": ["(2*5.92)+6.79+x=20"], "lSolutions": [1.37], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Friday, <PERSON> paid $5.92 each on 2 tickets to a movie theater. He also borrowed a movie for $6.79. <PERSON> paid with a $20 bill. How much change did <PERSON> receive?", "iIndex": 231, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.898}, {"lEquations": ["54/9=x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had been saving large empty cans to serve as pots for sunflowers. If she has 54 sunflower seeds and there are 9 cans, how many seeds will be placed in each can if she places an equal number of seeds in each can?", "iIndex": 429, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.853}, {"lEquations": ["6+x=16"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were 6 roses in the vase. <PERSON> cut some more roses from her flower garden. There are now 16 roses in the vase. How many roses did she cut ?", "iIndex": 7, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.937}, {"lEquations": ["7+8+4=x"], "lSolutions": [19.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 7 dimes in her bank. Her dad gave her 8 dimes and her mother gave her 4 dimes. How many dimes does <PERSON> have now ?", "iIndex": 30, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 3, "screened": true, "quality_score": 0.965}, {"lEquations": ["48/8=x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>’s neighbor, joined <PERSON> in making bracelets. She brought 48 heart-shaped stones and wanted to have 8 of this type of stone in each of the bracelet she makes. How many bracelets with heart-shaped stones can <PERSON> make?", "iIndex": 427, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.899}, {"lEquations": ["7=4+x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> is baking a cake. The recipe wants 7 cups of flour. She already put in 4 cups. How many more cups does she need to add ?", "iIndex": 57, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.975}, {"lEquations": ["20*5=x"], "lSolutions": [100.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "My car gets 20 miles per gallon of gas. How many miles can I drive on 5 gallons of gas?", "iIndex": 282, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.974}, {"lEquations": ["7.75 + 7 = x"], "lSolutions": [14.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 7.75 gallons of water in <PERSON>'s fish tank. If <PERSON> adds 7 gallons more, how many gallons will there be in all?", "iIndex": 365, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.955}, {"lEquations": ["2+9=x"], "lSolutions": [11.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 2 apples and <PERSON> picked 9 apples from the apple tree. How many apples were picked in total ?", "iIndex": 26, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.952}, {"lEquations": ["0.375 + 0.5 = x"], "lSolutions": [0.875], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to the salon and had 0.375 of an inch of hair cut off. The next day she went back and asked for another 0.5 of an inch to be cut off. How much hair did she have cut off in all?", "iIndex": 311, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.946}, {"lEquations": ["0.2 + 0.7 = x"], "lSolutions": [0.9], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> walked 0.2 of a mile from school to <PERSON>'s house and 0.7 of a mile from <PERSON>'s house to his own house. How many miles did <PERSON> walk in all?", "iIndex": 316, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.958}, {"lEquations": ["5*x=50"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "It has been tradition in <PERSON><PERSON>’s home to hang a sock above the fireplace for each member of the family. This year, she placed a cinnamon ball every day in the socks for 5 of her family members. How long can she do this if she bought 50 cinnamon balls?", "iIndex": 418, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.914}, {"lEquations": ["11.08+14.33+9.31=x"], "lSolutions": [34.72], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> loves eating fruits. <PERSON> paid $11.08 for berries, $14.33 for apples, and $9.31 for peaches. In total, how much money did she spend?", "iIndex": 221, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.897}, {"lEquations": ["300/60=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> spent 300 minutes working on her science project. How many hours did <PERSON> spend on her science project?", "iIndex": 469, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 3, "screened": true, "quality_score": 0.935}, {"lEquations": ["9+6=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant served 9 pizzas during lunch and 6 during dinner today. How many pizzas were served today ?", "iIndex": 22, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.862}, {"lEquations": ["12.50*12=x"], "lSolutions": [150.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> earns $12.50 an hour cleaning houses. If she works for 12 hours, how much money will she make ?", "iIndex": 142, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.892}, {"lEquations": ["81/9=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s younger brother helped pick up all the paper clips in <PERSON>’s room. He was able to collect 81 paper clips. If he wants to distribute the paper clips in 9 boxes, how many paper clips will each box contain?", "iIndex": 410, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 2, "screened": true, "quality_score": 0.939}, {"lEquations": ["344-136=x"], "lSolutions": [208.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON><PERSON> has 344 books. 136 are about school and the rest are about sports. How many books about sports does <PERSON><PERSON><PERSON> have?", "iIndex": 452, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.962}, {"lEquations": ["8-2=x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s cat had 8 kittens. She gave 2 to her friends.  How many kittens does she have now ?", "iIndex": 74, "complexity_level": "L1", "dir_score": 0.39, "reasoning_steps": 3, "screened": true, "quality_score": 0.917}, {"lEquations": ["5*2=x"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 5 blue marbles. <PERSON> has 2 times more blue marbles than <PERSON>. How many blue marbles does <PERSON> have ?", "iIndex": 131, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.965}, {"lEquations": ["9+8=x"], "lSolutions": [17.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 yellow balloons. <PERSON> has 8 yellow balloons. How many yellow balloons do they have in total ?", "iIndex": 18, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.898}, {"lEquations": ["2.3333333333333335 - 1 = x"], "lSolutions": [1.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> just bought a new lamp for her bedside table. The old lamp was 1 foot tall and the new lamp is 2.3333333333333335 feet tall. How much taller is the new lamp than the old lamp?", "iIndex": 363, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.924}, {"lEquations": ["0.6666666666666666 - 0.**************** = x"], "lSolutions": [0.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "At a pie-eating contest, <PERSON> got through 0.6666666666666666 of a pie before time was called; <PERSON> finished just 0.**************** of a pie. How much more pie did <PERSON> eat than <PERSON>?", "iIndex": 289, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.861}, {"lEquations": ["27*18=x"], "lSolutions": [486.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 27 packages of gum. There are 18 pieces in each package. How many pieces of gum does <PERSON> have?", "iIndex": 456, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.934}, {"lEquations": ["8-3=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 8 quarters in her bank. Her sister borrowed 3 of her quarters. How many quarters does <PERSON> have now ?", "iIndex": 190, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.897}, {"lEquations": ["7*(15/5)=x"], "lSolutions": [21.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> saw a rollercoaster. 7 students rode the rollercoaster every 5 minutes. How many students rode the rollercoaster in 15 minutes?", "iIndex": 486, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.936}, {"lEquations": ["0.**************** + 0.**************** + 0.**************** = x"], "lSolutions": [0.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> recorded the snowfall every day during a snowstorm. He recorded 0.**************** of a centimeter on Wednesday, 0.**************** of a centimeter on Thursday, and 0.**************** of a centimeter on Friday. How many total centimeters of snow did <PERSON> record?", "iIndex": 341, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 4, "screened": true, "quality_score": 0.865}, {"lEquations": ["20+21=x"], "lSolutions": [41.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 20 books. <PERSON> has 21 books.  How many books do they have together ?", "iIndex": 37, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.936}, {"lEquations": ["22+x=55"], "lSolutions": [33.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 22 walnut trees currently in the park. Park workers will plant more walnut trees today. When the workers are finished there will be 55 walnut trees in the park. How many walnut trees did the workers plant today ?", "iIndex": 5, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.958}, {"lEquations": ["(9*0.01)+(7*0.25)=x"], "lSolutions": [1.84], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sam got 9 pennies for washing clothes, and 7 quarters for mowing lawns. How much money does <PERSON> have?", "iIndex": 206, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.874}, {"lEquations": ["43+19+16=x"], "lSolutions": [78.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 43 pencils in the drawer and 19 pencils on the desk. <PERSON> placed  16 more pencils on the desk. How many pencils are now there in total ?", "iIndex": 55, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.892}, {"lEquations": ["489+x=2778"], "lSolutions": [2289.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has some marbles. <PERSON> bought 489 marbles. Now Calra has 2778 marbles all together. How many did <PERSON> start with?", "iIndex": 447, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.957}, {"lEquations": ["0.125 + 0.5 = x"], "lSolutions": [0.625], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In Yardley it snowed 0.125 of an inch in the morning and 0.5 of an inch in the afternoon. What was the total amount of snowfall?", "iIndex": 323, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 3, "screened": true, "quality_score": 0.878}, {"lEquations": ["6+7=x"], "lSolutions": [13.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 6 potatoes. <PERSON> grew 7 potatoes. How many potatoes did they grow in total ?", "iIndex": 65, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.93}, {"lEquations": ["177.0+(19.0*x)=500.0"], "lSolutions": [17.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> opened a savings account with an initial deposit of 177 dollars. If he wants to save 500 dollars during the next 19 weeks , how much must he save each week , in dollars?", "iIndex": 506, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.896}, {"lEquations": ["1.125 + 2.125 = x"], "lSolutions": [3.25], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> jogged 1.125 laps in P.E. class and 2.125 laps during track practice. How many laps did <PERSON> jog in all?", "iIndex": 353, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.897}, {"lEquations": ["9*x=72"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "9 of <PERSON><PERSON>’s closest friends like stickers. If she plans to give all of them an equal number of stickers, how many will each receive if she has 72 stickers?", "iIndex": 419, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.866}, {"lEquations": ["36*7=x"], "lSolutions": [252.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> has 36 books. <PERSON> has 7 times more books than  <PERSON><PERSON>. How many books does <PERSON> have ?", "iIndex": 164, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.901}, {"lEquations": ["54-(15+25)=x"], "lSolutions": [14.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> needs 54 cupcakes for a birthday party. She already has 15 chocolate cupcakes and 25 vanilla cupcakes. How many more cupcakes should <PERSON><PERSON> buy?", "iIndex": 271, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.85}, {"lEquations": ["7+5=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 7 nickels in her bank. Her dad gave her 5 more nickels. How many nickels does <PERSON> have now ?", "iIndex": 134, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 4, "screened": true, "quality_score": 0.933}, {"lEquations": ["((15+13)+8)-21=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> was collecting coins. She got 15 coins from her piggy bank and 13 coins from her brother. Her father gave <PERSON> 8 coins. <PERSON> gave 21 of the coins to her friend <PERSON>. How many coins did <PERSON> have left?", "iIndex": 262, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.912}, {"lEquations": ["35-18=x"], "lSolutions": [17.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 35 seashells on the beach, he gave <PERSON> 18 of the seashells. How many seashells does he now have ?", "iIndex": 78, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.898}, {"lEquations": ["29*7=x"], "lSolutions": [203.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 29 violet balloons. <PERSON> has 7 times more violet balloons than <PERSON>. How many violet balloons does <PERSON> have ?", "iIndex": 168, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 2, "screened": true, "quality_score": 0.922}, {"lEquations": ["121-22=x"], "lSolutions": [99.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 121 Nintendo games. How many does <PERSON> need to give away so that <PERSON> will have 22 games left?", "iIndex": 449, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.886}, {"lEquations": ["323+175=x"], "lSolutions": [498.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 323 marbles. <PERSON> has 175 more marbles than <PERSON>. How many marbles does <PERSON> have?", "iIndex": 455, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.952}, {"lEquations": ["142.46+8.89+7=x"], "lSolutions": [158.35], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> joined his school's band. He bought a flute for $142.46, a music stand for $8.89, and a song book for $7. How much did <PERSON> spend at the music store?", "iIndex": 223, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 2, "screened": true, "quality_score": 0.876}, {"lEquations": ["(3*x)+6=162"], "lSolutions": [52.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The sum of three consecutive even numbers is 162. What is the smallest of the three numbers ?", "iIndex": 11, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.934}, {"lEquations": ["0.3 + 0.1 + 0.4 = x"], "lSolutions": [0.8], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Last Saturday, <PERSON> walked all over town running errands. First, he walked 0.3 of a mile from his house to the library and 0.1 of a mile from the library to the post office. Then he walked 0.4 of a mile from the post office back home. How many miles did <PERSON> walk in all?", "iIndex": 337, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.957}, {"lEquations": ["(17*0.5)+(6*0.5)=x"], "lSolutions": [11.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> sold lemonade in her neighborhood. She got 17 half-dollars on Saturday and 6 half-dollars on Sunday. What amount of money did <PERSON> receive?", "iIndex": 205, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.947}, {"lEquations": ["1346+6444=x"], "lSolutions": [7790.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Before December, customers buy 1,346 ear muffs from the mall. During December, they buy 6,444 more, and there are none left. In all, how many ear muffs do the customers buy?", "iIndex": 234, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.891}, {"lEquations": ["13*((9+10)+3)=x"], "lSolutions": [286.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> earns $13 per hour. She worked 9 hours on Friday, 10 hours on Saturday, and 3 hours on Sunday. How much money did <PERSON> earn in all?", "iIndex": 257, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.972}, {"lEquations": ["(12*0.25)=x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has saved 1 dozen quarters from washing cars.  How much money does <PERSON> have ?", "iIndex": 172, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.965}, {"lEquations": ["(15*0.1)+(4*0.1)=x"], "lSolutions": [1.9], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "While digging through her clothes for ice cream money, <PERSON> found 15 dimes in her jacket, and 4 dimes in her shorts. How much money did <PERSON> find?", "iIndex": 211, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.916}, {"lEquations": ["9+3=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 9 crayons in the drawer. <PERSON> placed 3 more crayons in the drawer. How many crayons are now there in total ?", "iIndex": 66, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.954}, {"lEquations": ["((7+12)+4)-(6+13)=x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> wants to buy a jump rope that costs $7, a board game that costs $12, and a playground ball that costs $4. He has saved $6 from his allowance, and his uncle gave him $13. How much more money does <PERSON> need to buy the jump rope, the game, and the ball?", "iIndex": 269, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.976}, {"lEquations": ["11+17+16=x"], "lSolutions": [44.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to 11 football games this month. He went to 17 games last month, and plans to go to 16 games next month. How many games will he attend in all ?", "iIndex": 54, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 2, "screened": true, "quality_score": 0.893}, {"lEquations": ["(7.19+6.83)+x=20"], "lSolutions": [5.98], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> loves eating fruits. <PERSON> paid $7.19 for berries, and $6.83 for peaches with a $20 bill. How much change did <PERSON> receive?", "iIndex": 228, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.915}, {"lEquations": ["124*3=x"], "lSolutions": [372.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 124 students making 3 stars each for the school wall. How many stars will they make all together?", "iIndex": 468, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.949}, {"lEquations": ["0.8333333333333334 - 0.5 = x"], "lSolutions": [0.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "At the beach, <PERSON><PERSON> and her sister both built sandcastles and then measured their heights. <PERSON><PERSON>'s sandcastle was 0.8333333333333334 of a foot tall and her sister's was 0.5 of a foot tall. How much taller was <PERSON><PERSON>'s sandcastle than her sister's?", "iIndex": 313, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.878}, {"lEquations": ["0.25 + 0.5 = x"], "lSolutions": [0.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "At the hardware store, 0.25 of the nails are size 2d and 0.5 of the nails are size 4d. What fraction of the nails are either size 2d or 4d?", "iIndex": 318, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 4, "screened": true, "quality_score": 0.888}, {"lEquations": ["(3*0.05)+(13*0.1)+(7*0.1)+(9*0.5)=x"], "lSolutions": [6.65], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> got 3 nickels and 13 dimes for shining shoes, and in his tip jar found 7 dimes and 9 half-dollars. How much money did <PERSON> get?", "iIndex": 210, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.919}, {"lEquations": ["79-(9*x)=16"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 79 dollars to spend on 9 books. After buying them he had 16 dollars. How much did each book cost ?", "iIndex": 12, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.9}, {"lEquations": ["6*71=x"], "lSolutions": [426.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "White t-shirts can be purchased in packages of 6. If <PERSON> buys 71 packages, how many white t-shirts will <PERSON> have?", "iIndex": 442, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.885}, {"lEquations": ["0.5 + 0.1 = x"], "lSolutions": [0.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In one week, <PERSON>'s family drank 0.5 of a carton of regular milk and 0.1 of a carton of soy milk. How much milk did they drink in all?", "iIndex": 310, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.953}, {"lEquations": ["0.16666666666666666 + 0.16666666666666666 + 0.5 = x"], "lSolutions": [0.8333333333333334], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A construction company ordered 0.16666666666666666 of a ton of concrete, 0.16666666666666666 of a ton of bricks, and 0.5 of a ton of stone. How many tons of material did the company order in all?", "iIndex": 338, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.891}, {"lEquations": ["2041+63093=x"], "lSolutions": [65134.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A multi-national corporation has 2,041 part-time employees and 63,093 full-time employees. How many employees work for the corporation?", "iIndex": 249, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.876}, {"lEquations": ["x-9=4"], "lSolutions": [13.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had Pokemon cards. <PERSON> gave 9 to his friends. <PERSON> now has 4 Pokemon cards left. How many Pokemon cards did <PERSON> have to start with ?", "iIndex": 276, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.905}, {"lEquations": ["43-23=x"], "lSolutions": [20.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 43 pumpkins, but the rabbits ate 23 pumpkins. How many pumpkins does <PERSON> have left ?", "iIndex": 201, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.941}, {"lEquations": ["813-125=x"], "lSolutions": [688.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> has 813 stamps. <PERSON><PERSON> has 125 more stamps than Minerva. How many stamps does Minerva have?", "iIndex": 441, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.889}, {"lEquations": ["9.14+6.81+x=20"], "lSolutions": [4.05], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought some toys. She bought a football for $9.14, and paid $6.81 on a baseball with a $20 bill. How much change did he receive from the purchase?", "iIndex": 232, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.872}, {"lEquations": ["53=7+14+x"], "lSolutions": [32.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> needs 53 cupcakes for a birthday party. He already has 7 chocolate cupcakes and 19 vanilla cupcakes. How many more cupcakes should <PERSON> buy?", "iIndex": 279, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.867}, {"lEquations": ["2+((12*0.01)*x)=23.36"], "lSolutions": [178.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "For his long distance phone service Milan pays a 2 dollars monthly fee plus 12 cents per minute. Last month , Milan 's long distance bill was 23.36 dollars. For how many minutes was Milan billed for ?", "iIndex": 498, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.855}, {"lEquations": ["63/7=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant sold 63 hamburgers last week. How many hamburgers on average were sold each day ?", "iIndex": 101, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.899}, {"lEquations": ["9.24*****+x=20"], "lSolutions": [2.51], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to the mall on Saturday to buy clothes. She paid $9.24 on pants and $8.25 on a shirt with a $20 bill. How much money did <PERSON> get in change?", "iIndex": 229, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.885}, {"lEquations": ["4*x=4.80"], "lSolutions": [1.2], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> rented 4 DVDs for $4.80. How much did each DVD cost to rent?", "iIndex": 464, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.893}, {"lEquations": ["1 + 8.8 = x"], "lSolutions": [9.8], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On a hot day, <PERSON> poured 1 bucket of water into a plastic wading pool. A few minutes later he added another 8.8 buckets. How much water did <PERSON> pour into the pool?", "iIndex": 352, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.946}, {"lEquations": ["324*9=x"], "lSolutions": [2916.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> saved checking on the grapevines for his last stop. He was told by one of the pickers that they fill 324 drums of grapes per day. How many drums of grapes would be filled in 9 days?", "iIndex": 398, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.875}, {"lEquations": ["4*3=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, <PERSON>, and <PERSON> each have 3 baseball cards.  How many baseball cards do they have in all ?", "iIndex": 135, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.953}, {"lEquations": ["145.16+5.84=x"], "lSolutions": [151.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> joined his school's band. He bought a trumpet for $145.16, and a song book which was $5.84. How much did <PERSON> spend at the music store?", "iIndex": 214, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.875}, {"lEquations": ["18+41=x"], "lSolutions": [59.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 18 seashells and <PERSON> found 41 seashells on the beach. How many seashells did they find together ?", "iIndex": 46, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.905}, {"lEquations": ["8*45=x"], "lSolutions": [360.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Students went to a concert in 8 buses. Each bus took 45 students. How many students went to the concert?", "iIndex": 467, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.94}, {"lEquations": ["3*6=x"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> worked 3 hours for 6 days. How many hours did he work in total ?", "iIndex": 158, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.971}, {"lEquations": ["9.91 + 4.11 = x"], "lSolutions": [14.02], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought two watermelons. The first watermelon was 9.91 pounds, and the second watermelon was 4.11 pounds. How many pounds of watermelon did <PERSON> buy?", "iIndex": 371, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.972}, {"lEquations": ["3*5=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 3 calories in a candy bar. How many calories are there in 5 candy bars ?", "iIndex": 130, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.864}, {"lEquations": ["14797+4969=x"], "lSolutions": [19766.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A bathing suit manufacturer has a supply of 14,797 bathing suits for men. In addition, it has 4,969 bathing suits for women. How many bathing suits are available overall?", "iIndex": 251, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.914}, {"lEquations": ["4*(9*12)=x"], "lSolutions": [432.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, <PERSON>, and <PERSON> each have 9 dozen Pokemon cards.  How many Pokemon cards do they have in all ?", "iIndex": 177, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.951}, {"lEquations": ["37*8=x"], "lSolutions": [296.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> invited 37 people to her birthday party. They each ate 8 pieces of pizza. How many pieces of pizza did they eat?", "iIndex": 475, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 2, "screened": true, "quality_score": 0.965}, {"lEquations": ["(x+4)/2=34"], "lSolutions": [64.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 4 new baseball trading cards to add to his collection. The next day his dog ate half of his collection. There are now only 34 cards left. How many cards did <PERSON> start with ?", "iIndex": 9, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.897}, {"lEquations": ["0.625 - 0.25 = x"], "lSolutions": [0.375], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> made cookies. She used 0.625 of a cup of flour and 0.25 of a cup of sugar. How much more flour than sugar did <PERSON><PERSON> use?", "iIndex": 303, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.934}, {"lEquations": ["1110=30*x"], "lSolutions": [37.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 1110 students at a school. If each classroom holds 30 students, how many classrooms are needed at the school?", "iIndex": 147, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.939}, {"lEquations": ["2+7+1=1+x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> wants to ride the Ferris wheel, the roller coaster, and the log ride. The Ferris wheel costs 2 tickets, the roller coaster costs 7 tickets and the log ride costs 1 ticket. <PERSON> has 1 ticket. How many more tickets should <PERSON> buy?", "iIndex": 278, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 2, "screened": true, "quality_score": 0.934}, {"lEquations": ["10.22+11.73=x"], "lSolutions": [21.95], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> spent $10.22 on a cat toy, and a cage cost her $11.73. What was the total cost of <PERSON>'s purchases?", "iIndex": 217, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 2, "screened": true, "quality_score": 0.943}, {"lEquations": ["0.9 - 0.7 = x"], "lSolutions": [0.2], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "It rained 0.9 inches on Monday. On Tuesday, it rained 0.7 inches less than on Monday. How much did it rain on Tuesday?", "iIndex": 375, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.881}, {"lEquations": ["x-183=593"], "lSolutions": [776.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had some marbles. <PERSON> gave 183 to <PERSON>. Now <PERSON> has 593 marbles left. How many did <PERSON> have to start with?", "iIndex": 450, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.974}, {"lEquations": ["0.16666666666666666 + 0.**************** + 0.16666666666666666 = x"], "lSolutions": [0.6666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A renovation project required 0.16666666666666666 of a truck-load of sand, 0.**************** of a truck-load of dirt, and 0.16666666666666666 of a truck-load of cement. How many truck-loads of material were needed in all?", "iIndex": 345, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.929}, {"lEquations": ["2*x=18"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> helped her mom prepare fresh lemonade. If each glass needs 2 lemons, how many glasses of fresh lemonade can she make if they have 18 lemons?", "iIndex": 431, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.874}, {"lEquations": ["192=x*12"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 192 golf balls. How many dozen golf balls does she have?", "iIndex": 119, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.945}, {"lEquations": ["5-2=x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 5 dimes in her bank. She spent 2 of her dimes. How many dimes does she have now ?", "iIndex": 126, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.966}, {"lEquations": ["x+25+20+75=279"], "lSolutions": [159.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has been saving his allowance to buy a new pair of soccer cleats and a ball. His grandmother gave <PERSON> $25 for his birthday. His aunt and uncle gave <PERSON> $20 and his parents gave him $75. Now <PERSON> had $279. How much money did <PERSON> have before his birthday?", "iIndex": 491, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 2, "screened": true, "quality_score": 0.863}, {"lEquations": ["((17+48)+25)-49=x"], "lSolutions": [41.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> saved $17 in September. He saved $48 in October and $25 in November. Then <PERSON> spent $49 on a video game. How much money does <PERSON> have left?", "iIndex": 258, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.88}, {"lEquations": ["8*478=x"], "lSolutions": [3824.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The library is divided into different sections for different type of books. The science fiction section has 8 books. If each book has 478 pages, how many pages do all the books have in total?", "iIndex": 404, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.94}, {"lEquations": ["20*x=80"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s father and the senior ranger gathered firewood as they walked towards the lake in the park and brought with them sacks. If every sack can contain around 20 pieces of wood, how many sacks were they able to fill if they gathered 80 pieces of wood?", "iIndex": 413, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.966}, {"lEquations": ["(2*0.01)+(12*0.1)=x"], "lSolutions": [1.22], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Friday, <PERSON> spent 2 pennies on ice cream. The next day, <PERSON> spent 12 dimes on baseball cards. All in all, how much money did <PERSON> spend?", "iIndex": 212, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.974}, {"lEquations": ["0.75 - 0.5 = x"], "lSolutions": [0.25], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A tailor cut 0.75 of an inch off a skirt and 0.5 of an inch off a pair of pants. How much more did the tailor cut off the skirt than the pants?", "iIndex": 290, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.866}, {"lEquations": ["(24*2)+(14*3)=x"], "lSolutions": [90.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 24 bicycles and 14 tricycles in the storage area at <PERSON>'s apartment building. Each bicycle has 2 wheels and each tricycle has 3 wheels. How many wheels are there in all?", "iIndex": 273, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.917}, {"lEquations": ["80/8=x"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "She counted her crayons and found out that she has 80 crayons which she will place in crayon boxes. Every box can contain 8 crayons. How many boxes does she need?", "iIndex": 407, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.94}, {"lEquations": ["4+9=x"], "lSolutions": [13.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to 4 football games this year. She went to 9 football games last year. How many football games did <PERSON> go to in all ?", "iIndex": 17, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.979}, {"lEquations": ["60*45=x*12"], "lSolutions": [225.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 60 calories in a candy bar. How many dozen calories are there in 45 candy bars?", "iIndex": 116, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.863}, {"lEquations": ["3*8=x"], "lSolutions": [24.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, and <PERSON> each have 8 pencils. How many pencils do they have have in all ?", "iIndex": 162, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.864}, {"lEquations": ["45*x=315"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s shelves hold 45 books each. How many shelves will <PERSON> need if <PERSON> has 315 books?", "iIndex": 109, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.959}, {"lEquations": ["3 - 1.1666666666666667 = x"], "lSolutions": [1.8333333333333335], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found an orange caterpillar and a green caterpillar in her backyard. The green caterpillar was 3 inches long and the orange caterpillar was 1.1666666666666667 inches long. How much longer was the green caterpillar than the orange caterpillar?", "iIndex": 360, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.873}, {"lEquations": ["140/14=x"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> also bought 140 shiny blue round stones. If 14 pieces of this stone is in each bracelet, how many bracelets of blue shiny round stones will there be?", "iIndex": 425, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.874}, {"lEquations": ["18+47=x"], "lSolutions": [65.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 18 seashells and <PERSON> found 47 seashells on the beach. How many seashells did they find together ?", "iIndex": 35, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 4, "screened": true, "quality_score": 0.963}, {"lEquations": ["4900/100=x"], "lSolutions": [49.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has saved 4900 cents from selling lemonade.  How many dollars does <PERSON> have?", "iIndex": 153, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 2, "screened": true, "quality_score": 0.891}, {"lEquations": ["(16+14)/5=x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> baked pies last weekend for a holiday dinner. She baked 16 pecan pies and 14 apples pies. If she wants to arrange all of the pies in rows of 5 pies each, how many rows will she have?", "iIndex": 479, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.955}, {"lEquations": ["3-2=x"], "lSolutions": [1.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 3 Pokemon cards. <PERSON> bought 2 of <PERSON>'s  Pokemon cards. How many Pokemon cards does <PERSON> have now ?", "iIndex": 124, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.954}, {"lEquations": ["9=3+6"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant made 9 hamburgers to serve during lunch. Only 3 were  actually served. How many hamburgers were left over from lunch ?", "iIndex": 191, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 3, "screened": true, "quality_score": 0.923}, {"lEquations": ["(4*540)/20=x"], "lSolutions": [108.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> each bought 540 baseball cards,  which come in packs of 20. How many packs of baseball cards do they have in all?", "iIndex": 113, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.962}, {"lEquations": ["13+x=55"], "lSolutions": [42.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 13 peaches left at her roadside fruit stand. She went to the orchard and picked more peaches to stock up the stand. There are now 55 peaches at the stand, how many did she pick ?", "iIndex": 60, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.866}, {"lEquations": ["303*28=x"], "lSolutions": [8484.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> collected eggs from the hen and put them into 303 baskets. She put 28 eggs into each basket. How many eggs did <PERSON> collect?", "iIndex": 391, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.922}, {"lEquations": ["6*12=x"], "lSolutions": [72.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 6 dozen eggs from the grocery store to bake some cakes.  How many eggs did <PERSON> buy ?", "iIndex": 144, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.939}, {"lEquations": ["7=4+x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 7 football games this year. <PERSON> missed 4 of the games. How many football games did <PERSON> go to in all ?", "iIndex": 68, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.941}, {"lEquations": ["0.75 - 0.25 = x"], "lSolutions": [0.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s bus ride to school is 0.75 of a mile and <PERSON>'s bus ride is 0.25 of a mile. How much longer is <PERSON>'s bus ride than <PERSON>'s?", "iIndex": 286, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.968}, {"lEquations": ["1.54*3=x"], "lSolutions": [4.62], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "If each ball costs $1.54, how much must <PERSON><PERSON><PERSON> pay for 3 balls?", "iIndex": 458, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.953}, {"lEquations": ["91-((24+6)+11)=x"], "lSolutions": [50.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s mom gave her $91 to go shopping. She bought a sweater for $24, a T-shirt for $6, and a pair of shoes for $11. How much money does <PERSON> have left?", "iIndex": 261, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.946}, {"lEquations": ["390=30*x"], "lSolutions": [13.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 390 students at a school. If each classroom holds 30 students, how many classrooms are needed at the school?", "iIndex": 106, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.967}, {"lEquations": ["43-27=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 43 apples from the orchard, and gave 27 apples  to  <PERSON>. How many apples does <PERSON> have now ?", "iIndex": 196, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 3, "screened": true, "quality_score": 0.902}, {"lEquations": ["9*5=x"], "lSolutions": [45.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 five dollars bills.  How much money does she have ?", "iIndex": 160, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.974}, {"lEquations": ["9+7+5=x"], "lSolutions": [21.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 9 seashells, <PERSON> found 7 seashells, and <PERSON> found 5 seashells on the beach. How many seashells did they find together ?", "iIndex": 27, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.905}, {"lEquations": ["760-418=x"], "lSolutions": [342.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 760 quarters in her bank. She spent 418 of her quarters. How many quarters does she have now ?", "iIndex": 92, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.922}, {"lEquations": ["0.3 - 0.2 = x"], "lSolutions": [0.1], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A marine biologist measured one fish that was 0.3 of a foot long and a second fish that was 0.2 of a foot long. How much longer was the first fish?", "iIndex": 293, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.918}, {"lEquations": ["38+44=x"], "lSolutions": [82.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 38 cantelopes. <PERSON> grew 44 cantelopes. How many cantelopes did they grow in total ?", "iIndex": 44, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.955}, {"lEquations": ["1400-290=x"], "lSolutions": [1110.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 1400 crayons. <PERSON> has 290 crayons. How many more crayons does <PERSON> have then <PERSON>?", "iIndex": 436, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 4, "screened": true, "quality_score": 0.917}, {"lEquations": ["2.8333333333333335 + 2.8333333333333335 = x"], "lSolutions": [5.666666666666667], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s Vegetarian Restaurant bought 2.8333333333333335 pounds of green peppers and 2.8333333333333335 pounds of red peppers. How many pounds of peppers did <PERSON>'s Vegetarian Restaurant buy in all?", "iIndex": 348, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.902}, {"lEquations": ["0.2 + 0.4 + 0.1 = x"], "lSolutions": [0.7], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s family went on a camping trip in the mountains. On the first day, they hiked from their car to the campsite. First, they hiked 0.2 of a mile from the car to a stream, and 0.4 of a mile from the stream to a meadow. Then they hiked 0.1 of a mile from the meadow to the campsite. How many miles did <PERSON>'s family hike in all?", "iIndex": 326, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.911}, {"lEquations": ["0.75 - 0.5 = x"], "lSolutions": [0.25], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> filled a bucket with 0.75 of a gallon of water. A few minutes later, she realized only 0.5 of a gallon of water remained. How much water had leaked out of the bucket?", "iIndex": 325, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.898}, {"lEquations": ["0.9 - 0.5 = x"], "lSolutions": [0.4], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "When <PERSON> had one cat, he needed to serve 0.5 of a can of cat food each day. Now that <PERSON> has adopted a second cat, he needs to serve a total of 0.9 of a can each day. How much extra food is needed to feed the second cat?", "iIndex": 322, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.925}, {"lEquations": ["(2*10.62)*****=x"], "lSolutions": [36.78], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Saturday, <PERSON> spent $10.62 each on 2 tickets to a movie theater. She also rented a movie for $1.59, and bought a movie for $13.95. How much money in total did <PERSON> spend on movies?", "iIndex": 226, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.931}, {"lEquations": ["0.7 + 0.2 = x"], "lSolutions": [0.9], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In Mr<PERSON>'s mathematics class, 0.7 of the students received A's and 0.2 received B's. What fraction of the students received either A's or B's?", "iIndex": 299, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.854}, {"lEquations": ["8.9 - 2.3 = x"], "lSolutions": [6.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A carpenter bought a piece of wood that was 8.9 centimeters long. Then he sawed 2.3 centimeters off the end. How long is the piece of wood now?", "iIndex": 386, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.875}, {"lEquations": ["4.1 - 2.4 = x"], "lSolutions": [1.7], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A truck carrying 4.1 pounds of sand travels to a construction yard and loses 2.4 pounds of sand along the way. How much sand does the truck have when it arrives at the yard?", "iIndex": 384, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.972}, {"lEquations": ["1.30/5=x"], "lSolutions": [26.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> bought a notebook for $1.30. She paid with nickels. How many nickels did she use to buy the notebook?", "iIndex": 485, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.868}, {"lEquations": ["4+5+9=x"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 4 onions, <PERSON> grew 5 onions, and <PERSON> grew  9 onions. How many onions did they grow in all ?", "iIndex": 34, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.864}, {"lEquations": ["5+4=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 5 oak trees currently in the park. Park workers will plant 4 more oak trees today. How many oak trees will the park have when the workers are finished ?", "iIndex": 67, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.876}, {"lEquations": ["0.14 + 0.38 = x"], "lSolutions": [0.52], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A chef bought 0.14 kilograms of almonds and 0.38 kilograms of pecans. How many kilograms of nuts did the chef buy in all?", "iIndex": 382, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.978}, {"lEquations": ["7*4=x"], "lSolutions": [28.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 7 black balloons. <PERSON> has 4 times more  black balloons than <PERSON>. How many black balloons does <PERSON> have now ?", "iIndex": 161, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.881}, {"lEquations": ["16000.0/0.8=x"], "lSolutions": [20000.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The value of a sport utility vehicle this year is 16,000 dollars , which is 0.8 of what its value was last year. How much is the value of the vehicle last year?", "iIndex": 499, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.9}, {"lEquations": ["600-327=x"], "lSolutions": [273.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A pet supply store has 600 bags of dog food and 327 bags of cat food. How many more bags of dog food are there than cat food?", "iIndex": 493, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.903}, {"lEquations": ["1800000.0=0.75*x"], "lSolutions": [2400000.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Freeport McMoran projects the world grain supply will be 1800000 metric tons and the supply will be only 0.75 of the world grain demand. What will the world grain demand be?", "iIndex": 504, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 4, "screened": true, "quality_score": 0.906}, {"lEquations": ["6*33=x"], "lSolutions": [198.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The mini library also has a section for the classics. If <PERSON> has a collection of 6 classic authors, with each author having 33 books, how many books does he have in the classics section?", "iIndex": 406, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.882}, {"lEquations": ["4*2=x"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 4 children in the classroom, each student will get 2 pencils. How many pencils will the teacher have to give out ?", "iIndex": 137, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.903}, {"lEquations": ["41+30=x"], "lSolutions": [71.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 41 pencils in the drawer. <PERSON> placed 30 more  pencils in the drawer. How many pencils are now there in total ?", "iIndex": 41, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.892}, {"lEquations": ["6359+3485=x"], "lSolutions": [9844.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s family moved from the Bahamas to Japan, so they had convert their money into Japanese yen. Their checking account now has 6,359 yen and their savings account now has 3,485 yen. How many yen do they have?", "iIndex": 238, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.897}, {"lEquations": ["93-15=x"], "lSolutions": [78.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> had 93 cookies. <PERSON><PERSON> ate 15 of them. How many cookies did <PERSON><PERSON> have left?", "iIndex": 448, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.97}, {"lEquations": ["4*8=x"], "lSolutions": [32.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> is selling 4 gumballs for eight cents each. How much money can <PERSON> get from selling the gumballs?", "iIndex": 186, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.899}, {"lEquations": ["10+38=x"], "lSolutions": [48.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 10 books. <PERSON> has 38 books.  How many books do they have together ?", "iIndex": 42, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.851}, {"lEquations": ["34+49=x"], "lSolutions": [83.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 34 dogwood trees currently in the park. Park workers will plant  49 more dogwood trees today. How many dogwood trees will the park have when the workers are finished ?", "iIndex": 75, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 2, "screened": true, "quality_score": 0.964}, {"lEquations": ["0.875 - 0.75 = x"], "lSolutions": [0.125], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> sprinted 0.875 of a lap and then took a break by jogging 0.75 of a lap. How much farther did <PERSON><PERSON> sprint than jog?", "iIndex": 292, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.918}, {"lEquations": ["9-2=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 9 oak trees currently in the park. Park workers had to cut down 2  oak trees that were damaged. How many oak trees will the park have when the workers are finished ?", "iIndex": 189, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.891}, {"lEquations": ["14.02+9.46+12.04=x"], "lSolutions": [35.52], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> purchased a football game for $14.02, a strategy game for $9.46, and a Batman game for $12.04. How much did <PERSON> spend on video games?", "iIndex": 224, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.92}, {"lEquations": ["10+24+33=x"], "lSolutions": [67.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 10 books, <PERSON> has 24 books, and <PERSON> has  33 books. How many books do they have together ?", "iIndex": 48, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.928}, {"lEquations": ["0.2 + 0.4 = x"], "lSolutions": [0.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Each day, the polar bear at Richmond's zoo eats 0.2 of a bucket of trout and 0.4 of a bucket of salmon. How many buckets of fish does the polar bear eat daily?", "iIndex": 304, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.913}, {"lEquations": ["19.833333333333332 - 9.166666666666666 = x"], "lSolutions": [10.666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> ran 19.833333333333332 miles and walked 9.166666666666666 miles. How much farther did <PERSON> run than walk?", "iIndex": 366, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.867}, {"lEquations": ["0.25 + 0.4166666666666667 + 0.25 = x"], "lSolutions": [0.9166666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "During a school play, <PERSON> staffed the snack bar. He served 0.25 of a pitcher of lemonade during the first intermission, 0.4166666666666667 of a pitcher during the second, and 0.25 of a pitcher during the third. How many pitchers of lemonade did <PERSON> pour in all?", "iIndex": 343, "complexity_level": "L1", "dir_score": 0.35, "reasoning_steps": 4, "screened": true, "quality_score": 0.872}, {"lEquations": ["6+3=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 6 carrots. <PERSON> grew 3 carrots. How many carrots did they grow in total ?", "iIndex": 25, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.952}, {"lEquations": ["0.16666666666666666 + 0.5 = x"], "lSolutions": [0.6666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> is learning to drive, so this weekend she practiced driving 0.16666666666666666 of a mile with her mother and another 0.5 of a mile with her father. How far did <PERSON> drive in all?", "iIndex": 288, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 3, "screened": true, "quality_score": 0.921}, {"lEquations": ["3.6666666666666665 - 2.3333333333333335 = x"], "lSolutions": [1.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "At the beach, <PERSON> and her sister both built sandcastles and then measured their heights. <PERSON>'s sandcastle was 3.6666666666666665 feet tall and her sister's was 2.3333333333333335 feet tall. How much taller was <PERSON>'s sandcastle than her sister's?", "iIndex": 359, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.943}, {"lEquations": ["30*4=x"], "lSolutions": [120.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> measured the distance from her desk to the water fountain. It was 30 feet. How many feet will Mrs. <PERSON> walk on her trips to the fountain if she goes to the water fountain 4 times today?", "iIndex": 488, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.905}, {"lEquations": ["8*7=x"], "lSolutions": [56.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant sold 8 pies every day for a week. How many pies were sold during the week ?", "iIndex": 159, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.867}, {"lEquations": ["27+41+20=x"], "lSolutions": [88.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sally had 27 cards. <PERSON> gave her 41 new cards. Sally bought 20 cards. How many cards does Sally have now ?", "iIndex": 53, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.911}, {"lEquations": ["0.125 + 0.125 = x"], "lSolutions": [0.25], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> owns two dogs. Each day, one dog eats 0.125 of a scoop of dog food and the other dog eats 0.125 of a scoop. Together, how much dog food do the two dogs eat each day?", "iIndex": 295, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.914}, {"lEquations": ["29+16+20=x"], "lSolutions": [65.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 29 cantelopes, <PERSON> grew 16 cantelopes, and <PERSON> grew  20 cantelopes. How many cantelopes did they grow in total ?", "iIndex": 50, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.954}, {"lEquations": ["47-25=x"], "lSolutions": [22.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 47 seashells on the beach, he gave <PERSON> 25 of the seashells. How many seashells does he now have ?", "iIndex": 200, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.891}, {"lEquations": ["126-81=x"], "lSolutions": [45.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A DVD book holds 126 DVDs. There are 81 DVDs already in the book. How many more DVDs can be put in the book?", "iIndex": 446, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.883}, {"lEquations": ["79-63=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 79 seashells on the beach, she gave <PERSON> 63 of the seashells. How many seashells does she now have ?", "iIndex": 88, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.882}, {"lEquations": ["9+5+2=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 blue balloons, <PERSON> has 5 blue balloons, and <PERSON> has 2 blue balloons. How many blue balloons do they have in total ?", "iIndex": 29, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 4, "screened": true, "quality_score": 0.851}, {"lEquations": ["18/2=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> goes fishing with <PERSON>. They catch 18 trout.  If they equally split up the trout, how many will each one get ?", "iIndex": 100, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.925}, {"lEquations": ["8.75 - 6 = x"], "lSolutions": [2.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A farmer started the day with 8.75 buckets of seeds. After spending the morning sowing seeds, she now has 6 buckets left. How many buckets of seeds did the farmer sow?", "iIndex": 362, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.968}, {"lEquations": ["88/8=x"], "lSolutions": [11.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 88 pink flower stones and wanted to make 8 bracelets out of these stones. How many pink flower stones will each bracelet have if she used the same number of stones in each bracelet?", "iIndex": 424, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.893}, {"lEquations": ["375=(7*x)+4"], "lSolutions": [53.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Monday, 375 students went on a trip to the zoo. All 7 buses were filled and 4 students had to travel in cars. How many students were in each bus ?", "iIndex": 14, "complexity_level": "L3", "dir_score": 1.24, "reasoning_steps": 8, "screened": true, "quality_score": 0.968}, {"lEquations": ["0.16666666666666666 + 0.4166666666666667 + 0.08333333333333333 = x"], "lSolutions": [0.6666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s science class recorded the rainfall each day. They recorded 0.16666666666666666 of a centimeter of rain on Monday, 0.4166666666666667 of a centimeter of rain on Tuesday, and 0.08333333333333333 of a centimeter of rain on Wednesday. How many centimeters of rain did the class record in all?", "iIndex": 336, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 2, "screened": true, "quality_score": 0.966}, {"lEquations": ["(9*10)-(7*5)=x"], "lSolutions": [55.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 9 packages of cat food and 7 packages of dog food. Each package of cat food contained 10 cans, and each package of dog food contained 5 cans. How many more cans of cat food than dog food did <PERSON> buy?", "iIndex": 277, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.975}, {"lEquations": ["(6*6)+(8*18)=x"], "lSolutions": [180.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A group of science students went on a field trip. They took 6 vans and 8 buses. There were 6 people in each van and 18 people in each bus. How many people went on the field trip?", "iIndex": 260, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.87}, {"lEquations": ["48/4=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mr. <PERSON><PERSON><PERSON> bought 48 doughnuts packed equally into 4 boxes. How many doughnuts were in each box?", "iIndex": 460, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 3, "screened": true, "quality_score": 0.89}, {"lEquations": ["11*25=x"], "lSolutions": [275.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has saved 11 quarters from washing cars.  How many cents does <PERSON> have ?", "iIndex": 170, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.92}, {"lEquations": ["x=84.0/12.0"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A 12 ounce can of cranberry juice sells for 84 cents. What is the cost in cents per ounce.", "iIndex": 505, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.961}, {"lEquations": ["4*12=x"], "lSolutions": [48.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sally bought 4 dozen eggs from the grocery store to bake some cakes.  How many eggs did <PERSON> buy ?", "iIndex": 146, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.879}, {"lEquations": ["0.25 + 0.25 = x"], "lSolutions": [0.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> added 0.25 of a cup of walnuts to a batch of trail mix. Later, she added 0.25 of a cup of almonds. How many cups of nuts did <PERSON> put in the trail mix in all?", "iIndex": 287, "complexity_level": "L1", "dir_score": 0.39, "reasoning_steps": 2, "screened": true, "quality_score": 0.918}, {"lEquations": ["0.08333333333333333 + 0.75 + 0.08333333333333333 = x"], "lSolutions": [0.9166666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A waitress put leftover tarts into the fridge on Thursday night. She noticed that the restaurant had 0.08333333333333333 of a tart filled with cherries, 0.75 of a tart filled with blueberries, and 0.08333333333333333 of a tart filled with peaches. How many leftover tarts did the restaurant have in all?", "iIndex": 329, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.867}, {"lEquations": ["3884+2871=x"], "lSolutions": [6755.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A car company produced 3,884 cars in North America and 2,871 cars in Europe. How many cars is that in all?", "iIndex": 237, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.9}, {"lEquations": ["8*x=56"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sunday morning was spent for making wood carvings which can be sold as souvenir for tourists. They were placed in shelves that can contain 8 wood carvings at a time. If 56 wood carvings were displayed, how many shelves were filled with carvings?", "iIndex": 415, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.855}, {"lEquations": ["33-26=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> decided to sell all of her old books. She gathered up 33 books to sell. She sold 26 books in a yard sale. How many books does <PERSON> now have ?", "iIndex": 202, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.961}, {"lEquations": ["1.25 + 5.25 = x"], "lSolutions": [6.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "As part of a lesson on earthquakes, a science class is researching the movement of a nearby fault line. The fault line moved 1.25 inches during the past year and 5.25 inches the year before. How far did the fault line move in all?", "iIndex": 357, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.883}, {"lEquations": ["5*7=x"], "lSolutions": [35.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> was at the beach for 5 days and found 7 seashells every day. How many seashells did <PERSON> find during the beach trip ?", "iIndex": 157, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.944}, {"lEquations": ["21+49=x"], "lSolutions": [70.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 21 quarters in her bank. Her dad gave her 49 more quarters. How many quarters does she have now ?", "iIndex": 40, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 2, "screened": true, "quality_score": 0.936}, {"lEquations": ["98-93=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 98 pennies in his bank. He spent 93 of his pennies. How many pennies does he have now ?", "iIndex": 87, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.941}, {"lEquations": ["5*2=x"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> goes fishing with <PERSON>. <PERSON> catches 5 trout. <PERSON> catches 2 times as many trout as <PERSON>. How many trout did <PERSON> catch ?", "iIndex": 180, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.9}, {"lEquations": ["46*x=276"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> earns $46 cleaning a home. How many homes did she clean, if she made 276 dollars?", "iIndex": 111, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.97}, {"lEquations": ["10.2 + 8.6 = x"], "lSolutions": [18.8], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Hoping to be named Salesperson of the Month, <PERSON> called the names from 10.2 pages of the phone book last week. This week, she called the people listed on another 8.6 pages of the same phone book. How many pages worth of people did <PERSON> call in all?", "iIndex": 358, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.85}, {"lEquations": ["(2*10.62)*****+13.59=x"], "lSolutions": [36.78], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Saturday, <PERSON> spent $10.62 each on 2 tickets to a movie theater. <PERSON> also rented a movie for $1.59, and bought a movie for $13.95. How much money in total did <PERSON> spend on movies?", "iIndex": 255, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.87}, {"lEquations": ["21-(4+8)=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> needs 21 cartons of berries to make a berry cobbler. She already has 4 cartons of strawberries and 8 cartons of blueberries. How many more cartons of berries should <PERSON> buy?", "iIndex": 272, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.907}, {"lEquations": ["x=77.75-9.5"], "lSolutions": [68.25], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The tallest player on the basketball team is 77.75 inches tall. This is 9.5 inches taller than the shortest player. How tall is the shortest player , in inches?", "iIndex": 502, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.936}, {"lEquations": ["67-x=33"], "lSolutions": [34.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> received 67 dollars for his birthday. He went to a sporting goods store and bought a baseball glove, baseball, and bat. He had 33 dollars left over. How much did he spent on the baseball gear ?", "iIndex": 61, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.931}, {"lEquations": ["0.25 - 0.16 = x"], "lSolutions": [0.09], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 0.25 grams of pepper. Then he used 0.16 grams of the pepper to make some scrambled eggs. How much pepper does <PERSON> have left?", "iIndex": 377, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.903}, {"lEquations": ["10=5*x"], "lSolutions": [2.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 10 soccer games in the season. The season is played for 5 months. How many soccer games were played each month, if each month has the same number of games?", "iIndex": 154, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.944}, {"lEquations": ["2479+6085=x"], "lSolutions": [8564.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> is a beekeeper. Last year, she harvested 2,479 pounds of honey. This year, she bought some new hives and increased her honey harvest by 6,085 pounds. How many pounds of honey did <PERSON> harvest this year?", "iIndex": 235, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.954}, {"lEquations": ["24/3=x"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, and <PERSON> have 24 crayons all together. If the crayons are equally divided, how many will each person get ?", "iIndex": 179, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.937}, {"lEquations": ["14+29=x"], "lSolutions": [43.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sam went to 14 football games this year. He went to 29 games  last year. How many football games did <PERSON> go to in all ?", "iIndex": 45, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.855}, {"lEquations": ["6+9=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 6 turnips. <PERSON><PERSON> grew 9 turnips. How many turnips did they grow in all ?", "iIndex": 136, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.853}, {"lEquations": ["12170+54912=x"], "lSolutions": [67082.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Some insects called aphids attack a large farm. In response, the farmer releases ladybugs onto the fields. There are 12,170 ladybugs with spots and 54,912 ladybugs without spots. How many ladybugs are there in all?", "iIndex": 245, "complexity_level": "L1", "dir_score": 0.36, "reasoning_steps": 3, "screened": true, "quality_score": 0.853}, {"lEquations": ["34*4=x"], "lSolutions": [134.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "One stamp costs 34 cents. If the cost of each stamp remains the same, how much would 4 stamps cost?", "iIndex": 462, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 3, "screened": true, "quality_score": 0.885}, {"lEquations": ["0.8 - 0.2 = x"], "lSolutions": [0.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> filled a bucket with 0.8 of a gallon of water. Later, he poured out 0.2 of a gallon of the water. How much water is left in the bucket?", "iIndex": 296, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 2, "screened": true, "quality_score": 0.955}, {"lEquations": ["6*378=x"], "lSolutions": [2268.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The next act involved several jugglers. If each juggler is juggling 6 balls at a time, how many balls are needed if there are 378 jugglers putting a show at the same time?", "iIndex": 402, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.887}, {"lEquations": ["0.2*x-4.0=6.0"], "lSolutions": [50.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "0.20 of a number decreased by 4 is equal to 6. Find the number.", "iIndex": 503, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.959}, {"lEquations": ["64-14=x"], "lSolutions": [50.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 64 violet marbles, he gave <PERSON> 14 of the marbles. How many violet marbles does he now have ?", "iIndex": 83, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.935}, {"lEquations": ["3.42 - 2.2 = x"], "lSolutions": [1.22], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 3.42 pounds of fruit for a class party. The class ate 2.2 pounds of the fruit. How much fruit is left?", "iIndex": 381, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 2, "screened": true, "quality_score": 0.901}, {"lEquations": ["0.16666666666666666 + 0.6666666666666666 = x"], "lSolutions": [0.8333333333333334], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There is 0.16666666666666666 of a cup of oil in <PERSON>'s measuring cup. If <PERSON> adds 0.6666666666666666 of a cup more, how much oil will be in the measuring cup?", "iIndex": 300, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.889}, {"lEquations": ["(4*0.5)+(14*0.5)=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Wednesday, <PERSON> spent 4 half-dollars playing pinball. The next day, she spent 14 half-dollars on pinball. What was the total amount <PERSON> spent playing pinball?", "iIndex": 209, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.906}, {"lEquations": ["420/7=x"], "lSolutions": [60.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The junior ranger asked <PERSON> to help him place 420 seedlings in packets. If every packet needs to contain 7 seeds, how many packets do they need?", "iIndex": 411, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.927}, {"lEquations": ["13.99+12.14+7.43=x"], "lSolutions": [33.56], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to the mall to buy clothes. She spent $13.99 on shorts, $12.14 on a shirt, and $7.43 on a jacket. How much money did <PERSON> spend on clothes?", "iIndex": 222, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.869}, {"lEquations": ["261*23=x"], "lSolutions": [6003.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 261 fishbowls. Each fishbowl has 23 fish. How many fish are there?", "iIndex": 389, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.942}, {"lEquations": ["0.**************** + 0.**************** + 0.08333333333333333 = x"], "lSolutions": [0.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Paco's Countertop Company purchased pieces of marble from a quarry. The weights of the pieces they purchased were 0.**************** of a ton, 0.**************** of a ton, and 0.08333333333333333 of a ton. How many tons of marble did Paco's Countertop Company purchase in all?", "iIndex": 334, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.953}, {"lEquations": ["6 + 3.12 = x"], "lSolutions": [9.12], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> weighed two colored metal balls during a science class. The blue ball weighed 6 pounds and the brown ball weighed 3.12 pounds. If <PERSON> places both balls on the scale at the same time, what will the scale read?", "iIndex": 373, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.957}, {"lEquations": ["96=x*12"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> saw 96 birds in a tree. How many dozen birds did <PERSON> see?", "iIndex": 117, "complexity_level": "L1", "dir_score": 0.39, "reasoning_steps": 3, "screened": true, "quality_score": 0.957}, {"lEquations": ["(4*4.45)+6.06=x"], "lSolutions": [23.86], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> loves trading cards. She bought 4 packs of Digimon cards for $4.45 each, and a deck of baseball cards for $6.06. How much did <PERSON> spend on cards?", "iIndex": 216, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.965}, {"lEquations": ["6*25=x"], "lSolutions": [150.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has saved 6 quarters from washing cars.  How many cents does <PERSON> have ?", "iIndex": 140, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.941}, {"lEquations": ["49952+918=x"], "lSolutions": [50870.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A ship full of grain crashes into a coral reef. By the time the ship is fixed, 49,952 tons of grain have spilled into the water. Only 918 tons of grain remain onboard. How many tons of grain did the ship originally contain?", "iIndex": 247, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.903}, {"lEquations": ["523+x=750"], "lSolutions": [218.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "532 people are watching a movie in a theater. The theater has 750 seats. How many seats are empty in the theater?", "iIndex": 495, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.853}, {"lEquations": ["0.2 + 0.1 = x"], "lSolutions": [0.3], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "While making pastries, a bakery used 0.2 of a bag of wheat flour and 0.1 of a bag of white flour. How many bags of flour did the bakery use in all?", "iIndex": 324, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.966}, {"lEquations": ["212+x=280"], "lSolutions": [68.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 212 fish. How many more fish does <PERSON> need to buy to have 280 fish?", "iIndex": 490, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.879}, {"lEquations": ["24/3=x"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s dad brought home 24 marble potatoes. If <PERSON>’s mom made potato salad for lunch and served an equal amount of potatoes to <PERSON>, herself and her husband, how many potatoes did each of them have?", "iIndex": 432, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 2, "screened": true, "quality_score": 0.978}, {"lEquations": ["3*(1134+1475)=x"], "lSolutions": [7827.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In one week, an airplane pilot flew 1134 miles on Tuesday and 1475 miles on Thursday. If the pilot flies the same number of miles 3 weeks in a row, how many miles does the pilot fly in all?", "iIndex": 470, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.892}, {"lEquations": ["0.16666666666666666 + 0.16666666666666666 + 0.**************** = x"], "lSolutions": [0.6666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "During a visit to an orchard, <PERSON> picked 0.16666666666666666 of a bag of Golden Delicious apples, 0.16666666666666666 of a bag of Macintosh apples, and 0.**************** of a bag of Cortland apples. How many bags of fruit did <PERSON> pick in total?", "iIndex": 327, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.897}, {"lEquations": ["20817+97741=x"], "lSolutions": [118558.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were originally 20,817 houses in Lincoln County. During a housing boom, developers built 97,741 more. How many houses are there now in Lincoln County?", "iIndex": 240, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.937}, {"lEquations": ["28+x=54"], "lSolutions": [26.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were 28 bales of hay in the barn. <PERSON> stacked more bales in the barn today. There are now 54 bales of hay in the barn. How many bales did he store in the barn ?", "iIndex": 1, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 2, "screened": true, "quality_score": 0.94}, {"lEquations": ["6*5=x"], "lSolutions": [30.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 6 blue marbles. <PERSON> has 5 times more blue marbles than <PERSON>. How many blue marbles does <PERSON> have ?", "iIndex": 149, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.889}, {"lEquations": ["5*x=30"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s mother prepared lemonade. Every pitcher of lemonade can serve 5 glasses. If she was able to serve 30 glasses of lemonade, how many pitchers of lemonade did she prepare?", "iIndex": 412, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.978}, {"lEquations": ["0.5 + 0.1 + 0.1 = x"], "lSolutions": [0.7], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A spaceship traveled 0.5 of a light-year from Earth to Planet X and 0.1 of a light-year from Planet X to Planet Y. Then it traveled 0.1 of a light-year from Planet Y back to Earth. How many light-years did the spaceship travel in all?", "iIndex": 340, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.953}, {"lEquations": ["0.6 - 0.4 = x"], "lSolutions": [0.2], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Jenny ran 0.6 of a mile and walked 0.4 of a mile. How much farther did <PERSON> run than walk?", "iIndex": 305, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.864}, {"lEquations": ["0.36 - 0.05 = x"], "lSolutions": [0.31], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A bee colony produced 0.36 pounds of honey, but bears ate 0.05 pounds of it. How much honey remains?", "iIndex": 374, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.971}, {"lEquations": ["64535+522=x"], "lSolutions": [65057.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A dust storm sweeps across the prairie. It covers 64,535 acres of the prairie in dust, but leaves 522 acres untouched. How many acres does the prairie cover?", "iIndex": 244, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.887}, {"lEquations": ["22+20=x"], "lSolutions": [42.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 22 books. <PERSON> has 20 books.  How many books do they have together ?", "iIndex": 79, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.889}, {"lEquations": ["49+25=x"], "lSolutions": [74.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 49 quarters in his bank. His dad gave him 25 more quarters. How many quarters does he have now ?", "iIndex": 38, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 3, "screened": true, "quality_score": 0.972}, {"lEquations": ["5*28=x"], "lSolutions": [140.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The first act included 5 clown mobiles, each stuffed with 28 clowns. How many clowns are inside all the clown mobiles combined?", "iIndex": 401, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 2, "screened": true, "quality_score": 0.853}, {"lEquations": ["83*6=x"], "lSolutions": [498.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> then went to see the oranges being harvested. <PERSON> found out that they harvest 83 sacks per day. How many sacks of oranges will they have after 6 days of harvest?", "iIndex": 397, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.926}, {"lEquations": ["139+113=x"], "lSolutions": [252.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 139 turnips. <PERSON> grew 113 turnips. How many turnips did they grow in all ?", "iIndex": 94, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.852}, {"lEquations": ["11.166666666666666 - 0.8333333333333334 = x"], "lSolutions": [10.333333333333334], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Professor <PERSON> weighed two pieces of metal for an experiment. The piece of iron weighed 11.166666666666666 pounds and the piece of aluminum weighed 0.8333333333333334 of a pound. How much more did the piece of iron weigh than the piece of aluminum?", "iIndex": 356, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.973}, {"lEquations": ["(8*12)*5=x"], "lSolutions": [480.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 8 dozen books. <PERSON> has 5 times more books than  <PERSON>. How many books does <PERSON> have ?", "iIndex": 175, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.892}, {"lEquations": ["3.8333333333333335 - 0.16666666666666666 = x"], "lSolutions": [3.6666666666666665], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "This afternoon <PERSON> left school, rode the bus 3.8333333333333335 miles, and then walked 0.16666666666666666 of a mile to get home. How much farther did <PERSON> ride than walk?", "iIndex": 349, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.98}, {"lEquations": ["3*5=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> worked 3 hours, each day, for 5 days. How many hours did he work in total ?", "iIndex": 184, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.928}, {"lEquations": ["6=4+x"], "lSolutions": [2.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 6 soccer games this year. <PERSON> missed 4 of the games. How many soccer games did <PERSON> go to in all ?", "iIndex": 188, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 3, "screened": true, "quality_score": 0.916}, {"lEquations": ["7+3+2=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 7 dogwood trees currently in the park. Park workers will plant 3 more dogwood trees today and 2 more dogwood trees tomorrow. How many dogwood trees will the park have when the workers are finished ?", "iIndex": 33, "complexity_level": "L1", "dir_score": 0.45, "reasoning_steps": 2, "screened": true, "quality_score": 0.915}, {"lEquations": ["109-((2*11)+13)=x"], "lSolutions": [74.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s aunt gave her $109 to spend on clothes at the mall. She bought 2 shirts that cost $11 each and a pair of pants that cost $13. How much money does <PERSON> have left to buy more clothes?", "iIndex": 274, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.892}, {"lEquations": ["65899+119=x"], "lSolutions": [66018.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> went to a concert. A total of 65899 people attended the concert. The next week, Mrs. <PERSON><PERSON> went to a second concert, which had 119 more people in attendance. How many people were at the second concert?", "iIndex": 478, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.866}, {"lEquations": ["15*5=x"], "lSolutions": [75.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 15 dogs. Each dog had 5 puppies. How many puppies does <PERSON> now have?", "iIndex": 472, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.89}, {"lEquations": ["0.2 + 0.1 + 0.2 = x"], "lSolutions": [0.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> made smoothies in the blender. She used 0.2 of a cup of strawberries, 0.1 of a cup of yogurt, and 0.2 of a cup of orange juice. How many cups of ingredients did <PERSON> use for the smoothies?", "iIndex": 342, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.954}, {"lEquations": ["49/7=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant sold 49 hamburgers last week. How many hamburgers on average were sold each day ?", "iIndex": 182, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.881}, {"lEquations": ["45+11=x"], "lSolutions": [56.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 45 pears and <PERSON> picked 11 pears from the pear tree. How many pears were picked in total ?", "iIndex": 36, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.962}, {"lEquations": ["0.4 - 0.2 = x"], "lSolutions": [0.2], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> ran 0.4 of a mile and walked 0.2 of a mile. How much farther did <PERSON> run than walk?", "iIndex": 302, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 2, "screened": true, "quality_score": 0.973}, {"lEquations": ["36+85=x"], "lSolutions": [121.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had a terrible earache yesterday. When I peered into his ears yesterday, I found 36 frisky fleas having a party in his right ear and 85 baby fleas sleeping peacefully in his left ear. I cleaned out <PERSON>'s ears. How many fleas perished?", "iIndex": 492, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 3, "screened": true, "quality_score": 0.864}, {"lEquations": ["96/12=x"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 96 muffins, which he needs to box up into dozens. How many boxes does he need?", "iIndex": 107, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.91}, {"lEquations": ["53+x=64"], "lSolutions": [11.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 53 maple trees currently in the park. Park workers will plant more maple trees today. When the workers are finished there will be 64 maple trees in the park. How many maple trees did the workers plant today ?", "iIndex": 58, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.94}, {"lEquations": ["40-22=x"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 40 baseball cards. <PERSON> bought 22 of <PERSON>'s  baseball cards. How many baseball cards does <PERSON> have now ?", "iIndex": 199, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.886}, {"lEquations": ["54+22=x"], "lSolutions": [76.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 54 scissors in the drawer. <PERSON> placed 22 more  scissors in the drawer. How many scissors are now there in all ?", "iIndex": 85, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.925}, {"lEquations": ["72=x*12"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 72 eggs from the store to bake some cakes.  How many dozen eggs did <PERSON> buy?", "iIndex": 120, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 2, "screened": true, "quality_score": 0.873}, {"lEquations": ["9-4=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 9 limes and gave <PERSON> 4 of the limes.  How many limes does <PERSON> have now ?", "iIndex": 193, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.937}, {"lEquations": ["90/10=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 90 cents in his bank.  How many dimes does <PERSON> have ?", "iIndex": 104, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.941}, {"lEquations": ["1426+x=2000"], "lSolutions": [574.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Before the recent housing boom, there were 1,426 houses in Lawrence County. Now, there are 2,000 houses. How many houses did developers build during the housing boom?", "iIndex": 252, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.88}, {"lEquations": ["6*34=x"], "lSolutions": [204.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "6 students were sitting at each table in the lunchroom. There are 34 tables. How many students were sitting in the lunchroom?", "iIndex": 471, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.934}, {"lEquations": ["48/6=x"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "When relatives visit <PERSON> and her family, she and her cousins do origami. If she has 48 origami papers to give away to her 6 cousins, how many will each receive if she gives everyone the same number of origami papers?", "iIndex": 421, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.853}, {"lEquations": ["73+x=96"], "lSolutions": [23.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were 73 bales of hay in the barn. <PERSON> stacked more bales in the barn today. There are now 96 bales of hay in the barn. How many bales did he store in the barn ?", "iIndex": 64, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.94}, {"lEquations": ["0.625 - 0.5 = x"], "lSolutions": [0.125], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s bus ride to school is 0.625 of a mile and <PERSON>'s bus ride is 0.5 of a mile. How much longer is <PERSON>'s bus ride than <PERSON>'s?", "iIndex": 309, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.863}, {"lEquations": ["0.25 + 0.375 = x"], "lSolutions": [0.625], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> made a fruit salad with 0.25 of a pound of melon and 0.375 of a pound of berries. How many pounds of fruit did <PERSON> use in all?", "iIndex": 308, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.88}, {"lEquations": ["7-3=x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 7 crayons in the drawer. <PERSON> took 3 crayons out of the drawer. How many crayons are there now ?", "iIndex": 192, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.937}, {"lEquations": ["7=4+x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 7 seashells but 4 were broken.  How many unbroken seashells did <PERSON> find ?", "iIndex": 69, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.941}, {"lEquations": ["8-3=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 8 potatoes in the garden. The rabbits ate 3 of the potatoes. How many potatoes does <PERSON> now have ?", "iIndex": 187, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.948}, {"lEquations": ["37*17=x"], "lSolutions": [629.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 37 baskets. There are 17 apples in each basket. How many apples are there in all?", "iIndex": 392, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 3, "screened": true, "quality_score": 0.972}, {"lEquations": ["27+45=x"], "lSolutions": [72.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 27 pencils in the drawer. <PERSON> placed 45 more  pencils in the drawer. How many pencils are now there in total ?", "iIndex": 76, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.89}, {"lEquations": ["217+109=x"], "lSolutions": [326.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 217 markers. <PERSON> gave her 109 more markers. How many markers does <PERSON> have altogether?", "iIndex": 445, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.962}, {"lEquations": ["2000/100=x"], "lSolutions": [20.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has saved 2000 cents from selling lemonade.  How many dollars does <PERSON> have?", "iIndex": 108, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.882}, {"lEquations": ["0.25 + 0.25 + 0.375 = x"], "lSolutions": [0.875], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Before starting her shift, a waitress checks to make sure there is enough mustard for her customers. She finds 0.25 of a bottle at the first table, 0.25 of a bottle at the second table, and 0.375 of a bottle at the third table. <PERSON><PERSON><PERSON>, how many bottles of mustard does the waitress find?", "iIndex": 328, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.98}, {"lEquations": ["0.625 - 0.25 = x"], "lSolutions": [0.375], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "While making desserts for a bake sale, <PERSON> used 0.625 of a scoop of brown sugar as well as 0.25 of a scoop of white sugar. How much more brown sugar did <PERSON> use?", "iIndex": 319, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.945}, {"lEquations": ["96/8=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 96 cupcakes for 8 children to share. How much will each person get if they share the cupcakes equally?", "iIndex": 438, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.885}, {"lEquations": ["45/5=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 45 dollars in 5 dollar bills.  How many five dollars bills does  he have ?", "iIndex": 99, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.97}, {"lEquations": ["46*(4*12)=x"], "lSolutions": [2208.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 46 children in the classroom, each student will get 4 dozen pencils. How many pencils will the teacher have to give out ?", "iIndex": 171, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.961}, {"lEquations": ["56/8=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s mom picked cherry tomatoes from their backyard. If she gathered 56 cherry tomatoes and is about to place them in small jars which can contain 8 cherry tomatoes at a time, how many jars will she need?", "iIndex": 430, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.949}, {"lEquations": ["74=35+18+3*x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> spent 74 cents at the school store. She bought a notebook for 35 cents, a ruler for 18 cents, and 3 pencils. What is the cost of one pencil?", "iIndex": 481, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.96}, {"lEquations": ["6+8=x"], "lSolutions": [14.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 6 seashells and <PERSON> found 8 seashells on the beach. How many seashells did they find together ?", "iIndex": 24, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.932}, {"lEquations": ["34+x=86"], "lSolutions": [52.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 34 peaches left at his roadside fruit stand. He went to the orchard and picked more peaches to stock up the stand. There are now 86 peaches at the stand, how many did he pick ?", "iIndex": 6, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.942}, {"lEquations": ["15/3=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> goes to lunch with <PERSON> and <PERSON>. The total bill came to 15 dollars. They decided to equally split up the bill, how much will each person have to pay ?", "iIndex": 181, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.965}, {"lEquations": ["0.125 + 0.125 + 0.5 = x"], "lSolutions": [0.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "During a canned food drive, items were sorted into bins. The drive resulted in 0.125 of a bin of soup, 0.125 of a bin of vegetables, and 0.5 of a bin of pasta. Altogether, how many bins would the canned food take up?", "iIndex": 333, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.937}, {"lEquations": ["5973+8723=x"], "lSolutions": [14696.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A ship is filled with 5,973 tons of cargo. It stops in the Bahamas, where sailors load 8,723 more tons of cargo onboard. How many tons of cargo does the ship hold now?", "iIndex": 233, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.949}, {"lEquations": ["4*246=x"], "lSolutions": [984.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "They entered the circus tent and saw that there are 4 sections for the audience. If each section can accommodate 246 people, how many people can the tent accommodate in total?", "iIndex": 400, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 4, "screened": true, "quality_score": 0.906}, {"lEquations": ["x-9=4"], "lSolutions": [13.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had Pokemon cards. He gave 9 Pokemon cards to his friends. He now has 4 Pokemon cards left. How many Pokemon cards did he have to start with ?", "iIndex": 133, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.91}, {"lEquations": ["37+28+39=x"], "lSolutions": [104.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> has 37 blue balloons, <PERSON> has 28 blue balloons, and <PERSON>  has 39 blue balloons. How many blue balloons do they have in all ?", "iIndex": 52, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.878}, {"lEquations": ["0.25 + 0.375 + 0.125 = x"], "lSolutions": [0.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> made punch for her friend's birthday party. She used 0.25 of a gallon of grape juice, 0.375 of a gallon of cranberry juice, and 0.125 of a gallon of club soda. How many gallons of punch did <PERSON> make?", "iIndex": 339, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.932}, {"lEquations": ["48*8=x*12"], "lSolutions": [32.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 48 books. <PERSON><PERSON> has 8 times more books than  <PERSON>. How many dozen books does <PERSON><PERSON> have?", "iIndex": 121, "complexity_level": "L1", "dir_score": 0.38, "reasoning_steps": 3, "screened": true, "quality_score": 0.879}, {"lEquations": ["107+104=x"], "lSolutions": [211.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 107 walnut trees currently in the park. Park workers will plant  104 more walnut trees today. How many walnut trees will the park have when the workers are finished ?", "iIndex": 96, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.975}, {"lEquations": ["0.08333333333333333 + 0.**************** + 0.4166666666666667 = x"], "lSolutions": [0.8333333333333334], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s science class weighed plastic rings for an experiment. They found that the orange ring weighed 0.08333333333333333 of an ounce, the purple ring weighed 0.**************** of an ounce, and the white ring weighed 0.4166666666666667 of an ounce. What was the total weight of the plastic rings?", "iIndex": 346, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.864}, {"lEquations": ["9+3=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 yellow marbles <PERSON> has 3 yellow marbles. How many yellow marbles do they have in all ?", "iIndex": 138, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.85}, {"lEquations": ["0.6666666666666666 - 0.5 = x"], "lSolutions": [0.16666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mandy made an apple pie. She used 0.6666666666666666 of a tablespoon of cinnamon and 0.5 of a tablespoon of nutmeg. How much more cinnamon than nutmeg did Mandy use?", "iIndex": 297, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.904}, {"lEquations": ["122+105=x"], "lSolutions": [227.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 122 oranges and <PERSON> picked 105 oranges from the orange tree. How many oranges were picked in total ?", "iIndex": 91, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.896}, {"lEquations": ["0.5 + 0.**************** + 0.16666666666666666 = x"], "lSolutions": [0.8333333333333334], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> counted the leftover ice cream after a sundae party. She had 0.**************** of a carton of rocky road ice cream, 0.**************** of a carton of cookie dough ice cream, and 0.16666666666666666 of a carton of strawberry cheesecake ice cream. How many cartons of ice cream did <PERSON> have in all?", "iIndex": 332, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.932}, {"lEquations": ["56*9=x"], "lSolutions": [504.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> took a look at his books as well. If <PERSON> has 56 books in each of his 9 bookshelves, how many books does he have in total?", "iIndex": 393, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.934}, {"lEquations": ["61921+49500=x"], "lSolutions": [111421.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "To fill an order, the factory dyed 61,921 yards of silk green and 49,500 yards pink. How many yards of silk did it dye for that order?", "iIndex": 248, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.889}, {"lEquations": ["51+23=x"], "lSolutions": [74.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 51 pumpkins. <PERSON> grew 23 pumpkins. How many pumpkins did they grow in total ?", "iIndex": 81, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.854}, {"lEquations": ["17+(7*x)=80"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Oceanside Bike Rental Shop charges 17 dollars plus 7 dollars an hour for renting a bike. <PERSON> paid 80 dollars to rent a bike. How many hours did he pay to have the bike checked out ?", "iIndex": 13, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.977}, {"lEquations": ["35+x=56"], "lSolutions": [21.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 35 books in his library. He bought several books at a yard sale over the weekend. He now has 56 books in his library. How many books did he buy at the yard sale ?", "iIndex": 56, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.972}, {"lEquations": ["((10+1)+3)*16=x"], "lSolutions": [224.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 10 books about animals, 1 book about outer space, and 3 books about trains. Each book cost $16. How much did <PERSON> spend on the books?", "iIndex": 270, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.952}, {"lEquations": ["0.8 - 0.1 = x"], "lSolutions": [0.7], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> found two worms in the yard and measured them with a ruler. One worm was 0.8 of an inch long. The other worm was 0.1 of an inch long. How much longer was the longer worm?", "iIndex": 307, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.937}, {"lEquations": ["9*12=x"], "lSolutions": [108.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Dan bought 9 dozen eggs from the grocery store to bake some cakes.  How many eggs did <PERSON> buy ?", "iIndex": 173, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 4, "screened": true, "quality_score": 0.896}, {"lEquations": ["3 + 6.8 = x"], "lSolutions": [9.8], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A bucket contains 3 gallons of water. If <PERSON> adds 6.8 gallons more, how many gallons will there be in all?", "iIndex": 354, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.973}, {"lEquations": ["616=147+x"], "lSolutions": [469.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s high school played 616 baseball games this year. He attended  147 games. How many baseball games did <PERSON> miss ?", "iIndex": 93, "complexity_level": "L1", "dir_score": 0.36, "reasoning_steps": 3, "screened": true, "quality_score": 0.884}, {"lEquations": ["3.25 + 0.25 = x"], "lSolutions": [3.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> and his roommates ate 3.25 pints of ice cream on Friday night and 0.25 of a pint of ice cream on Saturday night. How many pints did they eat in all?", "iIndex": 361, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.963}, {"lEquations": ["639-504=x"], "lSolutions": [135.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 639 crayons. <PERSON> has 504 crayons. How many more crayons does <PERSON> have than <PERSON>?", "iIndex": 439, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.87}, {"lEquations": ["0.6 - 0.4 = x"], "lSolutions": [0.2], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> and his classmates placed colored blocks on a scale during a science lab. The yellow block weighed 0.6 pounds and the green block weighed 0.4 pounds. How much more did the yellow block weigh than the green block?", "iIndex": 367, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.903}, {"lEquations": ["27/3=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 27 soccer games during the 3 month season.  If the games are equally divided, how many soccer games are played a month ?", "iIndex": 103, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.913}, {"lEquations": ["12.08+9.85=x"], "lSolutions": [21.93], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> loves eating fruits. <PERSON><PERSON> paid $12.08 for grapes, and $9.85 for cherries. In total, how much money did <PERSON><PERSON> spend?", "iIndex": 220, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.907}, {"lEquations": ["44+52=x"], "lSolutions": [96.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 44 books. <PERSON> has 52 books.  How many books do they have together ?", "iIndex": 82, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.892}, {"lEquations": ["31*11=x"], "lSolutions": [341.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 31 calories in a candy bar. How many calories are there in 11 candy bars ?", "iIndex": 169, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.857}, {"lEquations": ["3.75/3=x"], "lSolutions": [1.25], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> needs to share $3.75 equally among 3 total people. How much money will each person get?", "iIndex": 480, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.921}, {"lEquations": ["2*9=x"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> uses 2 ounces of detergent to wash a pound of clothes. How many ounces of soap will Mrs<PERSON> <PERSON><PERSON> use to wash 9 pounds of clothes?", "iIndex": 477, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.865}, {"lEquations": ["85+48=x"], "lSolutions": [133.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 85 peanuts. Kenya has 48 more than <PERSON>. How many peanuts does Kenya have?", "iIndex": 440, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.855}, {"lEquations": ["x-69=26935"], "lSolutions": [27004.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> just transferred $69 out of her bank account. As a result, the account now has $26,935 left in it. How much money was in the account before the transfer?", "iIndex": 242, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.911}, {"lEquations": ["2+4=x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 2 rose bushes currently in the park. Park workers will plant 4 more rose bushes today. How many rose bushes will the park have when the workers are finished ?", "iIndex": 127, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.863}, {"lEquations": ["6522+5165=x"], "lSolutions": [11687.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "An oil pipe in the sea broke. Before engineers started to fix the pipe, 6,522 liters of oil leaked into the water. While the engineers worked, the pipe leaked 5,165 more liters of oil. In all, how many liters of oil leaked into the water?", "iIndex": 236, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.964}, {"lEquations": ["4*6=x"], "lSolutions": [24.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> each have 6 baseball cards.  How many baseball cards do they have in all ?", "iIndex": 145, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.938}, {"lEquations": ["8*1785=x"], "lSolutions": [14280.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 8 precious stones in his collection which he sold to his friend from the jewelry store. If the stones were sold at 1785 dollar each, how much money did <PERSON> get in total?", "iIndex": 395, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.959}, {"lEquations": ["127-88=x"], "lSolutions": [39.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The farmer had 127 apples. The farmer gave 88 apples to his neighbor. How many apples does the farmer have now?", "iIndex": 473, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.889}, {"lEquations": ["x-3-6=9"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s cat had kittens. He gave 3 to <PERSON> and 6 to <PERSON>. He now has 9 kittens left. How many kittens did he have to start with ?", "iIndex": 28, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.928}, {"lEquations": ["4.4 + 2.86 = x"], "lSolutions": [7.26], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> was 4.4 feet tall. Then she grew 2.86 feet taller. How tall is <PERSON> now?", "iIndex": 385, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.867}, {"lEquations": ["85-(10*5)=x"], "lSolutions": [35.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s father gave him $85. <PERSON> bought 10 books, each of which cost $5. How much money does <PERSON> have left?", "iIndex": 264, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.917}, {"lEquations": ["((11+19)+7)-8=x"], "lSolutions": [29.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> wants to buy a dictionary that costs $11, a dinosaur book that costs $19, and a children's cookbook that costs $7. He has saved $8 from his allowance. How much more money does <PERSON> need to buy all three books?", "iIndex": 256, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.971}, {"lEquations": ["(x/2)+8=12"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> spent half of her allowance going to the movies. She washed the family car and earned 8 dollars. What is her weekly allowance if she ended with 12 dollars ?", "iIndex": 10, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.924}, {"lEquations": ["87-13=x"], "lSolutions": [74.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 87 baseball cards. <PERSON> bought 13 of <PERSON>'s  baseball cards. How many baseball cards does <PERSON> have now ?", "iIndex": 80, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.943}, {"lEquations": ["64=32+x"], "lSolutions": [32.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s high school played 64 soccer games this year. She attended  32 games. How many soccer games did <PERSON> miss ?", "iIndex": 89, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.902}, {"lEquations": ["5.20+4.23=x"], "lSolutions": [9.43], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> purchased a basketball game for $5.20, and a racing game for $4.23. How much did <PERSON> spend on video games?", "iIndex": 213, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 2, "screened": true, "quality_score": 0.942}, {"lEquations": ["0.1111111111111111 + 0.1111111111111111 + 0.6666666666666666 = x"], "lSolutions": [0.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On her vacation last summer, <PERSON><PERSON> walked all over New York City to buy souvenirs. First, she walked 0.1111111111111111 of a mile from her hotel to a postcard shop. Then she walked 0.1111111111111111 of a mile from the postcard shop to a T-shirt shop and 0.6666666666666666 of a mile from the T-shirt shop back to the hotel. How many miles did <PERSON><PERSON> walk in all?", "iIndex": 330, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.965}, {"lEquations": ["5*4=x"], "lSolutions": [20.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Your class is having a pizza party. You buy 5 pizzas. Each pizza has 4 slices. How many slices is that altogether?", "iIndex": 281, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 4, "screened": true, "quality_score": 0.85}, {"lEquations": ["(4+8+4)*10=x"], "lSolutions": [160.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 4 packs of red bouncy balls, 8 packs of yellow bouncy balls, and 4 packs of green bouncy balls. There were 10 bouncy balls in each package. How many bouncy balls did <PERSON> buy in all?", "iIndex": 280, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.937}, {"lEquations": ["9+7=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 9 dimes in his bank. His dad gave him 7 more dimes. How many dimes does <PERSON> have now ?", "iIndex": 20, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.976}, {"lEquations": ["56-x=22"], "lSolutions": [34.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 56 seashells on the beach, he gave <PERSON> some of his seashells. He has 22 seashell left. How many seashells did he give to <PERSON> ?", "iIndex": 59, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.958}, {"lEquations": ["143/11=x"], "lSolutions": [13.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Uncle <PERSON> bought 143 ice cream sandwiches. If he wants to give them to his 11 hungry nieces, how many can each niece get?", "iIndex": 444, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.873}, {"lEquations": ["61-(26+7)=x"], "lSolutions": [28.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> needs 61 paper plates for a birthday party. He already has 26 blue plates and 7 red plates. How many more plates should <PERSON> buy?", "iIndex": 263, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.917}, {"lEquations": ["5.71+6.59=x"], "lSolutions": [12.3], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> bought some toys. She bought a football for $5.71, and spent $6.59 on marbles. In total, how much did <PERSON><PERSON> spend on toys?", "iIndex": 215, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 4, "screened": true, "quality_score": 0.861}, {"lEquations": ["(x/2)+6=13"], "lSolutions": [14.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> sold half of her comic books and then bought 6 more. She now has 13. How many did she begin with ?", "iIndex": 15, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.856}, {"lEquations": ["8*3=x"], "lSolutions": [24.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 8 calories in a candy bar. How many calories are there in 3 candy bars ?", "iIndex": 139, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.879}, {"lEquations": ["30-16=x"], "lSolutions": [14.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 30 violet balloons, he gave <PERSON> 16 of the balloons. How many violet balloons does he now have ?", "iIndex": 198, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.935}, {"lEquations": ["0.16666666666666666 + 1.1666666666666667 = x"], "lSolutions": [1.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Carefully following a recipe, <PERSON> used exactly 0.16666666666666666 of a cup of oil and 1.1666666666666667 cups of water. How many cups of liquid did <PERSON> use in all?", "iIndex": 347, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 4, "screened": true, "quality_score": 0.916}, {"lEquations": ["4+6=x"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 4 walnut trees currently in the park. Park workers will plant 6 more walnut trees today. How many walnut trees will the park have when the workers are finished ?", "iIndex": 19, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.862}, {"lEquations": ["3+x=14"], "lSolutions": [11.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were 3 roses in the vase. <PERSON><PERSON> cut some more roses from her flower garden. There are now 14 roses in the vase. How many roses did she cut ?", "iIndex": 62, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.923}, {"lEquations": ["(2*2.73)+4.01+8.95=x"], "lSolutions": [18.42], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> loves trading cards. He bought 2 packs of football cards for $2.73 each, a pack of Pokemon cards for $4.01, and a deck of baseball cards for $8.95. How much did <PERSON> spend on cards?", "iIndex": 225, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.926}, {"lEquations": ["0.5 - 0.16666666666666666 = x"], "lSolutions": [0.****************], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> began her pizza delivery route with 0.5 of a tank of gas in her car. When she made it back to the pizzeria, 0.16666666666666666 of a tank of gas was left. How much gas did <PERSON> use?", "iIndex": 314, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.965}, {"lEquations": ["(8+13)-(2+18)=x"], "lSolutions": [1.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had $8. Then she saved $13 from her allowance and spent $2 on a comic book and $18 on a puzzle. How much money does <PERSON> have left?", "iIndex": 265, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.897}, {"lEquations": ["74+x=86"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Last week <PERSON> had 74 dollars. He washed cars over the weekend and now has 86 dollars. How much money did he make washing cars ?", "iIndex": 63, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.978}, {"lEquations": ["49/7=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON><PERSON>’s brother, likes to play with blocks. <PERSON><PERSON> repainted <PERSON>’s old blocks in different colors. If <PERSON> has 49 identical blocks and there are 7 blocks for every color of paint used, how many colors did <PERSON><PERSON> use?", "iIndex": 417, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 3, "screened": true, "quality_score": 0.89}, {"lEquations": ["0.7 + 0.6 = x"], "lSolutions": [0.1], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Eve ran 0.7 of a mile and walked 0.6 of a mile. How much farther did <PERSON> run than walk?", "iIndex": 320, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.961}, {"lEquations": ["712261+259378=x"], "lSolutions": [971639.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Each year, salmon travel upstream, going from the ocean to the rivers where they were born. This year, 712,261 male and 259,378 female salmon returned to their rivers. How many salmon made the trip?", "iIndex": 250, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.904}, {"lEquations": ["0.81 - 0.35 = x"], "lSolutions": [0.46], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In March it rained 0.81 inches. It rained 0.35 inches less in April than in March. How much did it rain in April?", "iIndex": 372, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.9}, {"lEquations": ["84*8=x*12"], "lSolutions": [56.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 84 blue marbles. <PERSON> has 8 times more blue marbles than <PERSON>. How many dozen blue marbles does <PERSON> have?", "iIndex": 118, "complexity_level": "L1", "dir_score": 0.39, "reasoning_steps": 3, "screened": true, "quality_score": 0.892}, {"lEquations": ["6+9=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant served 6 cakes during lunch and 9 during dinner today. How many cakes were served today ?", "iIndex": 71, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.959}, {"lEquations": ["12=x+4"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sara's high school played 12 basketball games this year. The team won most of their games. They were defeated during 4 games. How many games did they win ?", "iIndex": 4, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.88}, {"lEquations": ["12=2*x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 12 basketball games in the season. The season is played for 2 months. How many basketball games were played each month, if each month has the same number of games?", "iIndex": 143, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.974}, {"lEquations": ["(45.0*x)+225.0=450.0"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A mechanic charged 45 dollars an hour , plus 225 dollars for the parts. If the total bill was 450 dollars , how many hours did the job take?", "iIndex": 507, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.866}, {"lEquations": ["9.05+4.95+6.52=x"], "lSolutions": [20.52], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought some toys. He bought marbles for $9.05, a football for $4.95, and spent $6.52 on a baseball. In total, how much did <PERSON> spend on toys?", "iIndex": 227, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.897}, {"lEquations": ["15*12=x"], "lSolutions": [180.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to a garage sale to buy chairs. Each chair is 15 dollars. How much did <PERSON> spend for the 12 chairs she bought?", "iIndex": 285, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.931}, {"lEquations": ["0.16666666666666666 + 0.16666666666666666 + 0.08333333333333333 = x"], "lSolutions": [0.4166666666666667], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> made trail mix for a backpacking trip. She used 0.16666666666666666 of a pound of peanuts, 0.16666666666666666 of a pound of chocolate chips, and 0.08333333333333333 of a pound of raisins. How many pounds of trail mix did <PERSON><PERSON> make?", "iIndex": 331, "complexity_level": "L1", "dir_score": 0.44, "reasoning_steps": 3, "screened": true, "quality_score": 0.919}, {"lEquations": ["5-3=x"], "lSolutions": [2.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 5 baseball cards. <PERSON> bought 3 of <PERSON>'s  baseball cards. How many baseball cards does <PERSON> have now ?", "iIndex": 152, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.892}, {"lEquations": ["46+47+12=x"], "lSolutions": [105.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 46 pears, <PERSON> picked 47 pears, and <PERSON> picked  12 pears from the pear tree. How many pears were picked in total ?", "iIndex": 49, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.905}, {"lEquations": ["8.77+10.97+x=20"], "lSolutions": [0.26], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> paid $8.77 on a cat toy, and a cage cost her $10.97 with a $20 bill. How much change did <PERSON> receive?", "iIndex": 230, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.914}, {"lEquations": ["7=3+x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 7 seashells but 3 were broken.  How many unbroken seashells did <PERSON> find ?", "iIndex": 194, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.89}, {"lEquations": ["58/2=x"], "lSolutions": [29.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "58 children are taking a bus to the zoo. They sit 2 children in every seat. How many seats will the children need in all?", "iIndex": 283, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.935}, {"lEquations": ["144*3=x"], "lSolutions": [432.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> saw 144 bees in the hive. The next day she saw 3 times that many. How many bees did she see on the second day?", "iIndex": 487, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.858}, {"lEquations": ["8=2+x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> is baking a cake. The recipe wants 8 cups of flour. She already put in 2 cups. How many more cups does she need to add ?", "iIndex": 3, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.963}, {"lEquations": ["4*122=x"], "lSolutions": [488.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "If books came from all the 4 continents that <PERSON> had been into and he collected 122 books per continent, how many books does he have from all 4 continents combined?", "iIndex": 394, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 2, "screened": true, "quality_score": 0.931}, {"lEquations": ["39+41+20=x"], "lSolutions": [100.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 39 dogwood trees currently in the park. Park workers will plant  41 more dogwood trees today and 20 more dogwood trees tomorrow. How many dogwood trees will the park have when the workers are finished ?", "iIndex": 47, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.861}, {"lEquations": ["(((20+12)+20)-5)-8=x"], "lSolutions": [39.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 20 stickers. He bought 12 stickers from a store in the mall and got 20 stickers for his birthday. Then <PERSON> gave 5 of the stickers to his sister and used 8 to decorate a greeting card. How many stickers does <PERSON> have left?", "iIndex": 266, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.926}, {"lEquations": ["0.6 + 0.2 = x"], "lSolutions": [0.8], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The Montoya family spends 0.6 of their budget on groceries and another 0.2 going out to eat. Altogether, what fraction of their budget does the Mont<PERSON> family spend on food?", "iIndex": 298, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.901}, {"lEquations": ["12.50*8=x"], "lSolutions": [100.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> earns $12.50 an hour cleaning houses. If he works for 8 hours, how much money will he make ?", "iIndex": 150, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.861}, {"lEquations": ["7*2=x"], "lSolutions": [14.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 7 baseball games a month.  The season is played for 2 months. How many baseball games are in a season ?", "iIndex": 185, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.891}, {"lEquations": ["13*12=x"], "lSolutions": [156.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 13 dozen golf balls. How many golf balls does he have ?", "iIndex": 165, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.935}, {"lEquations": ["45*8=x"], "lSolutions": [360.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The farmers reported that they harvest 45 sacks of apples from each of the 8 sections of the orchard daily. How many sacks are harvested every day?", "iIndex": 396, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.968}, {"lEquations": ["45*3=x"], "lSolutions": [135.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "After eating at the restaurant, <PERSON>, <PERSON>, and <PERSON><PERSON> decided to divide the bill evenly. If each person paid 45 dollars, what was the total of the bill ?", "iIndex": 2, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.872}, {"lEquations": ["7341=4221+x"], "lSolutions": [3120.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A worker at a medical lab is studying blood samples. Two samples contained a total of 7,341 blood cells. The first sample contained 4,221 blood cells. How many blood cells were in the second sample?", "iIndex": 253, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.919}, {"lEquations": ["648/4=x"], "lSolutions": [162.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "I have 648 pencils. If I put 4 pencils in each pencil box, how many pencil boxes will I fill?", "iIndex": 443, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.877}, {"lEquations": ["4 + 8.7 = x"], "lSolutions": [12.7], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked two pumpkins. The first pumpkin weighed 4 pounds, and the second pumpkin weighed 8.7 pounds. How much did the two pumpkins weigh all together?", "iIndex": 383, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.969}, {"lEquations": ["0.2 + 0.4 = x"], "lSolutions": [0.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "One evening, a restaurant served a total of 0.2 of a loaf of wheat bread and 0.4 of a loaf of white bread. How many loaves were served in all?", "iIndex": 301, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.857}, {"lEquations": ["8*2=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> goes fishing with <PERSON>. <PERSON> catches 8 trout. <PERSON> catches 2 times as many trout as <PERSON>. How many trout did <PERSON> catch ?", "iIndex": 156, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.966}, {"lEquations": ["0.08333333333333333 + 0.08333333333333333 + 0.6666666666666666 = x"], "lSolutions": [0.8333333333333334], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> did a running drill to get in shape for soccer season. First, <PERSON> ran 0.08333333333333333 of a mile. Then she ran 0.08333333333333333 of a mile and 0.6666666666666666 of a mile more. How many miles did <PERSON> run in total?", "iIndex": 335, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.91}, {"lEquations": ["4.0+(5.0*x)=29.0"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> planted a 4 foot tree. The tree grows at a rate of 5 feet every year. How many years will it take to be 29 feet?", "iIndex": 501, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.963}, {"lEquations": ["5.36+5.10=x"], "lSolutions": [10.46], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> got fast food for lunch. <PERSON> spent $5.36 on a hotdog and $5.10 on a salad. What was the total of the lunch bill?", "iIndex": 218, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.956}, {"lEquations": ["5256+2987=x"], "lSolutions": [8243.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The town of Milburg has 5256 grown-ups and 2987 children. How many people live in Milburg?", "iIndex": 463, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.886}, {"lEquations": ["9=(0.25*12)*x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has saved 9 dollars from washing cars.  How many dozen quarters does <PERSON> have?", "iIndex": 122, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.933}, {"lEquations": ["70-x=27"], "lSolutions": [43.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 70 seashells on the beach. she gave <PERSON> some of her seashells. She has 27 seashell left. How many seashells did she give to <PERSON> ?", "iIndex": 0, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.98}, {"lEquations": ["7.666666666666667 - 3.3333333333333335 = x"], "lSolutions": [4.333333333333333], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> drew a white line that was 7.666666666666667 inches long. Then he drew a blue line that was 3.3333333333333335 inches long. How much longer was the white line than the blue line?", "iIndex": 364, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.91}, {"lEquations": ["709-221=x"], "lSolutions": [488.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 709 green balloons, he gave <PERSON> 221 of the balloons. How many green balloons does he now have ?", "iIndex": 97, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.878}, {"lEquations": ["31=13+x"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON>'s high school played 31 hockey games this year. She attended  13 games. How many hockey games did <PERSON><PERSON> miss ?", "iIndex": 197, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.909}, {"lEquations": ["5*x=35"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In <PERSON>’s class, 5 are boys who love to play marbles. If <PERSON> has 35 marbles, how many will each of the boys receive?", "iIndex": 420, "complexity_level": "L1", "dir_score": 0.42, "reasoning_steps": 2, "screened": true, "quality_score": 0.959}, {"lEquations": ["4*96=x*12"], "lSolutions": [32.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> each have 96 baseball cards.  How many dozen baseball cards do they have in all?", "iIndex": 123, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.977}, {"lEquations": ["3*8=x"], "lSolutions": [24.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> goes out to lunch with <PERSON> and <PERSON>. Each person orders the $8 lunch special. <PERSON> agrees to pay the bill. How much will he have to pay ?", "iIndex": 155, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.864}, {"lEquations": ["7+9=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 7 lemons and <PERSON> picked 9 lemons from the lemon tree. How many lemons were picked in total ?", "iIndex": 70, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.902}, {"lEquations": ["8-2=x"], "lSolutions": [6.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 8 orange balloons  but lost 2 of them. How many orange balloons does <PERSON> have now ?", "iIndex": 72, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.916}, {"lEquations": ["45/5=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> worked 45 hours in the last 5 days. Assuming that she worked the same amount of hours each day, how long did she work each day ?", "iIndex": 102, "complexity_level": "L1", "dir_score": 0.37, "reasoning_steps": 4, "screened": true, "quality_score": 0.932}, {"lEquations": ["46-25=x"], "lSolutions": [21.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 46 rulers in the drawer. <PERSON> took 25  rulers from the drawer. How many rulers are now in the drawer ?", "iIndex": 203, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.968}, {"lEquations": ["2.6666666666666665 + 2.6666666666666665 = x"], "lSolutions": [5.333333333333333], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "At a pizza party, <PERSON> and his friends drank 2.6666666666666665 bottles of lemon-lime soda and 2.6666666666666665 bottles of cola. How much soda did they drink in all?", "iIndex": 355, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.928}, {"lEquations": ["676-224=x"], "lSolutions": [452.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 676 Pokemon cards. <PERSON><PERSON> bought 224 of <PERSON>'s  Pokemon cards. How many Pokemon cards does <PERSON> have now ?", "iIndex": 95, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.88}, {"lEquations": ["5*x=35"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s mother made cookies for 5. If she prepared 35 cookies and each of them had the same number of cookies, how many did each of them have?", "iIndex": 428, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.932}, {"lEquations": ["615/15=x"], "lSolutions": [41.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 615 crayons that came in packs of 15.  How many packs of crayons did <PERSON> buy?", "iIndex": 110, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.916}, {"lEquations": ["0.**************** + 0.**************** = x"], "lSolutions": [0.6666666666666666], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s Vegetarian Restaurant bought 0.**************** of a pound of green peppers and 0.**************** of a pound of red peppers. How many pounds of peppers did <PERSON>'s Vegetarian Restaurant buy in all?", "iIndex": 294, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.97}, {"lEquations": ["(3*x)+6=69"], "lSolutions": [21.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The sum of three consecutive odd numbers is 69. What is the smallest of the three numbers ?", "iIndex": 16, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 1, "screened": true, "quality_score": 0.976}, {"lEquations": ["8.2 + 1.6 = x"], "lSolutions": [9.8], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> hiked 8.2 miles on Saturday. Then, on Sunday, he hiked another 1.6 miles. How far did <PERSON><PERSON> hike all together?", "iIndex": 368, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.976}, {"lEquations": ["42+17=x"], "lSolutions": [59.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> picked 42 pears and <PERSON> picked 17 pears from the pear tree. How many pears were picked in all ?", "iIndex": 86, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.943}, {"lEquations": ["14*6=x"], "lSolutions": [84.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The Ferris wheel in Paradise Park has 14 seats. Each seat can hold 6 people. How many people can ride the Ferris wheel at the same time?", "iIndex": 388, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.873}, {"lEquations": ["110/22=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 110 blue marbles. <PERSON> has 22 times more blue marbles than <PERSON>. How many blue marbles does <PERSON> have?", "iIndex": 105, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.931}, {"lEquations": ["4*17=x"], "lSolutions": [68.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> read 4 books. Each book had 17 chapters in it. How many chapters did Mrs. <PERSON><PERSON> read?", "iIndex": 483, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 4, "screened": true, "quality_score": 0.948}, {"lEquations": ["0.3 + 0.4 = x"], "lSolutions": [0.7], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Jonah added 0.3 of a cup of yellow raisins and 0.4 of a cup of black raisins to a batch of trail mix. How many cups of raisins did <PERSON> add in all?", "iIndex": 321, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.965}, {"lEquations": ["40+41=x"], "lSolutions": [81.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 40 blue balloons <PERSON> has 41 blue balloons. How many blue balloons do they have in total ?", "iIndex": 43, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.858}, {"lEquations": ["9-2=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 blue balloons  but lost 2 of them. How many blue balloons does <PERSON> have now ?", "iIndex": 195, "complexity_level": "L1", "dir_score": 0.39, "reasoning_steps": 2, "screened": true, "quality_score": 0.906}, {"lEquations": ["14.28+4.74=x"], "lSolutions": [19.02], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to the mall on Saturday to buy clothes. He spent $14.28 on shorts and $4.74 on a jacket. In total, how much money did <PERSON> spend on clothing?", "iIndex": 219, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.928}, {"lEquations": ["22*11=x"], "lSolutions": [242.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The Spurs basketball team has 22 players. Each player has 11 basketballs. How many basketballs do they have in all?", "iIndex": 476, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.966}, {"lEquations": ["2*(3*7)=x"], "lSolutions": [42.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Bert runs 2 miles every day. How many miles will <PERSON> run in 3 weeks?", "iIndex": 497, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 2, "screened": true, "quality_score": 0.854}, {"lEquations": ["9*12=x"], "lSolutions": [108.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 dozen golf balls. How many golf balls does she have ?", "iIndex": 132, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 2, "screened": true, "quality_score": 0.866}, {"lEquations": ["5-2=x"], "lSolutions": [3.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> found 5 seashells on the beach. he gave <PERSON> 2 of the seashells. How many seashells does he now have ?", "iIndex": 151, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.861}, {"lEquations": ["7*12=x"], "lSolutions": [84.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 7 dozen eggs from the grocery store to bake some cakes.  How many eggs did <PERSON> buy ?", "iIndex": 167, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.949}, {"lEquations": ["0.41 - 0.33 = x"], "lSolutions": [0.08], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A carpenter bought a piece of wood that was 0.41 meters long. Then she sawed 0.33 meters off the end. How long is the piece of wood now?", "iIndex": 369, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.875}, {"lEquations": ["25+73=x"], "lSolutions": [98.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 25 popular trees currently in the park. Park workers will plant  73 more popular trees today. How many popular trees will the park have when the workers are finished ?", "iIndex": 84, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.97}, {"lEquations": ["21*8=x"], "lSolutions": [168.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "We ordered 21 pizzas. Each pizza has 8 slices. How many slices of pizza are there altogether?", "iIndex": 390, "complexity_level": "L1", "dir_score": 0.39, "reasoning_steps": 2, "screened": true, "quality_score": 0.868}, {"lEquations": ["6+4=x"], "lSolutions": [10.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> grew 6 carrots. <PERSON> grew 4 carrots. How many carrots did they grow in all ?", "iIndex": 148, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.891}, {"lEquations": ["4+9+3=x"], "lSolutions": [16.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 4 plums, <PERSON> picked 9 plums, and <PERSON> picked 3 plums from the plum tree. How many plums were picked in total ?", "iIndex": 32, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.9}, {"lEquations": ["x-7=5"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON>'s dog had puppies. She gave 7 to her friends.  She now has 5 puppies left. How many puppies did she have to start with ?", "iIndex": 21, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 1, "screened": true, "quality_score": 0.902}, {"lEquations": ["9*49=x"], "lSolutions": [441.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has a section filled with short story booklets. If each booklet has 9 pages and there are 49 booklets in the short story section, how many pages will <PERSON> need to go through if he plans to read them all?", "iIndex": 405, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.858}, {"lEquations": ["5.91 + 8.11 = x"], "lSolutions": [14.02], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A construction company bought 5.91 tons of gravel and 8.11 tons of sand. How many tons of material did the company buy in all?", "iIndex": 378, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.95}, {"lEquations": ["10 + 5.1 = x"], "lSolutions": [15.1], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Roadster's Paving Company used 10 tons of cement to pave <PERSON>'s street and 5.1 tons of cement to pave <PERSON>'s street. How much cement did Roadster's Paving Company use in all?", "iIndex": 351, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.857}, {"lEquations": ["4-3=x"], "lSolutions": [1.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sam grew 4 watermelons, but the rabbits ate 3 watermelons. How many watermelons does <PERSON> have left ?", "iIndex": 129, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.976}, {"lEquations": ["1222-513=x"], "lSolutions": [208.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 1222 balloons. <PERSON> has 513 balloons. How many more balloons does <PERSON> have than <PERSON>?", "iIndex": 453, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.854}, {"lEquations": ["7*934=x"], "lSolutions": [6538.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "For the final act, the circus brought out dancing animals wearing crowns. If each crown is made with 7 different colored feathers, how many feathers are needed for 934 crowns?", "iIndex": 403, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.942}, {"lEquations": ["6+5=x"], "lSolutions": [11.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> picked 6 pears and <PERSON> picked 5 pears from the pear tree. How many pears were picked in total ?", "iIndex": 128, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.94}, {"lEquations": ["0.375 + 0.25 = x"], "lSolutions": [0.625], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "At Lindsey's Vacation Wear, 0.375 of the garments are bikinis and 0.25 are trunks. What fraction of the garments are either bikinis or trunks?", "iIndex": 291, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.852}, {"lEquations": ["6*3=x"], "lSolutions": [18.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 6 blue balloons. <PERSON> has 3 times more  blue balloons than <PERSON>. How many blue balloons does <PERSON> have now ?", "iIndex": 183, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.941}, {"lEquations": ["90171+16320=x"], "lSolutions": [106491.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Last year, 90,171 people were born in a country, and 16,320 people immigrated to it. How many new people began living in the country last year?", "iIndex": 246, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.894}, {"lEquations": ["140/7=x"], "lSolutions": [20.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> and the junior ranger brought a bag of 140 nails as they visited every station assigned to the junior ranger. If they left exactly 7 nails in every station they visited, how many stations did <PERSON><PERSON> and the junior ranger visit?", "iIndex": 414, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.919}, {"lEquations": ["3409-145=x"], "lSolutions": [3264.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were 3409 pieces of candy in a jar. If 145 pieces were red and the rest were blue, how many were blue?", "iIndex": 465, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.872}, {"lEquations": ["960=30*x"], "lSolutions": [32.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 960 students at a school. If each classroom holds 30 students, how many classrooms are needed at the school?", "iIndex": 141, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.968}, {"lEquations": ["(25*96)=x*12"], "lSolutions": [200.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> earns $25.00 for each house she cleans. If she cleans 96 houses, how many dozens of dollars will she make?", "iIndex": 115, "complexity_level": "L0", "dir_score": 0.15, "reasoning_steps": 1, "screened": true, "quality_score": 0.868}, {"lEquations": ["(30+(49+46))-58=x"], "lSolutions": [67.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> saved $30 in September. He saved $49 in October and $46 in November. Then <PERSON> spent $58 on a video game. How much money does <PERSON> have left?", "iIndex": 267, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.924}, {"lEquations": ["9.8 - 5.2 = x"], "lSolutions": [4.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 9.8 ounces of sugar, and she spilled 5.2 ounces of it on the floor. How much is left?", "iIndex": 380, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.865}, {"lEquations": ["35+x=83"], "lSolutions": [48.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> baked 35 muffins. How many more muffins does <PERSON> have to bake to have 83 muffins?", "iIndex": 435, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.966}, {"lEquations": ["0.16666666666666666 + 0.**************** = x"], "lSolutions": [0.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "In Shannon's apartment complex, 0.16666666666666666 of the apartments are one-bedroom apartments and 0.**************** are two-bedroom apartments. What fraction of the apartments are either one- or two-bedroom apartments?", "iIndex": 312, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 3, "screened": true, "quality_score": 0.853}, {"lEquations": ["3*x=36"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>’s sister, wanted to have 3 bracelets with star-shaped stones. She also bought 36 star-shaped stones from the local store and gave it to <PERSON>. How many star-shaped stones will there be in each of the bracelet <PERSON> makes for <PERSON>?", "iIndex": 426, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.857}, {"lEquations": ["223-95=x"], "lSolutions": [128.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 223 whistles. <PERSON> has 95 more whistles than <PERSON>. How many whistles does <PERSON> have?", "iIndex": 454, "complexity_level": "L1", "dir_score": 0.43, "reasoning_steps": 4, "screened": true, "quality_score": 0.948}, {"lEquations": ["0.2 + 0.4 = x"], "lSolutions": [0.6], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "It rained 0.2 inches on Saturday and 0.4 inches on Sunday. How much did it rain on Saturday and Sunday combined?", "iIndex": 379, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.876}, {"lEquations": ["700/7=x"], "lSolutions": [7.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There was a stack of 700 sheets of used paper. <PERSON> wants to place it in boxes for recycling. If every box can contain 100 sheets, how many boxes does <PERSON> need?", "iIndex": 408, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.962}, {"lEquations": ["12/3=x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "For dessert, <PERSON>’s mom prepared 12 pieces of bite-size cinnamon swirls. If the 3 of them ate an equal number of pieces of cinnamon swirls, how many pieces did <PERSON> eat?", "iIndex": 433, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.86}, {"lEquations": ["1*12=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Sally saw 1 dozen birds in a tree. How many birds did <PERSON> see ?", "iIndex": 178, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.867}, {"lEquations": ["10*22=x"], "lSolutions": [220.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 10 stickers on a page. If you have 22 pages of stickers, how many stickers do you have?", "iIndex": 437, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.896}, {"lEquations": ["5*250=x"], "lSolutions": [1250.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "The <PERSON><PERSON> family took a 5 day vacation by car. Each day they drove 250 miles. How many total miles did they drive?", "iIndex": 461, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.953}, {"lEquations": ["323/17=x"], "lSolutions": [19.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 323 football games in the season. The season is played for 17 months. How many football games were played each month, if each month has the same number of games?", "iIndex": 112, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 1, "screened": true, "quality_score": 0.947}, {"lEquations": ["((20+26)+20)-(6+58)=x"], "lSolutions": [2.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> had 20 stickers. She bought 26 stickers from a store in the mall and got 20 stickers for her birthday. Then <PERSON><PERSON> gave 6 of the stickers to her sister and used 58 to decorate a greeting card. How many stickers does <PERSON><PERSON> have left?", "iIndex": 259, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.96}, {"lEquations": ["7*4=x"], "lSolutions": [28.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 7 boxes of stuffed toy dogs. Each box has 4 dogs in it. How many dogs are there in all?", "iIndex": 494, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.916}, {"lEquations": ["2+3=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 2 pencils in the drawer. <PERSON> placed 3 more pencils in the drawer. How many pencils are now there in total ?", "iIndex": 23, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.935}, {"lEquations": ["(4+4)*6=x"], "lSolutions": [48.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> can read 4 books a day. <PERSON> reads every Monday and Tuesday. How many books would <PERSON> read in 6 weeks?", "iIndex": 474, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.954}, {"lEquations": ["33+44=x"], "lSolutions": [77.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 33 walnut trees currently in the park. Park workers will plant  44 more walnut trees today. How many walnut trees will the park have when the workers are finished ?", "iIndex": 39, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.903}, {"lEquations": ["9+3=x"], "lSolutions": [12.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 9 yellow marbles. <PERSON> has 3 yellow marbles. How many yellow marbles do they have in all ?", "iIndex": 275, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.942}, {"lEquations": ["0.**************** + 0.**************** + 0.08333333333333333 = x"], "lSolutions": [0.75], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> went to the county fair last weekend. When she got there, she had to walk 0.**************** of a mile from the car to the entrance. Then she walked 0.**************** of a mile to the carnival rides and 0.08333333333333333 of a mile from the carnival rides back to the car. How many miles did <PERSON> walk in all?", "iIndex": 344, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.888}, {"lEquations": ["0.4 + 0.1 = x"], "lSolutions": [0.3], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> and <PERSON> own neighboring cornfields. <PERSON> harvested 0.4 of an acre of corn on Monday and <PERSON> harvested 0.1 of an acre. How many more acres did <PERSON> harvest than <PERSON>?", "iIndex": 317, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 1, "screened": true, "quality_score": 0.947}, {"lEquations": ["8-4=x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 8 orange marbles, he gave <PERSON> 4 of the marbles. How many orange marbles does he now have ?", "iIndex": 125, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 2, "screened": true, "quality_score": 0.919}, {"lEquations": ["19+39+25=x"], "lSolutions": [83.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 19 dimes in her bank. Her dad gave her 39 dimes and her mother gave her 25 dimes. How many dimes does <PERSON> have now ?", "iIndex": 51, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.939}, {"lEquations": ["4*x=32"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>’s mom gathered all her watercolor paintings and thought of placing an equal number of paintings in 4 rooms in the house. If <PERSON> has 32 watercolor paintings, how many paintings will be placed in each room?", "iIndex": 409, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 2, "screened": true, "quality_score": 0.865}, {"lEquations": ["4*14=x"], "lSolutions": [56.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>, <PERSON>, <PERSON>, and <PERSON> each have 14 Pokemon cards.  How many Pokemon cards do they have in all ?", "iIndex": 163, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.98}, {"lEquations": ["2+(5*x)=27"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought a soft drink for 2 dollars and 5 candy bars. He spent a total of 27 dollars. How much did each candy bar cost ?", "iIndex": 8, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.873}, {"lEquations": ["0.4 - 0.3 = x"], "lSolutions": [0.1], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "While taking inventory at her pastry shop, <PERSON> realizes that she had 0.4 of a box of baking powder yesterday, but the supply is now down to 0.3 of a box. How much more baking powder did <PERSON> have yesterday?", "iIndex": 315, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.859}, {"lEquations": ["1250*3=x"], "lSolutions": [3750.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "On Friday, 1250 people visited the zoo. 3 times as many people visited on Saturday than on Friday. How many people visited the zoo on Saturday?", "iIndex": 466, "complexity_level": "L1", "dir_score": 0.41, "reasoning_steps": 2, "screened": true, "quality_score": 0.891}, {"lEquations": ["48097+684=x"], "lSolutions": [48781.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A farmer estimates that he will harvest 48,097 bushels of wheat. The weather is perfect during the growing season, so he harvests 684 more bushels of wheat than expected. How many bushels of wheat does the farmer harvest?", "iIndex": 241, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.967}, {"lEquations": ["380-57=x"], "lSolutions": [323.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had 380 legos, but <PERSON> lost 57 of them. How many legos does <PERSON> have now?", "iIndex": 434, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.915}, {"lEquations": ["(3*12)*4=x"], "lSolutions": [144.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 3 dozen red marbles. <PERSON> has 4 times more red marbles than <PERSON>. How many red marbles does <PERSON> have ?", "iIndex": 176, "complexity_level": "L0", "dir_score": 0.05, "reasoning_steps": 2, "screened": true, "quality_score": 0.855}, {"lEquations": ["12/3=x"], "lSolutions": [4.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> has 12 guests coming to her Halloween party. Each table will hold 3 guests. How many tables will <PERSON><PERSON> need?", "iIndex": 284, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.931}, {"lEquations": ["20-(8+3)=x"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> had $20. <PERSON> paid $8 for a ticket to a baseball game. At the game, <PERSON> bought a hot dog for $3. What amount of money did <PERSON> have then?", "iIndex": 459, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.864}, {"lEquations": ["110+102=x"], "lSolutions": [212.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 110 books. <PERSON> has 102 books.  How many books do they have together ?", "iIndex": 90, "complexity_level": "L0", "dir_score": 0.07, "reasoning_steps": 2, "screened": true, "quality_score": 0.865}, {"lEquations": ["39-14=x"], "lSolutions": [25.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON>'s high school played 39 baseball games this year. He attended  14 games. How many baseball games did <PERSON> miss ?", "iIndex": 77, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 1, "screened": true, "quality_score": 0.922}, {"lEquations": ["4*x=36"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A large bag of balls was kept under <PERSON>’s bed. Her mom placed the balls in bags for children in foster homes. If every bag can contain 4 balls and <PERSON> has 36 balls, how many bags will be used?", "iIndex": 422, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.918}, {"lEquations": ["23*45=x"], "lSolutions": [1035.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Each bag contains 23 pounds of oranges. How many pounds of oranges are in 45 bags?", "iIndex": 496, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.922}, {"lEquations": ["9792=3513+x"], "lSolutions": [6279.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "So far, an orchard has sold a combined total of 9,792 pounds of fresh and frozen fruit this season. If they have sold 3,513 pounds of frozen fruit, how many pounds of fresh fruit have been sold so far?", "iIndex": 254, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.906}, {"lEquations": ["0.32 + 0.21 = x"], "lSolutions": [0.53], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "It snowed 0.32 inches on Monday and 0.21 inches on Tuesday. How much did it snow on Monday and Tuesday combined?", "iIndex": 376, "complexity_level": "L0", "dir_score": 0.11, "reasoning_steps": 1, "screened": true, "quality_score": 0.872}, {"lEquations": ["115+100=x"], "lSolutions": [215.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 115 pencils in the drawer. <PERSON> placed 100 more  pencils in the drawer. How many pencils are now there in all ?", "iIndex": 98, "complexity_level": "L0", "dir_score": 0.08, "reasoning_steps": 1, "screened": true, "quality_score": 0.902}, {"lEquations": ["5+6+3=x"], "lSolutions": [14.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A restaurant served 5 cakes during lunch and 6 during dinner today. The restaurant served 3 cakes yesterday. How many cakes were served in total ?", "iIndex": 31, "complexity_level": "L0", "dir_score": 0.06, "reasoning_steps": 2, "screened": true, "quality_score": 0.966}, {"lEquations": ["(4*12)*42=x"], "lSolutions": [2016.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 4 dozen calories in a candy bar. How many calories are there in 42 candy bars ?", "iIndex": 174, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 2, "screened": true, "quality_score": 0.932}, {"lEquations": ["(20*6)-(2*1)=x"], "lSolutions": [118.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 20 cartons of ice cream and 2 cartons of yogurt. Each carton of ice cream cost $6 and each carton of yogurt cost $1. How much more did <PERSON> spend on ice cream than on yogurt?", "iIndex": 268, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.852}, {"lEquations": ["0.1 + 0.4 = x"], "lSolutions": [0.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> bought 0.1 pounds of peanuts and 0.4 pounds of raisins. How many pounds of snacks did she buy in all?", "iIndex": 370, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.89}, {"lEquations": ["6*x=54"], "lSolutions": [9.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON><PERSON> has 6 neighbors who like to collect animal drawings. <PERSON><PERSON> drew 54 animals on small pieces of paper. If she plans to give the same number of animal drawings to her neighbors, how many will each of them receive?", "iIndex": 416, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.89}, {"lEquations": ["344/43=x"], "lSolutions": [8.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "A teacher has 344 pieces of candy. If there are 43 students, and the candy is divided evenly, How many pieces will each student get?", "iIndex": 114, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 1, "screened": true, "quality_score": 0.909}, {"lEquations": ["13*14=x"], "lSolutions": [182.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There were a total of 13 hockey games a month. The season is played for  14 months. How many hockey games are in the seasons ?", "iIndex": 166, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.854}, {"lEquations": ["5/2=x"], "lSolutions": [2.5], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> is baking bread. She needs 5 cups of flour to bake 2 loaves of bread. How much flour will she need to make one loaf of bread?", "iIndex": 484, "complexity_level": "L1", "dir_score": 0.4, "reasoning_steps": 3, "screened": true, "quality_score": 0.967}, {"lEquations": ["14507+213=x"], "lSolutions": [14720.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Last year at Newberg's airport, 14,507 passengers landed on time. Unfortunately, 213 passengers landed late. In all, how many passengers landed in Newberg last year?", "iIndex": 243, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 1, "screened": true, "quality_score": 0.928}, {"lEquations": ["1986+5106=x"], "lSolutions": [7092.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 1,986 books in Oak Grove's public library. In addition, there are 5,106 books in its school libraries. How many books do the libraries in Oak Grove have overall?", "iIndex": 239, "complexity_level": "L0", "dir_score": 0.12, "reasoning_steps": 2, "screened": true, "quality_score": 0.883}, {"lEquations": ["2315+1028=x"], "lSolutions": [3343.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "<PERSON> has 2315 red markers and 1028 blue markers. How many markers does <PERSON> have altogether?", "iIndex": 451, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.916}, {"lEquations": ["33-18=x"], "lSolutions": [15.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "There are 33 oak trees currently in the park. Park workers had to cut down  18 oak trees that were damaged. How many oak trees will be in the park when the workers are finished ?", "iIndex": 204, "complexity_level": "L1", "dir_score": 0.36, "reasoning_steps": 3, "screened": true, "quality_score": 0.907}, {"lEquations": ["(12*0.25)+(7*0.01)=x"], "lSolutions": [3.07], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "As <PERSON><PERSON> was searching through her couch cushions, she found 12 quarters, and 7 pennies in the couch. How much money in total does <PERSON><PERSON> have?", "iIndex": 208, "complexity_level": "L0", "dir_score": 0.09, "reasoning_steps": 2, "screened": true, "quality_score": 0.87}, {"lEquations": ["40/7=x"], "lSolutions": [5.0], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "Mrs. <PERSON><PERSON> has 40 markers. They are divided equally into 7 packages. Mrs. <PERSON><PERSON> wants to know how many markers are in each package?", "iIndex": 482, "complexity_level": "L0", "dir_score": 0.13, "reasoning_steps": 2, "screened": true, "quality_score": 0.903}, {"lEquations": ["0.5 + 0.125 = x"], "lSolutions": [0.625], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "0.5 of the students in the band are in the trumpet section. 0.125 of the students in the band are in the trombone section. What fraction of the students in the band are in either the trumpet section or the trombone section?", "iIndex": 306, "complexity_level": "L0", "dir_score": 0.1, "reasoning_steps": 2, "screened": true, "quality_score": 0.915}, {"lEquations": ["14*(0.25+0.5+0.1)=x"], "lSolutions": [11.9], "grammarCheck": 0, "templateNumber": 0, "sQuestion": "When <PERSON> was visited by the toothfairy, she received 14 each of quarters, half-dollars, and dimes. How much money did the toothfairy leave <PERSON>?", "iIndex": 207, "complexity_level": "L0", "dir_score": 0.14, "reasoning_steps": 1, "screened": true, "quality_score": 0.924}]